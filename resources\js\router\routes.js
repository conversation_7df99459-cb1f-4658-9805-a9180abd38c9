// Import layouts
import AdminLayout from '../layouts/AdminLayout.vue'
import GuestLayout from '../layouts/GuestLayout.vue'
import VisitorLayout from '../layouts/VisitorLayout.vue'

// Import pages
import Login from '../pages/auth/Login.vue'
import Dashboard from '../pages/admin/Dashboard.vue'
import AgentWorkspace from '../pages/admin/AgentWorkspace.vue'
import Analytics from '../pages/admin/Analytics.vue'
import SystemConfig from '../pages/admin/SystemConfig.vue'
import UserManagement from '../pages/admin/UserManagement.vue'
import ChatHistory from '../pages/admin/ChatHistory.vue'
import VisitorTracking from '../pages/admin/VisitorTracking.vue'
import ThemeCustomization from '../pages/admin/ThemeCustomization.vue'
import LanguageSettings from '../pages/admin/LanguageSettings.vue'

// Visitor pages
import Home from '../pages/visitor/Home.vue'
import Contact from '../pages/visitor/Contact.vue'
import ChatPage from '../pages/visitor/ChatPage.vue'

const routes = [
  // Guest routes
  {
    path: '/login',
    component: GuestLayout,
    meta: { requiresGuest: true },
    children: [
      {
        path: '',
        name: 'login',
        component: Login,
        meta: { title: 'Login' }
      }
    ]
  },

  // Admin routes
  {
    path: '/admin',
    component: AdminLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/admin/dashboard'
      },
      {
        path: 'dashboard',
        name: 'admin.dashboard',
        component: Dashboard,
        meta: { 
          title: 'Dashboard',
          roles: ['admin', 'supervisor', 'agent']
        }
      },
      {
        path: 'workspace',
        name: 'admin.workspace',
        component: AgentWorkspace,
        meta: { 
          title: 'Agent Workspace',
          roles: ['agent', 'supervisor', 'admin']
        }
      },
      {
        path: 'analytics',
        name: 'admin.analytics',
        component: Analytics,
        meta: { 
          title: 'Analytics',
          roles: ['admin', 'supervisor']
        }
      },
      {
        path: 'config',
        name: 'admin.config',
        component: SystemConfig,
        meta: { 
          title: 'System Configuration',
          roles: ['admin']
        }
      },
      {
        path: 'users',
        name: 'admin.users',
        component: UserManagement,
        meta: { 
          title: 'User Management',
          roles: ['admin', 'supervisor']
        }
      },
      {
        path: 'chat-history',
        name: 'admin.chat-history',
        component: ChatHistory,
        meta: { 
          title: 'Chat History',
          roles: ['admin', 'supervisor', 'agent']
        }
      },
      {
        path: 'visitor-tracking',
        name: 'admin.visitor-tracking',
        component: VisitorTracking,
        meta: { 
          title: 'Visitor Tracking',
          roles: ['admin', 'supervisor']
        }
      },
      {
        path: 'themes',
        name: 'admin.themes',
        component: ThemeCustomization,
        meta: { 
          title: 'Theme Customization',
          roles: ['admin']
        }
      },
      {
        path: 'languages',
        name: 'admin.languages',
        component: LanguageSettings,
        meta: { 
          title: 'Language Settings',
          roles: ['admin']
        }
      }
    ]
  },

  // Visitor routes
  {
    path: '/',
    component: VisitorLayout,
    children: [
      {
        path: '',
        name: 'home',
        component: Home,
        meta: { title: 'Home' }
      },
      {
        path: 'contact',
        name: 'contact',
        component: Contact,
        meta: { title: 'Contact Us' }
      },
      {
        path: 'chat',
        name: 'chat',
        component: ChatPage,
        meta: { title: 'Chat Support' }
      }
    ]
  },

  // Catch all route
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('../pages/errors/NotFound.vue'),
    meta: { title: 'Page Not Found' }
  }
]

export default routes

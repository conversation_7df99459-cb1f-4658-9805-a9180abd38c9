<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWebhooksTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('webhooks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('site_id')->constrained()->onDelete('cascade');
            
            $table->string('name');
            $table->text('url');
            $table->enum('method', ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'])->default('POST');
            $table->json('headers')->nullable();
            $table->json('events'); // Array of events to listen for
            $table->boolean('is_active')->default(true);
            $table->string('secret')->nullable(); // For webhook verification
            
            // Retry settings
            $table->integer('max_retries')->default(3);
            $table->integer('retry_delay')->default(60); // seconds
            $table->timestamp('last_triggered_at')->nullable();
            $table->integer('success_count')->default(0);
            $table->integer('failure_count')->default(0);
            
            $table->timestamps();
            
            $table->index(['site_id', 'is_active']);
            $table->index('events');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('webhooks');
    }
}

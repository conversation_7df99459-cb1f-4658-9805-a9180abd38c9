<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\Site;
use App\Models\Visitor;
use App\Services\ChatService;
use App\Services\PermissionService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ChatController extends Controller
{
    protected $chatService;
    protected $permissionService;

    public function __construct(ChatService $chatService, PermissionService $permissionService)
    {
        $this->chatService = $chatService;
        $this->permissionService = $permissionService;
        $this->middleware('auth');
    }

    /**
     * Get chat sessions for agent
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $siteId = $request->input('site_id');
        
        if ($siteId && !$user->canAccessSite($siteId)) {
            return response()->json(['error' => 'Access denied to this site'], 403);
        }

        $query = ChatSession::with(['visitor', 'user', 'latestMessage'])
            ->when($siteId, function ($q) use ($siteId) {
                return $q->where('site_id', $siteId);
            });

        // Filter based on user role
        if ($user->role === 'agent') {
            $query->where('user_id', $user->id);
        } elseif ($user->role === 'supervisor') {
            // Supervisors can see all sessions for their sites
            $siteIds = $user->sites()->pluck('site_id');
            $query->whereIn('site_id', $siteIds);
        }

        $status = $request->input('status', 'active');
        if ($status === 'active') {
            $query->whereIn('status', ['waiting', 'active']);
        } else {
            $query->where('status', $status);
        }

        $sessions = $query->orderBy('updated_at', 'desc')
            ->paginate($request->input('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $sessions->items(),
            'pagination' => [
                'current_page' => $sessions->currentPage(),
                'last_page' => $sessions->lastPage(),
                'per_page' => $sessions->perPage(),
                'total' => $sessions->total(),
            ]
        ]);
    }

    /**
     * Get specific chat session with messages
     */
    public function show(Request $request, $sessionId)
    {
        $user = Auth::user();
        
        try {
            $data = $this->chatService->getChatSessionWithMessages(
                $sessionId,
                $request->input('limit', 50),
                $request->input('offset', 0)
            );

            $session = $data['session'];
            
            // Check access permissions
            if (!$this->canAccessSession($user, $session)) {
                return response()->json(['error' => 'Access denied to this session'], 403);
            }

            // Mark messages as read for the current user
            if ($user->role === 'agent' && $session->user_id === $user->id) {
                $this->chatService->markMessagesAsRead($session, $user, 'agent');
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'session' => $session,
                    'messages' => $data['messages']->map(function ($message) {
                        return $message->toApiArray();
                    }),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Session not found'
            ], 404);
        }
    }

    /**
     * Send message to chat session
     */
    public function sendMessage(Request $request, $sessionId)
    {
        $validator = Validator::make($request->all(), [
            'content' => 'required|string|max:5000',
            'type' => 'sometimes|string|in:text,system',
            'parent_message_id' => 'sometimes|exists:chat_messages,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        try {
            $session = ChatSession::where('session_id', $sessionId)->firstOrFail();
            
            if (!$this->canAccessSession($user, $session)) {
                return response()->json(['error' => 'Access denied to this session'], 403);
            }

            if (!$this->permissionService->hasPermission($user, 'send_messages', $session->site_id)) {
                return response()->json(['error' => 'No permission to send messages'], 403);
            }

            $message = $this->chatService->addMessage(
                $session,
                $request->input('content'),
                'agent',
                $user,
                [
                    'type' => $request->input('type', 'text'),
                    'parent_message_id' => $request->input('parent_message_id'),
                ]
            );

            return response()->json([
                'success' => true,
                'data' => $message->toApiArray()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to send message'
            ], 500);
        }
    }

    /**
     * Upload file to chat session
     */
    public function uploadFile(Request $request, $sessionId)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:10240', // 10MB max
            'content' => 'sometimes|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        try {
            $session = ChatSession::where('session_id', $sessionId)->firstOrFail();
            
            if (!$this->canAccessSession($user, $session)) {
                return response()->json(['error' => 'Access denied to this session'], 403);
            }

            if (!$this->permissionService->hasPermission($user, 'upload_files', $session->site_id)) {
                return response()->json(['error' => 'No permission to upload files'], 403);
            }

            $message = $this->chatService->uploadFileMessage(
                $session,
                $request->file('file'),
                'agent',
                $user,
                $request->input('content')
            );

            return response()->json([
                'success' => true,
                'data' => $message->toApiArray()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign session to agent
     */
    public function assignAgent(Request $request, $sessionId)
    {
        $validator = Validator::make($request->all(), [
            'agent_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        try {
            $session = ChatSession::where('session_id', $sessionId)->firstOrFail();
            
            if (!$this->canAccessSession($user, $session)) {
                return response()->json(['error' => 'Access denied to this session'], 403);
            }

            if (!$this->permissionService->hasPermission($user, 'assign_sessions', $session->site_id)) {
                return response()->json(['error' => 'No permission to assign sessions'], 403);
            }

            $agent = User::findOrFail($request->input('agent_id'));
            
            if (!$agent->canAccessSite($session->site_id)) {
                return response()->json(['error' => 'Agent does not have access to this site'], 403);
            }

            $this->chatService->assignAgent($session, $agent);

            return response()->json([
                'success' => true,
                'message' => 'Session assigned successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Transfer session to another agent
     */
    public function transferSession(Request $request, $sessionId)
    {
        $validator = Validator::make($request->all(), [
            'agent_id' => 'required|exists:users,id',
            'reason' => 'sometimes|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        try {
            $session = ChatSession::where('session_id', $sessionId)->firstOrFail();
            
            if (!$this->canAccessSession($user, $session)) {
                return response()->json(['error' => 'Access denied to this session'], 403);
            }

            if (!$this->permissionService->hasPermission($user, 'transfer_sessions', $session->site_id)) {
                return response()->json(['error' => 'No permission to transfer sessions'], 403);
            }

            $newAgent = User::findOrFail($request->input('agent_id'));
            
            if (!$newAgent->canAccessSite($session->site_id)) {
                return response()->json(['error' => 'Target agent does not have access to this site'], 403);
            }

            $this->chatService->transferSession(
                $session,
                $newAgent,
                $request->input('reason')
            );

            return response()->json([
                'success' => true,
                'message' => 'Session transferred successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Close chat session
     */
    public function closeSession(Request $request, $sessionId)
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'sometimes|string|max:500',
            'rating' => 'sometimes|integer|min:1|max:5',
            'feedback' => 'sometimes|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        try {
            $session = ChatSession::where('session_id', $sessionId)->firstOrFail();
            
            if (!$this->canAccessSession($user, $session)) {
                return response()->json(['error' => 'Access denied to this session'], 403);
            }

            if (!$this->permissionService->hasPermission($user, 'close_sessions', $session->site_id)) {
                return response()->json(['error' => 'No permission to close sessions'], 403);
            }

            $this->chatService->closeSession(
                $session,
                $request->input('reason'),
                $request->input('rating'),
                $request->input('feedback')
            );

            return response()->json([
                'success' => true,
                'message' => 'Session closed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send typing notification
     */
    public function sendTyping(Request $request, $sessionId)
    {
        $validator = Validator::make($request->all(), [
            'is_typing' => 'required|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        try {
            $session = ChatSession::where('session_id', $sessionId)->firstOrFail();
            
            if (!$this->canAccessSession($user, $session)) {
                return response()->json(['error' => 'Access denied to this session'], 403);
            }

            $this->chatService->sendTypingNotification(
                $session,
                $user,
                'agent',
                $request->boolean('is_typing')
            );

            return response()->json([
                'success' => true,
                'message' => 'Typing notification sent'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to send typing notification'
            ], 500);
        }
    }

    /**
     * Check if user can access session
     */
    protected function canAccessSession($user, $session)
    {
        // Admin can access all sessions
        if ($user->role === 'admin') {
            return true;
        }

        // Check site access
        if (!$user->canAccessSite($session->site_id)) {
            return false;
        }

        // Agent can only access their own sessions
        if ($user->role === 'agent') {
            return $session->user_id === $user->id || $session->status === 'waiting';
        }

        // Supervisor can access all sessions in their sites
        return true;
    }
}

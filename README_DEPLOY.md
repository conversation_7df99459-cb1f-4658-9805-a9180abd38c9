# 🚀 快速部署指南

## 一键部署步骤

### 1. 环境检查
```
双击运行: check_env.bat
```
这会检查您的环境是否满足部署要求。

### 2. 基础部署
```
右键 deploy.bat → 以管理员身份运行
```
这会自动完成：
- 安装PHP依赖
- 配置Laravel
- 设置数据库连接
- 运行数据库迁移

### 3. WebSocket功能（可选）
```
双击运行: setup_websocket.bat
```
这会添加实时聊天功能。

## 宝塔面板配置

### 创建网站
1. 登录宝塔面板
2. 网站 → 添加站点
3. 域名: `aichat.jagship.com`
4. 根目录: `D:\wwwroot\aichat.jagship.com\public`
5. PHP版本: 7.2
6. 创建数据库: 是

### 配置PHP扩展
1. 软件商店 → 已安装 → PHP 7.2 → 设置
2. 安装扩展: `redis`, `opcache`, `fileinfo`, `zip`, `curl`, `gd`
3. 禁用函数: 删除 `putenv`, `getenv`
4. 重载配置

### 配置Nginx
在网站设置 → 配置文件中添加：
```nginx
location /ws {
    proxy_pass http://127.0.0.1:8080;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
}
```

## 启动服务

### WebSocket服务
```
双击运行: start_websocket.bat
```

### 队列处理
```
双击运行: start_queue.bat
```

## 测试访问

1. 配置hosts文件 (C:\Windows\System32\drivers\etc\hosts):
   ```
   127.0.0.1 aichat.jagship.com
   ```

2. 浏览器访问: http://aichat.jagship.com

## 常见问题

### Q: deploy.bat 一闪而过
A: 右键选择"以管理员身份运行"

### Q: 提示找不到PHP
A: 检查宝塔面板是否正确安装PHP 7.2

### Q: Composer安装失败
A: 脚本会自动下载composer.phar到项目目录

### Q: 数据库连接失败
A: 检查宝塔面板中MySQL服务是否启动，数据库信息是否正确

### Q: WebSocket连接失败
A: 
1. 检查防火墙是否开放8080端口
2. 确保start_websocket.bat正在运行
3. 检查Nginx配置中的WebSocket代理

## 文件说明

- `deploy.bat` - 主部署脚本
- `check_env.bat` - 环境检查工具
- `setup_websocket.bat` - WebSocket功能配置
- `start_websocket.bat` - WebSocket服务启动器（部署后生成）
- `start_queue.bat` - 队列处理启动器（部署后生成）

## 技术支持

如果遇到问题：
1. 先运行 `check_env.bat` 检查环境
2. 查看错误信息截图
3. 检查宝塔面板中的服务状态

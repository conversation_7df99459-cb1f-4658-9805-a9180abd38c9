<?php

namespace App\Services;

use App\Models\VisitorTracking;
use App\Models\MouseTracking;
use App\Models\ClickEvent;
use App\Models\FormEvent;
use App\Models\Site;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AnalyticsService
{
    /**
     * Get visitor behavior overview for a site
     */
    public function getVisitorOverview($siteId, $startDate = null, $endDate = null)
    {
        $cacheKey = "analytics_overview_{$siteId}_" . md5($startDate . $endDate);
        
        return Cache::remember($cacheKey, 300, function () use ($siteId, $startDate, $endDate) {
            $query = VisitorTracking::where('site_id', $siteId);
            
            if ($startDate && $endDate) {
                $query->whereBetween('visited_at', [$startDate, $endDate]);
            }
            
            $totalVisits = $query->count();
            $uniqueVisitors = $query->distinct('visitor_id')->count();
            $bounceRate = $query->bounced()->count();
            $avgDuration = $query->avg('visit_duration');
            $avgPageViews = $query->avg('page_views');
            $totalPageViews = $query->sum('page_views');
            
            return [
                'total_visits' => $totalVisits,
                'unique_visitors' => $uniqueVisitors,
                'bounce_rate' => $totalVisits > 0 ? round(($bounceRate / $totalVisits) * 100, 2) : 0,
                'avg_duration' => round($avgDuration ?? 0, 2),
                'avg_page_views' => round($avgPageViews ?? 0, 2),
                'total_page_views' => $totalPageViews,
                'pages_per_session' => $totalVisits > 0 ? round($totalPageViews / $totalVisits, 2) : 0,
            ];
        });
    }

    /**
     * Get device and browser statistics
     */
    public function getDeviceStats($siteId, $startDate = null, $endDate = null)
    {
        $cacheKey = "device_stats_{$siteId}_" . md5($startDate . $endDate);
        
        return Cache::remember($cacheKey, 300, function () use ($siteId, $startDate, $endDate) {
            $query = VisitorTracking::where('site_id', $siteId);
            
            if ($startDate && $endDate) {
                $query->whereBetween('visited_at', [$startDate, $endDate]);
            }
            
            // Device types
            $deviceTypes = $query->selectRaw('
                    JSON_UNQUOTE(JSON_EXTRACT(device_info, "$.type")) as device_type,
                    COUNT(*) as count
                ')
                ->groupBy('device_type')
                ->orderBy('count', 'desc')
                ->get();
            
            // Browsers
            $browsers = $query->selectRaw('
                    JSON_UNQUOTE(JSON_EXTRACT(browser_info, "$.name")) as browser_name,
                    COUNT(*) as count
                ')
                ->groupBy('browser_name')
                ->orderBy('count', 'desc')
                ->get();
            
            // Operating systems
            $operatingSystems = $query->selectRaw('
                    JSON_UNQUOTE(JSON_EXTRACT(browser_info, "$.os")) as os_name,
                    COUNT(*) as count
                ')
                ->groupBy('os_name')
                ->orderBy('count', 'desc')
                ->get();
            
            // Screen resolutions
            $screenResolutions = $query->selectRaw('
                    CONCAT(
                        JSON_UNQUOTE(JSON_EXTRACT(screen_resolution, "$.width")),
                        "x",
                        JSON_UNQUOTE(JSON_EXTRACT(screen_resolution, "$.height"))
                    ) as resolution,
                    COUNT(*) as count
                ')
                ->groupBy('resolution')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get();
            
            return [
                'device_types' => $deviceTypes,
                'browsers' => $browsers,
                'operating_systems' => $operatingSystems,
                'screen_resolutions' => $screenResolutions,
            ];
        });
    }

    /**
     * Get geographic statistics
     */
    public function getGeographicStats($siteId, $startDate = null, $endDate = null)
    {
        $cacheKey = "geographic_stats_{$siteId}_" . md5($startDate . $endDate);
        
        return Cache::remember($cacheKey, 300, function () use ($siteId, $startDate, $endDate) {
            $query = VisitorTracking::where('site_id', $siteId);
            
            if ($startDate && $endDate) {
                $query->whereBetween('visited_at', [$startDate, $endDate]);
            }
            
            // Countries
            $countries = $query->selectRaw('country, COUNT(*) as count')
                ->whereNotNull('country')
                ->groupBy('country')
                ->orderBy('count', 'desc')
                ->limit(20)
                ->get();
            
            // Cities
            $cities = $query->selectRaw('city, country, COUNT(*) as count')
                ->whereNotNull('city')
                ->groupBy(['city', 'country'])
                ->orderBy('count', 'desc')
                ->limit(20)
                ->get();
            
            // Languages
            $languages = $query->selectRaw('language, COUNT(*) as count')
                ->whereNotNull('language')
                ->groupBy('language')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get();
            
            return [
                'countries' => $countries,
                'cities' => $cities,
                'languages' => $languages,
            ];
        });
    }

    /**
     * Get page performance statistics
     */
    public function getPageStats($siteId, $startDate = null, $endDate = null)
    {
        $cacheKey = "page_stats_{$siteId}_" . md5($startDate . $endDate);
        
        return Cache::remember($cacheKey, 300, function () use ($siteId, $startDate, $endDate) {
            $query = VisitorTracking::where('site_id', $siteId);
            
            if ($startDate && $endDate) {
                $query->whereBetween('visited_at', [$startDate, $endDate]);
            }
            
            // Most visited pages
            $topPages = $query->selectRaw('
                    page_url,
                    page_title,
                    COUNT(*) as visits,
                    AVG(visit_duration) as avg_duration,
                    AVG(scroll_depth) as avg_scroll_depth,
                    SUM(CASE WHEN bounce = 1 THEN 1 ELSE 0 END) as bounces
                ')
                ->groupBy(['page_url', 'page_title'])
                ->orderBy('visits', 'desc')
                ->limit(20)
                ->get()
                ->map(function ($page) {
                    $page->bounce_rate = $page->visits > 0 ? round(($page->bounces / $page->visits) * 100, 2) : 0;
                    $page->avg_duration = round($page->avg_duration ?? 0, 2);
                    $page->avg_scroll_depth = round($page->avg_scroll_depth ?? 0, 2);
                    return $page;
                });
            
            // Entry pages
            $entryPages = $query->selectRaw('
                    page_url,
                    page_title,
                    COUNT(*) as entries
                ')
                ->where('page_views', 1) // First page of the session
                ->groupBy(['page_url', 'page_title'])
                ->orderBy('entries', 'desc')
                ->limit(10)
                ->get();
            
            // Exit pages
            $exitPages = $query->selectRaw('
                    exit_page,
                    COUNT(*) as exits
                ')
                ->whereNotNull('exit_page')
                ->groupBy('exit_page')
                ->orderBy('exits', 'desc')
                ->limit(10)
                ->get();
            
            return [
                'top_pages' => $topPages,
                'entry_pages' => $entryPages,
                'exit_pages' => $exitPages,
            ];
        });
    }

    /**
     * Get click heatmap data for a specific page
     */
    public function getClickHeatmap($siteId, $pageUrl, $startDate = null, $endDate = null)
    {
        $cacheKey = "click_heatmap_{$siteId}_" . md5($pageUrl . $startDate . $endDate);
        
        return Cache::remember($cacheKey, 600, function () use ($siteId, $pageUrl, $startDate, $endDate) {
            return ClickEvent::getClickHeatmapForPage($siteId, $pageUrl, $startDate, $endDate);
        });
    }

    /**
     * Get mouse movement heatmap for a specific page
     */
    public function getMouseHeatmap($siteId, $pageUrl, $startDate = null, $endDate = null)
    {
        $cacheKey = "mouse_heatmap_{$siteId}_" . md5($pageUrl . $startDate . $endDate);
        
        return Cache::remember($cacheKey, 600, function () use ($siteId, $pageUrl, $startDate, $endDate) {
            $query = MouseTracking::where('site_id', $siteId)
                ->where('page_url', $pageUrl);
            
            if ($startDate && $endDate) {
                $query->whereBetween('recorded_at', [$startDate, $endDate]);
            }
            
            $mouseTrackings = $query->get();
            $combinedHeatmap = [];
            
            foreach ($mouseTrackings as $tracking) {
                if (empty($tracking->heatmap_data)) {
                    $tracking->generateHeatmapData();
                }
                
                if (!empty($tracking->heatmap_data)) {
                    foreach ($tracking->heatmap_data as $y => $row) {
                        foreach ($row as $x => $value) {
                            $combinedHeatmap[$y][$x] = ($combinedHeatmap[$y][$x] ?? 0) + $value;
                        }
                    }
                }
            }
            
            return $combinedHeatmap;
        });
    }

    /**
     * Get form analytics for a specific form
     */
    public function getFormAnalytics($siteId, $formId, $startDate = null, $endDate = null)
    {
        $cacheKey = "form_analytics_{$siteId}_{$formId}_" . md5($startDate . $endDate);
        
        return Cache::remember($cacheKey, 300, function () use ($siteId, $formId, $startDate, $endDate) {
            $completionRate = FormEvent::getFieldCompletionRate($siteId, $formId, $startDate, $endDate);
            $abandonmentRate = FormEvent::getFormAbandonmentRate($siteId, $formId, $startDate, $endDate);
            $problematicFields = FormEvent::getProblematicFields($siteId, $formId, 10, $startDate, $endDate);
            
            $query = FormEvent::where('site_id', $siteId)
                ->where('form_id', $formId);
            
            if ($startDate && $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }
            
            $avgTimeSpent = $query->avg('time_spent');
            $totalInteractions = $query->count();
            $errorCount = $query->withErrors()->count();
            
            return [
                'completion_rate' => $completionRate,
                'abandonment_rate' => $abandonmentRate,
                'avg_time_spent' => round($avgTimeSpent ?? 0, 2),
                'total_interactions' => $totalInteractions,
                'error_count' => $errorCount,
                'error_rate' => $totalInteractions > 0 ? round(($errorCount / $totalInteractions) * 100, 2) : 0,
                'problematic_fields' => $problematicFields,
            ];
        });
    }

    /**
     * Get real-time visitor activity
     */
    public function getRealTimeActivity($siteId, $minutes = 30)
    {
        $startTime = now()->subMinutes($minutes);
        
        $activeVisitors = VisitorTracking::where('site_id', $siteId)
            ->where('visited_at', '>=', $startTime)
            ->whereNull('left_at')
            ->with('visitor')
            ->get();
        
        $recentClicks = ClickEvent::where('site_id', $siteId)
            ->where('created_at', '>=', $startTime)
            ->orderBy('created_at', 'desc')
            ->limit(50)
            ->get();
        
        $recentFormEvents = FormEvent::where('site_id', $siteId)
            ->where('created_at', '>=', $startTime)
            ->orderBy('created_at', 'desc')
            ->limit(20)
            ->get();
        
        return [
            'active_visitors' => $activeVisitors->count(),
            'visitor_details' => $activeVisitors->map(function ($tracking) {
                return [
                    'visitor_id' => $tracking->visitor_id,
                    'page_url' => $tracking->page_url,
                    'page_title' => $tracking->page_title,
                    'country' => $tracking->country,
                    'city' => $tracking->city,
                    'device_type' => $tracking->device_type,
                    'browser_name' => $tracking->browser_name,
                    'visited_at' => $tracking->visited_at->toISOString(),
                    'duration' => $tracking->visited_at->diffInSeconds(now()),
                ];
            }),
            'recent_clicks' => $recentClicks->map->toApiArray(),
            'recent_form_events' => $recentFormEvents->map->toApiArray(),
        ];
    }

    /**
     * Get engagement metrics
     */
    public function getEngagementMetrics($siteId, $startDate = null, $endDate = null)
    {
        $cacheKey = "engagement_metrics_{$siteId}_" . md5($startDate . $endDate);
        
        return Cache::remember($cacheKey, 300, function () use ($siteId, $startDate, $endDate) {
            $query = VisitorTracking::where('site_id', $siteId);
            
            if ($startDate && $endDate) {
                $query->whereBetween('visited_at', [$startDate, $endDate]);
            }
            
            $totalVisits = $query->count();
            $engagedVisits = $query->engaged()->count();
            $avgEngagementScore = $query->selectRaw('
                AVG(
                    LEAST(40, (visit_duration / 300) * 40) +
                    LEAST(20, page_views * 5) +
                    (scroll_depth / 100) * 20 +
                    LEAST(20, (clicks * 2) + (form_interactions * 5))
                ) as avg_score
            ')->first()->avg_score ?? 0;
            
            return [
                'total_visits' => $totalVisits,
                'engaged_visits' => $engagedVisits,
                'engagement_rate' => $totalVisits > 0 ? round(($engagedVisits / $totalVisits) * 100, 2) : 0,
                'avg_engagement_score' => round($avgEngagementScore, 2),
            ];
        });
    }

    /**
     * Get visitor journey analysis
     */
    public function getVisitorJourney($siteId, $visitorId, $sessionId = null)
    {
        $query = VisitorTracking::where('site_id', $siteId)
            ->where('visitor_id', $visitorId);

        if ($sessionId) {
            $query->where('session_id', $sessionId);
        }

        $visits = $query->orderBy('visited_at')->get();

        $journey = [];
        foreach ($visits as $visit) {
            $clickEvents = ClickEvent::where('visitor_tracking_id', $visit->id)
                ->orderBy('created_at')
                ->get();

            $formEvents = FormEvent::where('visitor_tracking_id', $visit->id)
                ->orderBy('created_at')
                ->get();

            $journey[] = [
                'visit' => $visit->toApiArray(),
                'clicks' => $clickEvents->map->toApiArray(),
                'form_events' => $formEvents->map->toApiArray(),
            ];
        }

        return $journey;
    }

    /**
     * Get conversion funnel analysis
     */
    public function getConversionFunnel($siteId, $funnelSteps, $startDate = null, $endDate = null)
    {
        $query = VisitorTracking::where('site_id', $siteId);

        if ($startDate && $endDate) {
            $query->whereBetween('visited_at', [$startDate, $endDate]);
        }

        $totalVisitors = $query->distinct('visitor_id')->count();
        $funnelData = [];

        foreach ($funnelSteps as $index => $step) {
            $stepQuery = clone $query;

            if (isset($step['page_url'])) {
                $stepQuery->where('page_url', 'like', '%' . $step['page_url'] . '%');
            }

            if (isset($step['event_type']) && $step['event_type'] === 'click') {
                $stepQuery->whereHas('clickEvents', function ($q) use ($step) {
                    if (isset($step['element_selector'])) {
                        $q->where('element_selector', $step['element_selector']);
                    }
                });
            }

            if (isset($step['event_type']) && $step['event_type'] === 'form_submit') {
                $stepQuery->whereHas('formEvents', function ($q) use ($step) {
                    $q->where('event_type', 'submit');
                    if (isset($step['form_id'])) {
                        $q->where('form_id', $step['form_id']);
                    }
                });
            }

            $stepVisitors = $stepQuery->distinct('visitor_id')->count();
            $conversionRate = $totalVisitors > 0 ? round(($stepVisitors / $totalVisitors) * 100, 2) : 0;

            $funnelData[] = [
                'step' => $index + 1,
                'name' => $step['name'],
                'visitors' => $stepVisitors,
                'conversion_rate' => $conversionRate,
                'drop_off' => $index > 0 ? $funnelData[$index - 1]['visitors'] - $stepVisitors : 0,
            ];

            $totalVisitors = $stepVisitors; // Update for next step calculation
        }

        return $funnelData;
    }

    /**
     * Clear analytics cache for a site
     */
    public function clearCache($siteId)
    {
        $patterns = [
            "analytics_overview_{$siteId}_*",
            "device_stats_{$siteId}_*",
            "geographic_stats_{$siteId}_*",
            "page_stats_{$siteId}_*",
            "click_heatmap_{$siteId}_*",
            "mouse_heatmap_{$siteId}_*",
            "form_analytics_{$siteId}_*",
            "engagement_metrics_{$siteId}_*",
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }
}

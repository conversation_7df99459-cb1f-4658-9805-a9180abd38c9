@echo off
chcp 65001 >nul
title 环境检查工具
color 0C

echo.
echo ========================================
echo    环境检查工具
echo ========================================
echo.

echo 正在检查您的环境...
echo.

:: 检查当前目录
echo [检查1] 当前工作目录:
cd
echo.

:: 检查PHP路径
echo [检查2] PHP安装检查:
set PHP_FOUND=0

if exist "D:\BtSoft\php\72\php.exe" (
    echo ✅ 找到 PHP 7.2: D:\BtSoft\php\72\php.exe
    set PHP_PATH=D:\BtSoft\php\72\php.exe
    set PHP_FOUND=1
)

if exist "D:\BtSoft\php\73\php.exe" (
    echo ✅ 找到 PHP 7.3: D:\BtSoft\php\73\php.exe
    if %PHP_FOUND%==0 set PHP_PATH=D:\BtSoft\php\73\php.exe
    set PHP_FOUND=1
)

if exist "D:\BtSoft\php\74\php.exe" (
    echo ✅ 找到 PHP 7.4: D:\BtSoft\php\74\php.exe
    if %PHP_FOUND%==0 set PHP_PATH=D:\BtSoft\php\74\php.exe
    set PHP_FOUND=1
)

if exist "D:\BtSoft\php\80\php.exe" (
    echo ✅ 找到 PHP 8.0: D:\BtSoft\php\80\php.exe
    if %PHP_FOUND%==0 set PHP_PATH=D:\BtSoft\php\80\php.exe
    set PHP_FOUND=1
)

if %PHP_FOUND%==0 (
    echo ❌ 未找到PHP安装
    echo 请检查宝塔面板是否正确安装PHP
) else (
    echo.
    echo 使用PHP版本:
    "%PHP_PATH%" --version
)

echo.

:: 检查Composer
echo [检查3] Composer安装检查:
composer --version >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Composer已安装
    composer --version
) else (
    echo ❌ Composer未安装或不在PATH中
    echo.
    echo 解决方案：
    echo 1. 下载Composer: https://getcomposer.org/download/
    echo 2. 安装到系统PATH中
    echo 3. 或者下载composer.phar到当前目录
)

echo.

:: 检查目标目录
echo [检查4] 项目目录检查:
if exist "D:\wwwroot" (
    echo ✅ wwwroot目录存在
    if exist "D:\wwwroot\aichat.jagship.com" (
        echo ⚠️ 项目目录已存在: D:\wwwroot\aichat.jagship.com
        echo 内容:
        dir "D:\wwwroot\aichat.jagship.com" /b
    ) else (
        echo ✅ 项目目录不存在，可以创建新项目
    )
) else (
    echo ❌ wwwroot目录不存在
    echo 请先创建目录: D:\wwwroot
)

echo.

:: 检查网络连接
echo [检查5] 网络连接检查:
ping -n 1 packagist.phpcomposer.com >nul 2>&1
if %errorlevel%==0 (
    echo ✅ 网络连接正常
) else (
    echo ❌ 网络连接有问题
    echo 请检查网络设置
)

echo.

:: 检查权限
echo [检查6] 权限检查:
echo test > test_write.tmp 2>nul
if exist test_write.tmp (
    echo ✅ 当前目录有写入权限
    del test_write.tmp
) else (
    echo ❌ 当前目录没有写入权限
    echo 请以管理员身份运行
)

echo.
echo ========================================
echo    检查完成
echo ========================================
echo.

if %PHP_FOUND%==1 (
    echo 环境检查基本通过，可以尝试部署
    echo.
    echo 建议的PHP路径: %PHP_PATH%
    echo.
    echo 按任意键继续，或关闭窗口退出
    pause >nul
    
    echo.
    echo 是否要创建简化的部署脚本？ (Y/N)
    set /p choice=请选择: 
    if /i "%choice%"=="Y" (
        call :create_simple_deploy
    )
) else (
    echo ❌ 环境检查失败，请先解决上述问题
    pause
)

goto :eof

:create_simple_deploy
echo.
echo 正在创建简化部署脚本...

echo @echo off > 简化部署.bat
echo title 简化部署脚本 >> 简化部署.bat
echo. >> 简化部署.bat
echo set PHP_PATH=%PHP_PATH% >> 简化部署.bat
echo set PROJECT_DIR=D:\wwwroot\aichat.jagship.com >> 简化部署.bat
echo. >> 简化部署.bat
echo echo 开始部署... >> 简化部署.bat
echo. >> 简化部署.bat
echo cd /d D:\wwwroot >> 简化部署.bat
echo composer config -g secure-http false >> 简化部署.bat
echo composer config -g repo.packagist composer http://packagist.phpcomposer.com >> 简化部署.bat
echo composer create-project --prefer-dist laravel/laravel aichat.jagship.com "7.*" >> 简化部署.bat
echo. >> 简化部署.bat
echo cd aichat.jagship.com >> 简化部署.bat
echo "%%PHP_PATH%%" artisan key:generate >> 简化部署.bat
echo. >> 简化部署.bat
echo echo 部署完成！ >> 简化部署.bat
echo pause >> 简化部署.bat

echo ✅ 简化部署脚本已创建: 简化部署.bat
echo.
echo 现在可以运行 简化部署.bat 进行部署
pause
goto :eof

<?php

return [

    /*
    |--------------------------------------------------------------------------
    | WebSocket Server Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the WebSocket server used for real-time chat
    |
    */

    'host' => env('WEBSOCKET_HOST', '127.0.0.1'),
    'port' => env('WEBSOCKET_PORT', 8080),

    /*
    |--------------------------------------------------------------------------
    | Connection Settings
    |--------------------------------------------------------------------------
    */

    'max_connections' => env('WEBSOCKET_MAX_CONNECTIONS', 1000),
    'heartbeat_interval' => env('WEBSOCKET_HEARTBEAT_INTERVAL', 30), // seconds
    'connection_timeout' => env('WEBSOCKET_CONNECTION_TIMEOUT', 300), // seconds

    /*
    |--------------------------------------------------------------------------
    | Message Settings
    |--------------------------------------------------------------------------
    */

    'max_message_size' => env('WEBSOCKET_MAX_MESSAGE_SIZE', 65536), // bytes
    'message_queue_size' => env('WEBSOCKET_MESSAGE_QUEUE_SIZE', 100),

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */

    'allowed_origins' => env('WEBSOCKET_ALLOWED_ORIGINS', '*'),
    'enable_compression' => env('WEBSOCKET_ENABLE_COMPRESSION', true),
    'enable_ssl' => env('WEBSOCKET_ENABLE_SSL', false),
    'ssl_cert_path' => env('WEBSOCKET_SSL_CERT_PATH'),
    'ssl_key_path' => env('WEBSOCKET_SSL_KEY_PATH'),

    /*
    |--------------------------------------------------------------------------
    | Redis Configuration for WebSocket
    |--------------------------------------------------------------------------
    */

    'redis' => [
        'connection' => env('WEBSOCKET_REDIS_CONNECTION', 'default'),
        'prefix' => env('WEBSOCKET_REDIS_PREFIX', 'websocket:'),
        'channels' => [
            'session' => 'session:',
            'site_agents' => 'site:{site_id}:agents',
            'user' => 'user:',
            'broadcast' => 'broadcast',
        ]
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    */

    'logging' => [
        'enabled' => env('WEBSOCKET_LOGGING_ENABLED', true),
        'level' => env('WEBSOCKET_LOGGING_LEVEL', 'info'),
        'log_connections' => env('WEBSOCKET_LOG_CONNECTIONS', true),
        'log_messages' => env('WEBSOCKET_LOG_MESSAGES', false), // Set to false in production
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */

    'performance' => [
        'enable_message_batching' => env('WEBSOCKET_ENABLE_MESSAGE_BATCHING', true),
        'batch_size' => env('WEBSOCKET_BATCH_SIZE', 10),
        'batch_timeout' => env('WEBSOCKET_BATCH_TIMEOUT', 100), // milliseconds
        'enable_connection_pooling' => env('WEBSOCKET_ENABLE_CONNECTION_POOLING', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Chat-specific Settings
    |--------------------------------------------------------------------------
    */

    'chat' => [
        'typing_timeout' => env('WEBSOCKET_TYPING_TIMEOUT', 3), // seconds
        'max_concurrent_sessions_per_agent' => env('MAX_CONCURRENT_SESSIONS_PER_AGENT', 5),
        'session_timeout' => env('CHAT_SESSION_TIMEOUT', 1800), // 30 minutes
        'visitor_timeout' => env('VISITOR_TIMEOUT', 300), // 5 minutes
        'auto_assign_agents' => env('AUTO_ASSIGN_AGENTS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Event Types
    |--------------------------------------------------------------------------
    */

    'events' => [
        'connection' => 'connection',
        'auth' => 'auth',
        'visitor_join' => 'visitor_join',
        'agent_join' => 'agent_join',
        'chat_message' => 'chat_message',
        'typing_start' => 'typing_start',
        'typing_stop' => 'typing_stop',
        'session_transfer' => 'session_transfer',
        'session_close' => 'session_close',
        'agent_status' => 'agent_status',
        'heartbeat' => 'heartbeat',
        'error' => 'error',
    ],

];

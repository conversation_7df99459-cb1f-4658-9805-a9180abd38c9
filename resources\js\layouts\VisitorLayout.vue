<template>
  <div class="min-h-screen bg-white">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div class="flex items-center">
            <img class="h-8 w-auto" src="/images/logo.png" alt="Company Logo" />
            <h1 class="ml-3 text-xl font-semibold text-gray-900">Your Company</h1>
          </div>
          
          <nav class="hidden md:flex space-x-8">
            <router-link
              v-for="item in navigationItems"
              :key="item.name"
              :to="item.to"
              class="text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
              :class="{ 'text-blue-600 bg-blue-50': isActiveRoute(item.to) }"
            >
              {{ item.label }}
            </router-link>
          </nav>

          <!-- Mobile menu button -->
          <div class="md:hidden">
            <button @click="mobileMenuOpen = !mobileMenuOpen" class="text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500">
              <Bars3Icon v-if="!mobileMenuOpen" class="h-6 w-6" />
              <XMarkIcon v-else class="h-6 w-6" />
            </button>
          </div>
        </div>

        <!-- Mobile menu -->
        <div v-if="mobileMenuOpen" class="md:hidden pb-4">
          <div class="space-y-1">
            <router-link
              v-for="item in navigationItems"
              :key="item.name"
              :to="item.to"
              class="block px-3 py-2 rounded-md text-base font-medium text-gray-500 hover:text-gray-900 hover:bg-gray-50"
              :class="{ 'text-blue-600 bg-blue-50': isActiveRoute(item.to) }"
              @click="mobileMenuOpen = false"
            >
              {{ item.label }}
            </router-link>
          </div>
        </div>
      </div>
    </header>

    <!-- Main content -->
    <main>
      <router-view />
    </main>

    <!-- Footer -->
    <footer class="bg-gray-50 border-t border-gray-200">
      <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div class="col-span-1 md:col-span-2">
            <img class="h-8 w-auto" src="/images/logo.png" alt="Company Logo" />
            <p class="mt-4 text-gray-600 text-sm">
              We provide excellent customer service and support. Contact us anytime for assistance.
            </p>
          </div>
          
          <div>
            <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase">Support</h3>
            <ul class="mt-4 space-y-4">
              <li>
                <router-link to="/contact" class="text-base text-gray-500 hover:text-gray-900">
                  Contact Us
                </router-link>
              </li>
              <li>
                <router-link to="/chat" class="text-base text-gray-500 hover:text-gray-900">
                  Live Chat
                </router-link>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase">Company</h3>
            <ul class="mt-4 space-y-4">
              <li>
                <a href="#" class="text-base text-gray-500 hover:text-gray-900">About</a>
              </li>
              <li>
                <a href="#" class="text-base text-gray-500 hover:text-gray-900">Privacy</a>
              </li>
              <li>
                <a href="#" class="text-base text-gray-500 hover:text-gray-900">Terms</a>
              </li>
            </ul>
          </div>
        </div>
        
        <div class="mt-8 border-t border-gray-200 pt-8">
          <p class="text-base text-gray-400 text-center">
            &copy; 2024 Your Company. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { Bars3Icon, XMarkIcon } from '@heroicons/vue/24/outline'

export default {
  name: 'VisitorLayout',
  components: {
    Bars3Icon,
    XMarkIcon
  },
  setup() {
    const route = useRoute()
    const mobileMenuOpen = ref(false)

    const navigationItems = [
      { name: 'home', label: 'Home', to: '/' },
      { name: 'contact', label: 'Contact', to: '/contact' },
      { name: 'chat', label: 'Live Chat', to: '/chat' }
    ]

    const isActiveRoute = (routePath) => {
      return route.path === routePath
    }

    return {
      mobileMenuOpen,
      navigationItems,
      isActiveRoute
    }
  }
}
</script>

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;

class FileUploadController extends Controller
{
    /**
     * Upload chat file
     */
    public function uploadChatFile(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:jpeg,png,gif,pdf,doc,docx,txt,zip|max:10240',
            'session_id' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $file = $request->file('file');
            $sessionId = $request->session_id;
            
            // Generate unique filename
            $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            $path = "chat_files/{$sessionId}/" . $filename;
            
            // Store file
            $storedPath = $file->storeAs('chat_files/' . $sessionId, $filename, 'public');
            
            // Get file info
            $fileInfo = [
                'original_name' => $file->getClientOriginalName(),
                'filename' => $filename,
                'path' => $storedPath,
                'url' => Storage::url($storedPath),
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'extension' => $file->getClientOriginalExtension()
            ];

            // Generate thumbnail for images
            if (in_array($file->getClientOriginalExtension(), ['jpg', 'jpeg', 'png', 'gif'])) {
                $thumbnailPath = $this->generateThumbnail($file, $sessionId, $filename);
                $fileInfo['thumbnail_url'] = $thumbnailPath ? Storage::url($thumbnailPath) : null;
            }

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully',
                'data' => $fileInfo
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'File upload failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload avatar
     */
    public function uploadAvatar(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,jpg|max:2048'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $file = $request->file('avatar');
            $userId = $request->user()->id;
            
            // Delete old avatar if exists
            $user = $request->user();
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }
            
            // Generate unique filename
            $filename = 'avatar_' . $userId . '_' . time() . '.' . $file->getClientOriginalExtension();
            $path = 'avatars/' . $filename;
            
            // Resize and store image
            $image = Image::make($file);
            $image->fit(200, 200, function ($constraint) {
                $constraint->upsize();
            });
            
            Storage::disk('public')->put($path, $image->encode());
            
            // Update user avatar
            $user->update(['avatar' => $path]);
            
            return response()->json([
                'success' => true,
                'message' => 'Avatar uploaded successfully',
                'data' => [
                    'avatar_url' => Storage::url($path),
                    'path' => $path
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Avatar upload failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload contact attachment
     */
    public function uploadContactAttachment(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|mimes:jpeg,png,gif,pdf,doc,docx,txt|max:10240'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $file = $request->file('file');
            
            // Generate unique filename
            $filename = 'contact_' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            $path = 'contact_attachments/' . $filename;
            
            // Store file privately
            $storedPath = $file->storeAs('contact_attachments', $filename, 'private');
            
            $fileInfo = [
                'original_name' => $file->getClientOriginalName(),
                'filename' => $filename,
                'path' => $storedPath,
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'extension' => $file->getClientOriginalExtension()
            ];

            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully',
                'data' => $fileInfo
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'File upload failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download file
     */
    public function downloadFile(Request $request, string $filename): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $path = $request->get('path');
        $disk = $request->get('disk', 'public');

        if (!$path) {
            abort(404, 'File not found');
        }

        if (!Storage::disk($disk)->exists($path)) {
            abort(404, 'File not found');
        }

        return Storage::disk($disk)->download($path, $filename);
    }

    /**
     * Delete file
     */
    public function deleteFile(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'path' => 'required|string',
            'disk' => 'nullable|in:public,private'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $path = $request->path;
            $disk = $request->get('disk', 'public');

            if (Storage::disk($disk)->exists($path)) {
                Storage::disk($disk)->delete($path);
                
                return response()->json([
                    'success' => true,
                    'message' => 'File deleted successfully'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'File not found'
            ], 404);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'File deletion failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get file info
     */
    public function getFileInfo(Request $request, string $path): JsonResponse
    {
        $disk = $request->get('disk', 'public');

        if (!Storage::disk($disk)->exists($path)) {
            return response()->json([
                'success' => false,
                'message' => 'File not found'
            ], 404);
        }

        try {
            $fileInfo = [
                'path' => $path,
                'size' => Storage::disk($disk)->size($path),
                'last_modified' => Storage::disk($disk)->lastModified($path),
                'mime_type' => Storage::disk($disk)->mimeType($path),
                'url' => $disk === 'public' ? Storage::url($path) : null
            ];

            return response()->json([
                'success' => true,
                'data' => $fileInfo
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get file info: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk upload files
     */
    public function bulkUpload(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'files' => 'required|array|max:10',
            'files.*' => 'file|mimes:jpeg,png,gif,pdf,doc,docx,txt|max:10240',
            'folder' => 'nullable|string|max:100'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $uploadedFiles = [];
        $errors = [];

        foreach ($request->file('files') as $index => $file) {
            try {
                $folder = $request->get('folder', 'uploads');
                $filename = time() . '_' . $index . '_' . $file->getClientOriginalName();
                $path = $folder . '/' . $filename;
                
                $storedPath = $file->storeAs($folder, $filename, 'public');
                
                $uploadedFiles[] = [
                    'original_name' => $file->getClientOriginalName(),
                    'filename' => $filename,
                    'path' => $storedPath,
                    'url' => Storage::url($storedPath),
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType()
                ];

            } catch (\Exception $e) {
                $errors[] = [
                    'file' => $file->getClientOriginalName(),
                    'error' => $e->getMessage()
                ];
            }
        }

        return response()->json([
            'success' => count($errors) === 0,
            'message' => count($errors) === 0 ? 'All files uploaded successfully' : 'Some files failed to upload',
            'data' => [
                'uploaded_files' => $uploadedFiles,
                'errors' => $errors
            ]
        ]);
    }

    /**
     * Clean up old files
     */
    public function cleanupOldFiles(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        $folder = $request->get('folder', 'chat_files');
        
        try {
            $files = Storage::disk('public')->allFiles($folder);
            $deletedCount = 0;
            $cutoffTime = now()->subDays($days)->timestamp;

            foreach ($files as $file) {
                $lastModified = Storage::disk('public')->lastModified($file);
                
                if ($lastModified < $cutoffTime) {
                    Storage::disk('public')->delete($file);
                    $deletedCount++;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Cleaned up {$deletedCount} old files",
                'data' => [
                    'deleted_count' => $deletedCount
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Cleanup failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate thumbnail for image
     */
    private function generateThumbnail($file, string $sessionId, string $filename): ?string
    {
        try {
            $thumbnailFilename = 'thumb_' . $filename;
            $thumbnailPath = "chat_files/{$sessionId}/thumbnails/" . $thumbnailFilename;
            
            $image = Image::make($file);
            $image->fit(150, 150, function ($constraint) {
                $constraint->upsize();
            });
            
            Storage::disk('public')->put($thumbnailPath, $image->encode());
            
            return $thumbnailPath;

        } catch (\Exception $e) {
            \Log::error('Thumbnail generation failed: ' . $e->getMessage());
            return null;
        }
    }
}

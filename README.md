# 国际独立网站在线客服系统

一个功能强大的多站点客服解决方案，支持实时聊天、访客行为分析、智能工作模式切换等功能。

## 🚀 主要特性

### 核心功能
- **实时聊天系统**: 基于WebSocket的即时通讯，支持文字、文件、表情符号
- **多站点管理**: 支持管理多个网站的客服服务，独立配置和数据隔离
- **智能路由分配**: 自动分配客服代理，支持负载均衡和技能匹配
- **访客行为追踪**: 详细的访客轨迹分析，包括页面访问、停留时间、转化路径
- **数据可视化**: 丰富的图表和报告，实时监控和历史分析

### 高级功能
- **智能工作模式**: 根据工作时间自动切换在线/离线状态
- **GDPR合规**: 完整的数据保护和隐私合规功能
- **多语言支持**: 支持15种语言的国际化
- **主题定制**: 灵活的UI主题和样式定制
- **API接口**: 完整的RESTful API，支持第三方集成
- **Webhook集成**: 支持实时事件通知和第三方系统集成

### 技术特性
- **高性能**: Redis缓存、数据库优化、CDN支持
- **高可用**: 负载均衡、故障转移、自动恢复
- **安全防护**: 多层安全防护、数据加密、访问控制
- **移动适配**: 响应式设计，完美支持各种设备

## 🛠 技术栈

### 后端技术
- **框架**: Laravel 7.29 (PHP 7.2+)
- **数据库**: MySQL 5.7+ / MariaDB 10.3+
- **缓存**: Redis 5.0+
- **实时通信**: WebSocket (Ratchet)
- **队列**: Redis Queue
- **认证**: Laravel Sanctum

### 前端技术
- **框架**: Vue.js 3 + Composition API
- **状态管理**: Pinia
- **UI框架**: Tailwind CSS
- **构建工具**: Vite
- **图表库**: Chart.js / ECharts

### 基础设施
- **Web服务器**: Nginx 1.20+ / Apache 2.4+
- **操作系统**: CentOS 7+ / Ubuntu 18.04+
- **容器化**: Docker (可选)
- **监控**: 自定义监控系统

## 📋 系统要求

### 最低要求
- **PHP**: 7.2 或更高版本
- **MySQL**: 5.7 或更高版本
- **Redis**: 5.0 或更高版本
- **内存**: 2GB RAM
- **存储**: 10GB 可用空间

### 推荐配置
- **PHP**: 7.4 或 8.0
- **MySQL**: 8.0
- **Redis**: 6.0+
- **内存**: 4GB+ RAM
- **存储**: 50GB+ SSD

### PHP扩展要求
```
php-fpm, php-mysql, php-redis, php-gd, php-curl
php-mbstring, php-xml, php-zip, php-json, php-openssl
php-fileinfo, php-tokenizer
```

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-repo/chat_web.git
cd chat_web
```

### 2. 安装依赖
```bash
# 安装 PHP 依赖
composer install

# 安装前端依赖
npm install
```

### 3. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env

# 生成应用密钥
php artisan key:generate

# 编辑 .env 文件，配置数据库和Redis连接
```

### 4. 数据库设置
```bash
# 运行数据库迁移
php artisan migrate

# 运行数据填充（可选）
php artisan db:seed
```

### 5. 构建前端资源
```bash
# 开发环境
npm run dev

# 生产环境
npm run production
```

### 6. 启动服务
```bash
# 启动 Web 服务
php artisan serve

# 启动 WebSocket 服务器
php artisan websocket:serve

# 启动队列处理器
php artisan queue:work
```

## 📚 文档

- [安装部署指南](docs/INSTALLATION_GUIDE.md)
- [用户使用说明](docs/USER_MANUAL.md)
- [API接口文档](docs/API_DOCUMENTATION.md)
- [开发者文档](docs/DEVELOPER_GUIDE.md)

## 🏗 项目结构

```
chat_web/
├── app/                    # 应用核心代码
│   ├── Console/           # 命令行工具
│   ├── Events/            # 事件类
│   ├── Http/              # HTTP控制器和中间件
│   ├── Models/            # 数据模型
│   ├── Services/          # 业务逻辑服务
│   └── WebSocket/         # WebSocket处理器
├── config/                # 配置文件
├── database/              # 数据库迁移和填充
├── docs/                  # 项目文档
├── public/                # 公共资源文件
├── resources/             # 前端资源
│   ├── js/               # JavaScript文件
│   ├── css/              # 样式文件
│   └── views/            # 视图模板
├── routes/                # 路由定义
├── storage/               # 存储目录
└── tests/                 # 测试文件
```

## 🔧 主要功能模块

### 1. 用户认证系统
- 多角色权限管理（管理员、主管、客服代理）
- JWT令牌认证
- 单点登录支持
- 密码安全策略

### 2. 实时聊天系统
- WebSocket实时通信
- 消息类型支持（文字、文件、表情）
- 聊天记录存储和检索
- 消息状态追踪（已发送、已读等）

### 3. 访客管理系统
- 访客信息收集和存储
- 访问轨迹追踪
- 地理位置识别
- 设备和浏览器识别

### 4. 数据分析系统
- 实时数据看板
- 历史数据报告
- 客服绩效分析
- 访客行为分析

### 5. 系统配置管理
- 站点配置管理
- 主题和样式定制
- 工作流程配置
- 通知设置管理

## 🔒 安全特性

- **数据加密**: 敏感数据加密存储
- **访问控制**: 基于角色的权限控制
- **安全头**: 完整的HTTP安全头配置
- **CSRF保护**: 跨站请求伪造防护
- **XSS防护**: 跨站脚本攻击防护
- **SQL注入防护**: 参数化查询和ORM保护
- **文件上传安全**: 文件类型和大小限制
- **GDPR合规**: 数据保护和隐私合规

## 📊 性能优化

- **数据库优化**: 索引优化、查询优化、连接池
- **缓存策略**: Redis缓存、页面缓存、对象缓存
- **CDN支持**: 静态资源CDN加速
- **代码优化**: 自动加载优化、代码压缩
- **监控告警**: 性能监控和自动告警

## 🧪 测试

```bash
# 运行所有测试
php artisan test

# 运行特定测试
php artisan test --filter=ChatServiceTest

# 生成测试覆盖率报告
php artisan test --coverage
```

## 🚀 部署

### 生产环境部署
1. 参考 [安装部署指南](docs/INSTALLATION_GUIDE.md)
2. 配置Web服务器（Nginx/Apache）
3. 设置SSL证书
4. 配置防火墙和安全策略
5. 设置监控和日志

### Docker部署（可选）
```bash
# 构建镜像
docker build -t chat_web .

# 运行容器
docker-compose up -d
```

## 🤝 贡献

欢迎提交Issue和Pull Request来帮助改进项目。

### 开发流程
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

- **技术支持**: <EMAIL>
- **商务合作**: <EMAIL>
- **官方网站**: https://example.com
- **在线文档**: https://docs.example.com

## 🎯 路线图

### v1.1 计划功能
- [ ] 移动端APP
- [ ] 语音通话支持
- [ ] 视频通话支持
- [ ] AI智能客服
- [ ] 更多第三方集成

### v1.2 计划功能
- [ ] 微信小程序支持
- [ ] 多媒体消息支持
- [ ] 高级分析功能
- [ ] 自定义机器人
- [ ] 工单系统集成

---

**开发团队**: Customer Service System Team  
**版本**: v1.0  
**最后更新**: 2024年1月

<?php

namespace App\Http\Controllers;

use App\Services\StatisticsService;
use App\Services\AnalyticsService;
use App\Models\ChatSession;
use App\Models\Site;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class StatisticsController extends Controller
{
    protected $statisticsService;
    protected $analyticsService;

    public function __construct(StatisticsService $statisticsService, AnalyticsService $analyticsService)
    {
        $this->statisticsService = $statisticsService;
        $this->analyticsService = $analyticsService;
    }

    /**
     * Get dashboard overview statistics
     */
    public function getDashboardOverview(Request $request, $siteId = null): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $overview = $this->statisticsService->getDashboardOverview($siteId, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $overview,
        ]);
    }

    /**
     * Get session statistics over time
     */
    public function getSessionStatistics(Request $request, $siteId = null): JsonResponse
    {
        $request->validate([
            'period' => 'nullable|in:hour,day,week,month,year',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $period = $request->period ?? 'day';
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $statistics = $this->statisticsService->getSessionStatistics($siteId, $period, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => [
                'period' => $period,
                'statistics' => $statistics,
            ],
        ]);
    }

    /**
     * Get agent performance statistics
     */
    public function getAgentPerformance(Request $request, $siteId = null): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'limit' => 'nullable|integer|min:1|max:100',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $performance = $this->statisticsService->getAgentPerformance($siteId, $startDate, $endDate);

        if ($request->limit) {
            $performance = $performance->take($request->limit);
        }

        return response()->json([
            'success' => true,
            'data' => $performance,
        ]);
    }

    /**
     * Get response time distribution
     */
    public function getResponseTimeDistribution(Request $request, $siteId = null): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $distribution = $this->statisticsService->getResponseTimeDistribution($siteId, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $distribution,
        ]);
    }

    /**
     * Get satisfaction rating distribution
     */
    public function getSatisfactionDistribution(Request $request, $siteId = null): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $distribution = $this->statisticsService->getSatisfactionDistribution($siteId, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $distribution,
        ]);
    }

    /**
     * Get message volume statistics
     */
    public function getMessageVolumeStats(Request $request, $siteId = null): JsonResponse
    {
        $request->validate([
            'period' => 'nullable|in:hour,day,week,month,year',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $period = $request->period ?? 'day';
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $stats = $this->statisticsService->getMessageVolumeStats($siteId, $period, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => [
                'period' => $period,
                'statistics' => $stats,
            ],
        ]);
    }

    /**
     * Get comprehensive analytics report
     */
    public function getAnalyticsReport(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'include_visitor_analytics' => 'nullable|boolean',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;
        $includeVisitorAnalytics = $request->include_visitor_analytics ?? false;

        $report = [
            'overview' => $this->statisticsService->getDashboardOverview($siteId, $startDate, $endDate),
            'session_stats' => $this->statisticsService->getSessionStatistics($siteId, 'day', $startDate, $endDate),
            'agent_performance' => $this->statisticsService->getAgentPerformance($siteId, $startDate, $endDate),
            'response_time_distribution' => $this->statisticsService->getResponseTimeDistribution($siteId, $startDate, $endDate),
            'satisfaction_distribution' => $this->statisticsService->getSatisfactionDistribution($siteId, $startDate, $endDate),
            'message_volume' => $this->statisticsService->getMessageVolumeStats($siteId, 'day', $startDate, $endDate),
        ];

        if ($includeVisitorAnalytics) {
            $report['visitor_overview'] = $this->analyticsService->getVisitorOverview($siteId, $startDate, $endDate);
            $report['device_stats'] = $this->analyticsService->getDeviceStats($siteId, $startDate, $endDate);
            $report['geographic_stats'] = $this->analyticsService->getGeographicStats($siteId, $startDate, $endDate);
            $report['page_stats'] = $this->analyticsService->getPageStats($siteId, $startDate, $endDate);
            $report['engagement_metrics'] = $this->analyticsService->getEngagementMetrics($siteId, $startDate, $endDate);
        }

        return response()->json([
            'success' => true,
            'data' => $report,
        ]);
    }

    /**
     * Export statistics data
     */
    public function exportStatistics(Request $request, $siteId = null): JsonResponse
    {
        $request->validate([
            'format' => 'required|in:csv,json,xlsx',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'include_sections' => 'nullable|array',
            'include_sections.*' => 'in:overview,sessions,agents,response_times,satisfaction,messages,visitors',
        ]);

        $format = $request->format;
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;
        $includeSections = $request->include_sections ?? ['overview', 'sessions', 'agents'];

        $exportData = [];

        if (in_array('overview', $includeSections)) {
            $exportData['overview'] = $this->statisticsService->getDashboardOverview($siteId, $startDate, $endDate);
        }

        if (in_array('sessions', $includeSections)) {
            $exportData['sessions'] = $this->statisticsService->getSessionStatistics($siteId, 'day', $startDate, $endDate);
        }

        if (in_array('agents', $includeSections)) {
            $exportData['agents'] = $this->statisticsService->getAgentPerformance($siteId, $startDate, $endDate);
        }

        if (in_array('response_times', $includeSections)) {
            $exportData['response_times'] = $this->statisticsService->getResponseTimeDistribution($siteId, $startDate, $endDate);
        }

        if (in_array('satisfaction', $includeSections)) {
            $exportData['satisfaction'] = $this->statisticsService->getSatisfactionDistribution($siteId, $startDate, $endDate);
        }

        if (in_array('messages', $includeSections)) {
            $exportData['messages'] = $this->statisticsService->getMessageVolumeStats($siteId, 'day', $startDate, $endDate);
        }

        if (in_array('visitors', $includeSections)) {
            $exportData['visitors'] = $this->analyticsService->getVisitorOverview($siteId, $startDate, $endDate);
        }

        // Generate filename
        $siteName = $siteId ? Site::find($siteId)?->name ?? 'site' : 'all-sites';
        $dateRange = $startDate && $endDate 
            ? $startDate->format('Y-m-d') . '_to_' . $endDate->format('Y-m-d')
            : 'all-time';
        $filename = "statistics_{$siteName}_{$dateRange}.{$format}";

        return response()->json([
            'success' => true,
            'data' => [
                'filename' => $filename,
                'format' => $format,
                'export_data' => $exportData,
                'generated_at' => now()->toISOString(),
            ],
        ]);
    }

    /**
     * Get real-time statistics
     */
    public function getRealTimeStats(Request $request, $siteId = null): JsonResponse
    {
        $request->validate([
            'minutes' => 'nullable|integer|min:1|max:1440',
        ]);

        $minutes = $request->minutes ?? 30;
        $startTime = now()->subMinutes($minutes);

        // Get recent sessions
        $recentSessionsQuery = ChatSession::where('started_at', '>=', $startTime);
        if ($siteId) {
            $recentSessionsQuery->where('site_id', $siteId);
        }

        $recentSessions = $recentSessionsQuery->count();
        $activeSessions = $recentSessionsQuery->where('status', 'active')->count();

        // Get visitor activity
        $visitorActivity = $this->analyticsService->getRealTimeActivity($siteId, $minutes);

        return response()->json([
            'success' => true,
            'data' => [
                'time_range_minutes' => $minutes,
                'recent_sessions' => $recentSessions,
                'active_sessions' => $activeSessions,
                'visitor_activity' => $visitorActivity,
                'updated_at' => now()->toISOString(),
            ],
        ]);
    }

    /**
     * Get visitor conversion funnel
     */
    public function getConversionFunnel(Request $request, $siteId = null): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $funnel = $this->statisticsService->getVisitorConversionFunnel($siteId, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $funnel,
        ]);
    }

    /**
     * Get peak hours analysis
     */
    public function getPeakHours(Request $request, $siteId = null): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $peakHours = $this->statisticsService->getPeakHoursAnalysis($siteId, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $peakHours,
        ]);
    }

    /**
     * Clear statistics cache
     */
    public function clearCache($siteId = null): JsonResponse
    {
        $this->statisticsService->clearCache($siteId);
        $this->analyticsService->clearCache($siteId);

        return response()->json([
            'success' => true,
            'message' => 'Statistics cache cleared successfully',
        ]);
    }
}

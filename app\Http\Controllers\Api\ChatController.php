<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\Visitor;
use App\Models\User;
use App\Events\MessageSent;
use App\Events\SessionStarted;
use App\Events\SessionEnded;
use App\Events\SessionTransferred;
use App\Events\TypingIndicator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Carbon\Carbon;

class ChatController extends Controller
{
    /**
     * Start a new chat session
     */
    public function startSession(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'visitor_name' => 'required|string|max:255',
            'visitor_email' => 'nullable|email|max:255',
            'visitor_phone' => 'nullable|string|max:20',
            'subject' => 'nullable|string|max:255',
            'message' => 'required|string|max:2000',
            'site_id' => 'required|exists:sites,id',
            'visitor_id' => 'nullable|string',
            'page_url' => 'nullable|url',
            'user_agent' => 'nullable|string',
            'language' => 'nullable|string|max:10'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Get or create visitor
        $visitor = null;
        if ($request->visitor_id) {
            $visitor = Visitor::find($request->visitor_id);
        }

        if (!$visitor) {
            $visitor = Visitor::create([
                'visitor_id' => Str::uuid(),
                'site_id' => $request->site_id,
                'name' => $request->visitor_name,
                'email' => $request->visitor_email,
                'phone' => $request->visitor_phone,
                'ip_address' => $request->ip(),
                'user_agent' => $request->user_agent,
                'language' => $request->language ?? 'en',
                'first_visit_at' => now(),
                'last_visit_at' => now()
            ]);
        } else {
            $visitor->update([
                'name' => $request->visitor_name,
                'email' => $request->visitor_email,
                'phone' => $request->visitor_phone,
                'last_visit_at' => now()
            ]);
        }

        // Create chat session
        $session = ChatSession::create([
            'session_id' => Str::uuid(),
            'site_id' => $request->site_id,
            'visitor_id' => $visitor->id,
            'visitor_name' => $request->visitor_name,
            'visitor_email' => $request->visitor_email,
            'visitor_phone' => $request->visitor_phone,
            'subject' => $request->subject,
            'status' => 'waiting',
            'started_at' => now(),
            'page_url' => $request->page_url,
            'user_agent' => $request->user_agent,
            'ip_address' => $request->ip(),
            'language' => $request->language ?? 'en'
        ]);

        // Create initial message
        $message = ChatMessage::create([
            'session_id' => $session->id,
            'message_id' => Str::uuid(),
            'sender_type' => 'visitor',
            'sender_name' => $request->visitor_name,
            'message' => $request->message,
            'sent_at' => now()
        ]);

        // Broadcast session started event
        broadcast(new SessionStarted($session));

        return response()->json([
            'success' => true,
            'message' => 'Chat session started',
            'data' => [
                'session_id' => $session->session_id,
                'status' => $session->status,
                'queue_position' => $this->getQueuePosition($session),
                'estimated_wait_time' => $this->getEstimatedWaitTime()
            ]
        ]);
    }

    /**
     * Get messages for a session
     */
    public function getMessages(Request $request, string $sessionId): JsonResponse
    {
        $session = ChatSession::where('session_id', $sessionId)->first();

        if (!$session) {
            return response()->json([
                'success' => false,
                'message' => 'Session not found'
            ], 404);
        }

        $messages = ChatMessage::where('session_id', $session->id)
            ->orderBy('sent_at', 'asc')
            ->get()
            ->map(function ($message) {
                return [
                    'id' => $message->message_id,
                    'sender_type' => $message->sender_type,
                    'sender_name' => $message->sender_name,
                    'message' => $message->message,
                    'file_url' => $message->file_url,
                    'file_name' => $message->file_name,
                    'file_size' => $message->file_size,
                    'sent_at' => $message->sent_at->toISOString(),
                    'is_internal' => $message->is_internal
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'messages' => $messages,
                'session' => [
                    'id' => $session->session_id,
                    'status' => $session->status,
                    'agent_name' => $session->agent_name,
                    'started_at' => $session->started_at->toISOString()
                ]
            ]
        ]);
    }

    /**
     * Send a message
     */
    public function sendMessage(Request $request, string $sessionId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required_without:file|string|max:2000',
            'file' => 'nullable|file|mimes:jpeg,png,gif,pdf,doc,docx,txt|max:10240',
            'sender_name' => 'required|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $session = ChatSession::where('session_id', $sessionId)->first();

        if (!$session) {
            return response()->json([
                'success' => false,
                'message' => 'Session not found'
            ], 404);
        }

        if ($session->status === 'ended') {
            return response()->json([
                'success' => false,
                'message' => 'Session has ended'
            ], 400);
        }

        $messageData = [
            'session_id' => $session->id,
            'message_id' => Str::uuid(),
            'sender_type' => 'visitor',
            'sender_name' => $request->sender_name,
            'message' => $request->message,
            'sent_at' => now()
        ];

        // Handle file upload
        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $filename = time() . '_' . $file->getClientOriginalName();
            $path = $file->storeAs('chat_files', $filename, 'public');
            
            $messageData['file_url'] = '/storage/' . $path;
            $messageData['file_name'] = $file->getClientOriginalName();
            $messageData['file_size'] = $file->getSize();
        }

        $message = ChatMessage::create($messageData);

        // Update session last activity
        $session->update(['last_activity_at' => now()]);

        // Broadcast message
        broadcast(new MessageSent($message, $session));

        return response()->json([
            'success' => true,
            'message' => 'Message sent',
            'data' => [
                'message_id' => $message->message_id,
                'sent_at' => $message->sent_at->toISOString()
            ]
        ]);
    }

    /**
     * Update typing indicator
     */
    public function updateTyping(Request $request, string $sessionId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'is_typing' => 'required|boolean',
            'sender_name' => 'required|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $session = ChatSession::where('session_id', $sessionId)->first();

        if (!$session) {
            return response()->json([
                'success' => false,
                'message' => 'Session not found'
            ], 404);
        }

        // Broadcast typing indicator
        broadcast(new TypingIndicator(
            $session,
            'visitor',
            $request->sender_name,
            $request->is_typing
        ));

        return response()->json([
            'success' => true,
            'message' => 'Typing indicator updated'
        ]);
    }

    /**
     * End session (visitor)
     */
    public function endSession(Request $request, string $sessionId): JsonResponse
    {
        $session = ChatSession::where('session_id', $sessionId)->first();

        if (!$session) {
            return response()->json([
                'success' => false,
                'message' => 'Session not found'
            ], 404);
        }

        if ($session->status === 'ended') {
            return response()->json([
                'success' => false,
                'message' => 'Session already ended'
            ], 400);
        }

        $session->update([
            'status' => 'ended',
            'ended_at' => now(),
            'ended_by' => 'visitor'
        ]);

        // Create system message
        ChatMessage::create([
            'session_id' => $session->id,
            'message_id' => Str::uuid(),
            'sender_type' => 'system',
            'sender_name' => 'System',
            'message' => 'Visitor has left the chat',
            'sent_at' => now()
        ]);

        // Broadcast session ended
        broadcast(new SessionEnded($session));

        return response()->json([
            'success' => true,
            'message' => 'Session ended'
        ]);
    }

    /**
     * Submit rating
     */
    public function submitRating(Request $request, string $sessionId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'feedback' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $session = ChatSession::where('session_id', $sessionId)->first();

        if (!$session) {
            return response()->json([
                'success' => false,
                'message' => 'Session not found'
            ], 404);
        }

        $session->update([
            'rating' => $request->rating,
            'feedback' => $request->feedback,
            'rated_at' => now()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Rating submitted successfully'
        ]);
    }

    /**
     * Submit feedback
     */
    public function submitFeedback(Request $request, string $sessionId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'feedback' => 'required|string|max:1000',
            'feedback_type' => 'nullable|in:compliment,complaint,suggestion'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $session = ChatSession::where('session_id', $sessionId)->first();

        if (!$session) {
            return response()->json([
                'success' => false,
                'message' => 'Session not found'
            ], 404);
        }

        $session->update([
            'feedback' => $request->feedback,
            'feedback_type' => $request->feedback_type
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Feedback submitted successfully'
        ]);
    }

    /**
     * Get queue position for waiting session
     */
    private function getQueuePosition(ChatSession $session): int
    {
        return ChatSession::where('status', 'waiting')
            ->where('started_at', '<', $session->started_at)
            ->count() + 1;
    }

    /**
     * Get estimated wait time
     */
    private function getEstimatedWaitTime(): int
    {
        $averageSessionTime = ChatSession::where('status', 'ended')
            ->where('started_at', '>=', now()->subDays(7))
            ->avg(\DB::raw('TIMESTAMPDIFF(MINUTE, started_at, ended_at)'));

        $waitingCount = ChatSession::where('status', 'waiting')->count();
        $onlineAgents = User::where('role', 'agent')
            ->where('status', 'online')
            ->where('is_active', true)
            ->count();

        if ($onlineAgents === 0) {
            return 0; // No agents available
        }

        return (int) (($waitingCount * ($averageSessionTime ?? 15)) / $onlineAgents);
    }
}

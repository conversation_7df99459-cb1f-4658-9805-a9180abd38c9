<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChatMessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('chat_messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('chat_session_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // null for visitor messages
            $table->string('message_id')->unique(); // UUID for message
            
            // Message Content
            $table->enum('type', ['text', 'file', 'image', 'system', 'emoji'])->default('text');
            $table->text('content');
            $table->json('metadata')->nullable(); // For file info, system message data, etc.
            
            // Message Status
            $table->enum('sender_type', ['visitor', 'agent', 'system'])->default('visitor');
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->boolean('is_edited')->default(false);
            $table->timestamp('edited_at')->nullable();
            
            // File Attachments
            $table->string('file_name')->nullable();
            $table->string('file_path')->nullable();
            $table->string('file_type')->nullable();
            $table->integer('file_size')->nullable(); // in bytes
            $table->string('file_mime_type')->nullable();
            
            // Message Threading (for replies)
            $table->foreignId('reply_to_message_id')->nullable()->constrained('chat_messages')->onDelete('set null');
            
            $table->timestamps();
            
            $table->index(['chat_session_id', 'created_at']);
            $table->index(['chat_session_id', 'sender_type']);
            $table->index(['user_id', 'created_at']);
            $table->index('message_id');
            $table->index(['is_read', 'sender_type']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('chat_messages');
    }
}

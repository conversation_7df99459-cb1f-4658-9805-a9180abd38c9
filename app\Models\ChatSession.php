<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class ChatSession extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'session_id',
        'site_id',
        'visitor_id',
        'user_id',
        'status',
        'initial_message',
        'initial_page_url',
        'initial_referrer',
        'started_at',
        'ended_at',
        'response_time',
        'first_response_time',
        'quality_rating',
        'quality_feedback',
        'transfer_reason',
        'close_reason',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'response_time' => 'integer',
        'first_response_time' => 'integer',
        'quality_rating' => 'integer',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($session) {
            if (!$session->session_id) {
                $session->session_id = Str::uuid();
            }
            if (!$session->started_at) {
                $session->started_at = now();
            }
        });
    }

    /**
     * Get the site that owns the chat session
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Get the visitor that owns the chat session
     */
    public function visitor()
    {
        return $this->belongsTo(Visitor::class);
    }

    /**
     * Get the user (agent) assigned to the chat session
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the chat messages for the session
     */
    public function messages()
    {
        return $this->hasMany(ChatMessage::class)->orderBy('created_at');
    }

    /**
     * Get the latest message
     */
    public function latestMessage()
    {
        return $this->hasOne(ChatMessage::class)->latestOfMany();
    }

    /**
     * Get session duration in seconds
     */
    public function getDurationAttribute()
    {
        if (!$this->started_at) {
            return 0;
        }

        $endTime = $this->ended_at ?: now();
        return $this->started_at->diffInSeconds($endTime);
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute()
    {
        $duration = $this->duration;
        $hours = floor($duration / 3600);
        $minutes = floor(($duration % 3600) / 60);
        $seconds = $duration % 60;

        if ($hours > 0) {
            return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        }
        
        return sprintf('%02d:%02d', $minutes, $seconds);
    }

    /**
     * Check if session is active
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Check if session is waiting
     */
    public function isWaiting()
    {
        return $this->status === 'waiting';
    }

    /**
     * Check if session is closed
     */
    public function isClosed()
    {
        return in_array($this->status, ['closed', 'timeout', 'transferred']);
    }

    /**
     * Assign agent to session
     */
    public function assignAgent(User $agent)
    {
        $this->update([
            'user_id' => $agent->id,
            'status' => 'active',
        ]);

        // Calculate first response time if this is the first agent assignment
        if (!$this->first_response_time) {
            $firstResponseTime = $this->started_at->diffInSeconds(now());
            $this->update(['first_response_time' => $firstResponseTime]);
        }

        return $this;
    }

    /**
     * Transfer session to another agent
     */
    public function transferTo(User $newAgent, $reason = null)
    {
        $oldAgentId = $this->user_id;
        
        $this->update([
            'user_id' => $newAgent->id,
            'transfer_reason' => $reason,
        ]);

        // Log transfer message
        $this->messages()->create([
            'message_id' => Str::uuid(),
            'content' => "Session transferred from agent to {$newAgent->name}" . ($reason ? " (Reason: {$reason})" : ''),
            'sender_type' => 'system',
            'type' => 'system',
            'metadata' => [
                'old_agent_id' => $oldAgentId,
                'new_agent_id' => $newAgent->id,
                'transfer_reason' => $reason,
            ]
        ]);

        return $this;
    }

    /**
     * Close session
     */
    public function close($reason = null, $rating = null, $feedback = null)
    {
        $this->update([
            'status' => 'closed',
            'ended_at' => now(),
            'close_reason' => $reason,
            'quality_rating' => $rating,
            'quality_feedback' => $feedback,
        ]);

        // Log close message
        $this->messages()->create([
            'message_id' => Str::uuid(),
            'content' => 'Session closed' . ($reason ? " (Reason: {$reason})" : ''),
            'sender_type' => 'system',
            'type' => 'system',
            'metadata' => [
                'close_reason' => $reason,
                'quality_rating' => $rating,
                'quality_feedback' => $feedback,
            ]
        ]);

        return $this;
    }

    /**
     * Mark session as timeout
     */
    public function timeout()
    {
        $this->update([
            'status' => 'timeout',
            'ended_at' => now(),
            'close_reason' => 'timeout',
        ]);

        return $this;
    }

    /**
     * Calculate average response time
     */
    public function calculateAverageResponseTime()
    {
        $agentMessages = $this->messages()
            ->where('sender_type', 'agent')
            ->orderBy('created_at')
            ->get();

        if ($agentMessages->isEmpty()) {
            return 0;
        }

        $totalResponseTime = 0;
        $responseCount = 0;
        $lastVisitorMessageTime = $this->started_at;

        foreach ($this->messages()->orderBy('created_at')->get() as $message) {
            if ($message->sender_type === 'visitor') {
                $lastVisitorMessageTime = $message->created_at;
            } elseif ($message->sender_type === 'agent' && $lastVisitorMessageTime) {
                $responseTime = $lastVisitorMessageTime->diffInSeconds($message->created_at);
                $totalResponseTime += $responseTime;
                $responseCount++;
                $lastVisitorMessageTime = null;
            }
        }

        if ($responseCount === 0) {
            return 0;
        }

        $averageResponseTime = $totalResponseTime / $responseCount;
        
        $this->update(['response_time' => $averageResponseTime]);
        
        return $averageResponseTime;
    }

    /**
     * Get session statistics
     */
    public function getStatistics()
    {
        $messages = $this->messages;
        
        return [
            'total_messages' => $messages->count(),
            'visitor_messages' => $messages->where('sender_type', 'visitor')->count(),
            'agent_messages' => $messages->where('sender_type', 'agent')->count(),
            'system_messages' => $messages->where('sender_type', 'system')->count(),
            'duration' => $this->duration,
            'formatted_duration' => $this->formatted_duration,
            'first_response_time' => $this->first_response_time,
            'average_response_time' => $this->response_time,
            'quality_rating' => $this->quality_rating,
        ];
    }

    /**
     * Scope for active sessions
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for waiting sessions
     */
    public function scopeWaiting($query)
    {
        return $query->where('status', 'waiting');
    }

    /**
     * Scope for closed sessions
     */
    public function scopeClosed($query)
    {
        return $query->whereIn('status', ['closed', 'timeout', 'transferred']);
    }

    /**
     * Scope for sessions by site
     */
    public function scopeBySite($query, $siteId)
    {
        return $query->where('site_id', $siteId);
    }

    /**
     * Scope for sessions by agent
     */
    public function scopeByAgent($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for sessions within date range
     */
    public function scopeWithinDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('started_at', [$startDate, $endDate]);
    }
}

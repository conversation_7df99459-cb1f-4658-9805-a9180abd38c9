<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Site;

class CheckSiteAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $permission = null)
    {
        $user = $request->user();
        
        if (!$user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        // Get site from route parameter or request
        $siteId = $request->route('site') ?? $request->input('site_id');
        
        if ($siteId) {
            // If site ID is provided, check access to specific site
            $site = Site::find($siteId);
            
            if (!$site) {
                return response()->json(['error' => 'Site not found'], 404);
            }

            if (!$user->canAccessSite($site->id)) {
                return response()->json(['error' => 'Access denied to this site'], 403);
            }

            // Check specific permission if provided
            if ($permission && !$user->hasPermission($permission, $site->id)) {
                return response()->json(['error' => 'Insufficient permissions'], 403);
            }

            // Add site to request for easy access in controllers
            $request->merge(['current_site' => $site]);
        } else {
            // If no site specified, check if user has access to any site
            if ($user->sites()->where('is_active', true)->count() === 0) {
                return response()->json(['error' => 'No site access'], 403);
            }
        }

        return $next($request);
    }
}

<template>
  <form @submit.prevent="handleLogin" class="mt-8 space-y-6">
    <input type="hidden" name="remember" value="true" />
    
    <div class="rounded-md shadow-sm -space-y-px">
      <div>
        <label for="email" class="sr-only">Email address</label>
        <input
          id="email"
          v-model="form.email"
          name="email"
          type="email"
          autocomplete="email"
          required
          class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
          :class="{ 'border-red-300': errors.email }"
          placeholder="Email address"
        />
        <p v-if="errors.email" class="mt-1 text-sm text-red-600">{{ errors.email }}</p>
      </div>
      
      <div>
        <label for="password" class="sr-only">Password</label>
        <input
          id="password"
          v-model="form.password"
          name="password"
          type="password"
          autocomplete="current-password"
          required
          class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
          :class="{ 'border-red-300': errors.password }"
          placeholder="Password"
        />
        <p v-if="errors.password" class="mt-1 text-sm text-red-600">{{ errors.password }}</p>
      </div>
    </div>

    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <input
          id="remember-me"
          v-model="form.remember"
          name="remember-me"
          type="checkbox"
          class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label for="remember-me" class="ml-2 block text-sm text-gray-900">
          Remember me
        </label>
      </div>

      <div class="text-sm">
        <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
          Forgot your password?
        </a>
      </div>
    </div>

    <!-- Error message -->
    <div v-if="errorMessage" class="rounded-md bg-red-50 p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <ExclamationTriangleIcon class="h-5 w-5 text-red-400" />
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">
            Login Failed
          </h3>
          <div class="mt-2 text-sm text-red-700">
            <p>{{ errorMessage }}</p>
          </div>
        </div>
      </div>
    </div>

    <div>
      <button
        type="submit"
        :disabled="isLoading"
        class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
          <LockClosedIcon class="h-5 w-5 text-blue-500 group-hover:text-blue-400" />
        </span>
        
        <span v-if="isLoading" class="flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Signing in...
        </span>
        <span v-else>Sign in</span>
      </button>
    </div>

    <!-- Demo accounts -->
    <div class="mt-6">
      <div class="relative">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-gray-300" />
        </div>
        <div class="relative flex justify-center text-sm">
          <span class="px-2 bg-gray-50 text-gray-500">Demo Accounts</span>
        </div>
      </div>

      <div class="mt-6 grid grid-cols-1 gap-3">
        <button
          v-for="demo in demoAccounts"
          :key="demo.role"
          type="button"
          @click="fillDemoAccount(demo)"
          class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
        >
          <span>{{ demo.label }}</span>
        </button>
      </div>
    </div>
  </form>
</template>

<script>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth.js'
import { useNotificationStore } from '../../stores/notifications.js'
import { LockClosedIcon, ExclamationTriangleIcon } from '@heroicons/vue/24/outline'

export default {
  name: 'Login',
  components: {
    LockClosedIcon,
    ExclamationTriangleIcon
  },
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    const notificationStore = useNotificationStore()

    // Form state
    const form = reactive({
      email: '',
      password: '',
      remember: false
    })

    const errors = reactive({})
    const errorMessage = ref('')
    const isLoading = ref(false)

    // Demo accounts for testing
    const demoAccounts = [
      {
        role: 'admin',
        label: 'Admin Demo',
        email: '<EMAIL>',
        password: 'password123'
      },
      {
        role: 'supervisor',
        label: 'Supervisor Demo',
        email: '<EMAIL>',
        password: 'password123'
      },
      {
        role: 'agent',
        label: 'Agent Demo',
        email: '<EMAIL>',
        password: 'password123'
      }
    ]

    // Methods
    const validateForm = () => {
      Object.keys(errors).forEach(key => delete errors[key])
      
      if (!form.email) {
        errors.email = 'Email is required'
      } else if (!/\S+@\S+\.\S+/.test(form.email)) {
        errors.email = 'Email is invalid'
      }
      
      if (!form.password) {
        errors.password = 'Password is required'
      } else if (form.password.length < 6) {
        errors.password = 'Password must be at least 6 characters'
      }
      
      return Object.keys(errors).length === 0
    }

    const handleLogin = async () => {
      if (!validateForm()) {
        return
      }

      try {
        isLoading.value = true
        errorMessage.value = ''

        const result = await authStore.login({
          email: form.email,
          password: form.password,
          remember: form.remember
        })

        if (result.success) {
          notificationStore.success('Welcome!', 'You have successfully logged in')
          router.push('/admin/dashboard')
        } else {
          errorMessage.value = result.message
        }
      } catch (error) {
        console.error('Login error:', error)
        errorMessage.value = 'An unexpected error occurred. Please try again.'
      } finally {
        isLoading.value = false
      }
    }

    const fillDemoAccount = (demo) => {
      form.email = demo.email
      form.password = demo.password
      errorMessage.value = ''
      Object.keys(errors).forEach(key => delete errors[key])
    }

    return {
      form,
      errors,
      errorMessage,
      isLoading,
      demoAccounts,
      handleLogin,
      fillDemoAccount
    }
  }
}
</script>

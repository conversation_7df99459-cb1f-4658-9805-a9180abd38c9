# 部署检查清单

## 本地部署检查清单 (Windows 10 + 宝塔)

### 环境准备 ✅
- [ ] 安装宝塔面板 8.3.3
- [ ] 安装 Nginx 1.20.0
- [ ] 安装 PHP 7.2 及必要扩展
- [ ] 安装 MySQL 5.7.38
- [ ] 安装 Redis 5.0.10
- [ ] 安装 phpMyAdmin 5.0

### PHP扩展检查 ✅
- [ ] opcache
- [ ] redis
- [ ] gd
- [ ] curl
- [ ] mbstring
- [ ] xml
- [ ] zip
- [ ] json
- [ ] openssl
- [ ] fileinfo
- [ ] tokenizer

### 项目部署 ✅
- [ ] 创建网站和数据库
- [ ] 上传项目文件
- [ ] 安装 Composer
- [ ] 运行 `composer install`
- [ ] 配置 `.env` 文件
- [ ] 运行 `php artisan key:generate`
- [ ] 运行 `php artisan migrate`
- [ ] 运行 `php artisan db:seed`

### 服务配置 ✅
- [ ] 配置 Nginx 虚拟主机
- [ ] 配置 hosts 文件
- [ ] 启动 WebSocket 服务
- [ ] 启动队列处理器
- [ ] 设置定时任务

### 测试验证 ✅
- [ ] 访问网站首页
- [ ] 测试数据库连接
- [ ] 测试 Redis 连接
- [ ] 测试 WebSocket 连接
- [ ] 创建管理员账户
- [ ] 登录后台管理

---

## 生产环境部署检查清单 (CentOS 7 + 宝塔)

### 服务器准备 ✅
- [ ] 更新系统 `yum update -y`
- [ ] 配置防火墙端口
- [ ] 设置时区
- [ ] 安装基础工具

### 宝塔面板安装 ✅
- [ ] 安装宝塔面板 9.4.0
- [ ] 记录面板登录信息
- [ ] 绑定宝塔账号
- [ ] 修改默认端口（可选）

### 环境安装 ✅
- [ ] 安装 Nginx 1.20.2
- [ ] 安装 PHP 7.2
- [ ] 安装 MySQL 5.7.44
- [ ] 安装 Redis 7.4.3
- [ ] 安装 phpMyAdmin 5.0

### PHP配置优化 ✅
- [ ] 安装必要扩展
- [ ] 优化 PHP 配置参数
- [ ] 配置 OPcache
- [ ] 设置错误日志

### 网站和数据库 ✅
- [ ] 创建网站
- [ ] 配置 SSL 证书
- [ ] 创建数据库
- [ ] 记录数据库连接信息

### Redis配置 ✅
- [ ] 设置 Redis 密码
- [ ] 配置内存限制
- [ ] 配置持久化
- [ ] 启动 Redis 服务

### 项目部署 ✅
- [ ] 上传/克隆项目代码
- [ ] 安装 Composer
- [ ] 运行 `composer install --no-dev --optimize-autoloader`
- [ ] 配置生产环境 `.env`
- [ ] 生成应用密钥
- [ ] 运行数据库迁移
- [ ] 创建存储链接
- [ ] 优化应用缓存

### Nginx配置 ✅
- [ ] 配置 SSL 证书
- [ ] 配置安全头
- [ ] 配置 WebSocket 代理
- [ ] 配置静态文件缓存
- [ ] 配置安全规则

### 系统服务 ✅
- [ ] 创建 WebSocket 服务
- [ ] 创建队列服务
- [ ] 启动并设置开机自启
- [ ] 配置定时任务

### 文件权限 ✅
- [ ] 设置项目文件所有者
- [ ] 设置目录权限 755
- [ ] 设置文件权限 644
- [ ] 设置存储目录权限 775

### 安全加固 ✅
- [ ] 配置防火墙规则
- [ ] 隐藏服务器版本信息
- [ ] 配置 fail2ban（可选）
- [ ] 设置强密码策略

### 监控和备份 ✅
- [ ] 配置系统监控
- [ ] 设置告警通知
- [ ] 配置自动备份脚本
- [ ] 测试备份恢复

### 性能优化 ✅
- [ ] 优化 MySQL 配置
- [ ] 优化 Redis 配置
- [ ] 优化 PHP-FPM 配置
- [ ] 配置 CDN（可选）

### 测试验证 ✅
- [ ] 运行系统测试脚本
- [ ] 检查所有服务状态
- [ ] 测试 HTTPS 访问
- [ ] 测试 WebSocket 连接
- [ ] 测试文件上传
- [ ] 测试邮件发送
- [ ] 性能压测

### 管理员设置 ✅
- [ ] 创建管理员账户
- [ ] 测试登录功能
- [ ] 配置基础设置
- [ ] 测试核心功能

---

## 部署后验证清单

### 功能验证 ✅
- [ ] 用户注册/登录
- [ ] 聊天功能
- [ ] 文件上传
- [ ] 消息推送
- [ ] 数据统计
- [ ] 邮件通知
- [ ] 多语言切换
- [ ] 主题定制

### 性能验证 ✅
- [ ] 页面加载速度 < 3秒
- [ ] API 响应时间 < 500ms
- [ ] WebSocket 连接稳定
- [ ] 数据库查询优化
- [ ] 缓存命中率 > 80%

### 安全验证 ✅
- [ ] HTTPS 强制跳转
- [ ] 安全头配置
- [ ] 文件上传安全
- [ ] SQL 注入防护
- [ ] XSS 防护
- [ ] CSRF 防护

### 监控验证 ✅
- [ ] 系统资源监控
- [ ] 应用日志记录
- [ ] 错误告警通知
- [ ] 备份任务执行

---

## 常用命令速查

### 服务管理
```bash
# 查看服务状态
systemctl status nginx
systemctl status mysql
systemctl status redis
systemctl status chat-websocket
systemctl status chat-queue

# 重启服务
systemctl restart nginx
systemctl restart mysql
systemctl restart redis
systemctl restart chat-websocket
systemctl restart chat-queue
```

### Laravel 命令
```bash
# 清除缓存
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

# 重新缓存
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 数据库操作
php artisan migrate
php artisan migrate:rollback
php artisan db:seed

# 队列操作
php artisan queue:work
php artisan queue:restart
php artisan queue:failed
```

### 系统监控
```bash
# 查看系统资源
htop
free -h
df -h

# 查看网络连接
netstat -tulpn
ss -tulpn

# 查看日志
tail -f /var/log/messages
tail -f /www/wwwlogs/your-domain.com.log
tail -f /www/wwwroot/your-domain.com/storage/logs/laravel.log
```

### 备份恢复
```bash
# 数据库备份
mysqldump -u username -p database_name > backup.sql

# 数据库恢复
mysql -u username -p database_name < backup.sql

# 文件备份
tar -czf backup.tar.gz /path/to/files

# 文件恢复
tar -xzf backup.tar.gz -C /path/to/restore
```

---

## 故障排除快速指南

### WebSocket 连接问题
1. 检查防火墙端口 8080
2. 检查 WebSocket 服务状态
3. 检查 Nginx 代理配置
4. 查看 WebSocket 服务日志

### 数据库连接问题
1. 检查 MySQL 服务状态
2. 验证数据库连接信息
3. 检查数据库用户权限
4. 查看 MySQL 错误日志

### Redis 连接问题
1. 检查 Redis 服务状态
2. 验证 Redis 密码配置
3. 检查 PHP Redis 扩展
4. 查看 Redis 日志

### 文件权限问题
1. 检查项目文件所有者
2. 检查目录权限设置
3. 检查 SELinux 状态
4. 重新设置权限

### 性能问题
1. 检查系统资源使用
2. 分析慢查询日志
3. 检查缓存命中率
4. 优化数据库索引

---

**使用此清单确保部署过程完整无遗漏！** ✅

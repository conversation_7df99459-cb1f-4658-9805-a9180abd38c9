@echo off
chcp 65001 >nul
title 客服系统一键部署脚本
color 0A

echo.
echo ========================================
echo    客服系统一键部署脚本 v1.0
echo ========================================
echo.

:: 设置变量
set PROJECT_DIR=D:\wwwroot\aichat.jagship.com
set PHP_PATH=D:\BtSoft\php\72\php.exe
set COMPOSER_PATH=composer

echo [1/8] 检查环境...
if not exist "%PHP_PATH%" (
    echo ❌ PHP路径不存在，请检查宝塔PHP安装
    pause
    exit /b 1
)

echo ✅ PHP环境检查通过

echo.
echo [2/8] 配置Composer镜像...
%COMPOSER_PATH% config -g secure-http false
%COMPOSER_PATH% config -g repo.packagist composer http://packagist.phpcomposer.com
echo ✅ Composer镜像配置完成

echo.
echo [3/8] 备份现有项目（如果存在）...
if exist "%PROJECT_DIR%" (
    echo 发现现有项目，正在备份...
    ren "%PROJECT_DIR%" "aichat.jagship.com_backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
    echo ✅ 备份完成
) else (
    echo ✅ 无需备份
)

echo.
echo [4/8] 创建Laravel项目...
cd /d D:\wwwroot
%COMPOSER_PATH% create-project --prefer-dist laravel/laravel aichat.jagship.com "7.*" --no-interaction
if %errorlevel% neq 0 (
    echo ❌ Laravel项目创建失败
    pause
    exit /b 1
)
echo ✅ Laravel项目创建成功

echo.
echo [5/8] 安装额外依赖...
cd /d "%PROJECT_DIR%"
%COMPOSER_PATH% require laravel/sanctum predis/predis --no-interaction
echo ✅ 依赖安装完成

echo.
echo [6/8] 配置环境文件...
if not exist .env copy .env.example .env
"%PHP_PATH%" artisan key:generate --force
echo ✅ 环境配置完成

echo.
echo [7/8] 发布配置文件...
"%PHP_PATH%" artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider" --force
echo ✅ 配置文件发布完成

echo.
echo [8/8] 验证安装...
"%PHP_PATH%" artisan --version
if %errorlevel% neq 0 (
    echo ❌ Laravel安装验证失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo    🎉 基础安装完成！
echo ========================================
echo.
echo 项目路径: %PROJECT_DIR%
echo.
echo 下一步需要手动配置：
echo 1. 在宝塔面板中创建网站和数据库
echo 2. 编辑 .env 文件配置数据库连接
echo 3. 运行数据库迁移
echo.
echo 按任意键继续配置数据库...
pause >nul

echo.
echo ========================================
echo    数据库配置向导
echo ========================================
echo.

set /p DB_NAME="请输入数据库名称 (默认: aichat_jagship_com): "
if "%DB_NAME%"=="" set DB_NAME=aichat_jagship_com

set /p DB_USER="请输入数据库用户名 (默认: %DB_NAME%): "
if "%DB_USER%"=="" set DB_USER=%DB_NAME%

set /p DB_PASS="请输入数据库密码: "
if "%DB_PASS%"=="" (
    echo ❌ 数据库密码不能为空
    pause
    exit /b 1
)

echo.
echo 正在配置 .env 文件...

:: 创建临时配置文件
echo APP_NAME="Customer Service System" > .env.temp
echo APP_ENV=local >> .env.temp
echo APP_KEY= >> .env.temp
echo APP_DEBUG=true >> .env.temp
echo APP_URL=http://aichat.jagship.com >> .env.temp
echo. >> .env.temp
echo LOG_CHANNEL=stack >> .env.temp
echo LOG_LEVEL=debug >> .env.temp
echo. >> .env.temp
echo DB_CONNECTION=mysql >> .env.temp
echo DB_HOST=127.0.0.1 >> .env.temp
echo DB_PORT=3306 >> .env.temp
echo DB_DATABASE=%DB_NAME% >> .env.temp
echo DB_USERNAME=%DB_USER% >> .env.temp
echo DB_PASSWORD=%DB_PASS% >> .env.temp
echo. >> .env.temp
echo BROADCAST_DRIVER=log >> .env.temp
echo CACHE_DRIVER=redis >> .env.temp
echo FILESYSTEM_DRIVER=local >> .env.temp
echo QUEUE_CONNECTION=sync >> .env.temp
echo SESSION_DRIVER=redis >> .env.temp
echo SESSION_LIFETIME=120 >> .env.temp
echo. >> .env.temp
echo REDIS_HOST=127.0.0.1 >> .env.temp
echo REDIS_PASSWORD=null >> .env.temp
echo REDIS_PORT=6379 >> .env.temp

:: 替换原有的.env文件
move .env.temp .env

echo ✅ 数据库配置完成

echo.
echo [最后一步] 运行数据库迁移...
"%PHP_PATH%" artisan migrate --force
if %errorlevel% neq 0 (
    echo ⚠️ 数据库迁移失败，请检查数据库连接配置
    echo 您可以稍后手动运行: php artisan migrate
) else (
    echo ✅ 数据库迁移完成
)

echo.
echo ========================================
echo    🎉 部署完成！
echo ========================================
echo.
echo 项目已成功部署到: %PROJECT_DIR%
echo.
echo 接下来请：
echo 1. 在宝塔面板中配置网站指向 %PROJECT_DIR%\public
echo 2. 访问您的网站测试是否正常
echo.
echo 如需添加WebSocket功能，请运行: 添加WebSocket功能.bat
echo.
pause

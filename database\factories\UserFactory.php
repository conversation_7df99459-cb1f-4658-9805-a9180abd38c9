<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Site;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition()
    {
        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => Hash::make('password'),
            'remember_token' => Str::random(10),
            'role' => $this->faker->randomElement(['admin', 'supervisor', 'agent']),
            'status' => $this->faker->randomElement(['online', 'away', 'busy', 'offline']),
            'is_active' => true,
            'phone' => $this->faker->phoneNumber(),
            'avatar' => null,
            'timezone' => $this->faker->timezone(),
            'language' => $this->faker->randomElement(['en', 'zh', 'es', 'fr', 'de']),
            'max_concurrent_chats' => $this->faker->numberBetween(1, 5),
            'auto_accept_chats' => $this->faker->boolean(),
            'working_hours' => json_encode([
                'monday' => ['start' => '09:00', 'end' => '17:00'],
                'tuesday' => ['start' => '09:00', 'end' => '17:00'],
                'wednesday' => ['start' => '09:00', 'end' => '17:00'],
                'thursday' => ['start' => '09:00', 'end' => '17:00'],
                'friday' => ['start' => '09:00', 'end' => '17:00'],
                'saturday' => null,
                'sunday' => null,
            ]),
            'site_id' => Site::factory(),
            'last_login_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'failed_login_attempts' => 0,
        ];
    }

    public function unverified()
    {
        return $this->state(function (array $attributes) {
            return [
                'email_verified_at' => null,
            ];
        });
    }

    public function admin()
    {
        return $this->state(function (array $attributes) {
            return [
                'role' => 'admin',
            ];
        });
    }

    public function supervisor()
    {
        return $this->state(function (array $attributes) {
            return [
                'role' => 'supervisor',
            ];
        });
    }

    public function agent()
    {
        return $this->state(function (array $attributes) {
            return [
                'role' => 'agent',
            ];
        });
    }

    public function online()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'online',
            ];
        });
    }

    public function offline()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'offline',
            ];
        });
    }

    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }
}

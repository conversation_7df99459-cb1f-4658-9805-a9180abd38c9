<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class VisitorTracking extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tracking_id',
        'site_id',
        'visitor_id',
        'session_id',
        'page_url',
        'page_title',
        'referrer',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
        'visit_duration',
        'page_views',
        'scroll_depth',
        'clicks',
        'form_interactions',
        'exit_page',
        'bounce',
        'device_info',
        'browser_info',
        'screen_resolution',
        'viewport_size',
        'user_agent',
        'ip_address',
        'country',
        'city',
        'timezone',
        'language',
        'visited_at',
        'left_at',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'visit_duration' => 'integer',
        'page_views' => 'integer',
        'scroll_depth' => 'integer',
        'clicks' => 'integer',
        'form_interactions' => 'integer',
        'bounce' => 'boolean',
        'device_info' => 'array',
        'browser_info' => 'array',
        'screen_resolution' => 'array',
        'viewport_size' => 'array',
        'visited_at' => 'datetime',
        'left_at' => 'datetime',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($tracking) {
            if (!$tracking->tracking_id) {
                $tracking->tracking_id = Str::uuid();
            }
            if (!$tracking->visited_at) {
                $tracking->visited_at = now();
            }
        });
    }

    /**
     * Get the site that owns the tracking record
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Get the visitor that owns the tracking record
     */
    public function visitor()
    {
        return $this->belongsTo(Visitor::class);
    }

    /**
     * Get the mouse tracking data for this visit
     */
    public function mouseTracking()
    {
        return $this->hasMany(MouseTracking::class, 'visitor_tracking_id');
    }

    /**
     * Get the click events for this visit
     */
    public function clickEvents()
    {
        return $this->hasMany(ClickEvent::class, 'visitor_tracking_id');
    }

    /**
     * Get the form events for this visit
     */
    public function formEvents()
    {
        return $this->hasMany(FormEvent::class, 'visitor_tracking_id');
    }

    /**
     * Update visit duration
     */
    public function updateDuration()
    {
        if ($this->visited_at && $this->left_at) {
            $this->visit_duration = $this->visited_at->diffInSeconds($this->left_at);
            $this->save();
        }
        
        return $this;
    }

    /**
     * Mark as bounced visit
     */
    public function markAsBounce()
    {
        $this->update(['bounce' => true]);
        return $this;
    }

    /**
     * Update scroll depth
     */
    public function updateScrollDepth($depth)
    {
        if ($depth > $this->scroll_depth) {
            $this->update(['scroll_depth' => $depth]);
        }
        
        return $this;
    }

    /**
     * Increment page views
     */
    public function incrementPageViews()
    {
        $this->increment('page_views');
        return $this;
    }

    /**
     * Increment clicks
     */
    public function incrementClicks()
    {
        $this->increment('clicks');
        return $this;
    }

    /**
     * Increment form interactions
     */
    public function incrementFormInteractions()
    {
        $this->increment('form_interactions');
        return $this;
    }

    /**
     * Get formatted visit duration
     */
    public function getFormattedDurationAttribute()
    {
        if (!$this->visit_duration) {
            return '0s';
        }

        $hours = floor($this->visit_duration / 3600);
        $minutes = floor(($this->visit_duration % 3600) / 60);
        $seconds = $this->visit_duration % 60;

        if ($hours > 0) {
            return "{$hours}h {$minutes}m {$seconds}s";
        } elseif ($minutes > 0) {
            return "{$minutes}m {$seconds}s";
        } else {
            return "{$seconds}s";
        }
    }

    /**
     * Get device type
     */
    public function getDeviceTypeAttribute()
    {
        return $this->device_info['type'] ?? 'unknown';
    }

    /**
     * Get browser name
     */
    public function getBrowserNameAttribute()
    {
        return $this->browser_info['name'] ?? 'unknown';
    }

    /**
     * Get operating system
     */
    public function getOperatingSystemAttribute()
    {
        return $this->browser_info['os'] ?? 'unknown';
    }

    /**
     * Get screen resolution string
     */
    public function getScreenResolutionStringAttribute()
    {
        if (!$this->screen_resolution) {
            return 'unknown';
        }

        return ($this->screen_resolution['width'] ?? 0) . 'x' . ($this->screen_resolution['height'] ?? 0);
    }

    /**
     * Get viewport size string
     */
    public function getViewportSizeStringAttribute()
    {
        if (!$this->viewport_size) {
            return 'unknown';
        }

        return ($this->viewport_size['width'] ?? 0) . 'x' . ($this->viewport_size['height'] ?? 0);
    }

    /**
     * Check if visit is a bounce
     */
    public function isBounce()
    {
        return $this->bounce || ($this->page_views <= 1 && $this->visit_duration < 30);
    }

    /**
     * Get engagement score (0-100)
     */
    public function getEngagementScoreAttribute()
    {
        $score = 0;
        
        // Duration score (max 40 points)
        if ($this->visit_duration > 0) {
            $score += min(40, ($this->visit_duration / 300) * 40); // 5 minutes = max points
        }
        
        // Page views score (max 20 points)
        $score += min(20, $this->page_views * 5);
        
        // Scroll depth score (max 20 points)
        $score += ($this->scroll_depth / 100) * 20;
        
        // Interactions score (max 20 points)
        $interactionScore = ($this->clicks * 2) + ($this->form_interactions * 5);
        $score += min(20, $interactionScore);
        
        return round($score);
    }

    /**
     * Scope for bounced visits
     */
    public function scopeBounced($query)
    {
        return $query->where('bounce', true)
            ->orWhere(function ($q) {
                $q->where('page_views', '<=', 1)
                  ->where('visit_duration', '<', 30);
            });
    }

    /**
     * Scope for engaged visits
     */
    public function scopeEngaged($query, $minDuration = 60)
    {
        return $query->where('visit_duration', '>=', $minDuration)
            ->orWhere('page_views', '>', 1)
            ->orWhere('clicks', '>', 0)
            ->orWhere('form_interactions', '>', 0);
    }

    /**
     * Scope for visits by device type
     */
    public function scopeByDeviceType($query, $deviceType)
    {
        return $query->whereJsonContains('device_info->type', $deviceType);
    }

    /**
     * Scope for visits by browser
     */
    public function scopeByBrowser($query, $browser)
    {
        return $query->whereJsonContains('browser_info->name', $browser);
    }

    /**
     * Scope for visits by country
     */
    public function scopeByCountry($query, $country)
    {
        return $query->where('country', $country);
    }

    /**
     * Scope for visits in date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('visited_at', [$startDate, $endDate]);
    }

    /**
     * Scope for recent visits
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('visited_at', '>=', now()->subHours($hours));
    }

    /**
     * Convert to API array
     */
    public function toApiArray()
    {
        return [
            'id' => $this->id,
            'tracking_id' => $this->tracking_id,
            'page_url' => $this->page_url,
            'page_title' => $this->page_title,
            'referrer' => $this->referrer,
            'utm_source' => $this->utm_source,
            'utm_medium' => $this->utm_medium,
            'utm_campaign' => $this->utm_campaign,
            'visit_duration' => $this->visit_duration,
            'formatted_duration' => $this->formatted_duration,
            'page_views' => $this->page_views,
            'scroll_depth' => $this->scroll_depth,
            'clicks' => $this->clicks,
            'form_interactions' => $this->form_interactions,
            'bounce' => $this->isBounce(),
            'engagement_score' => $this->engagement_score,
            'device_type' => $this->device_type,
            'browser_name' => $this->browser_name,
            'operating_system' => $this->operating_system,
            'screen_resolution' => $this->screen_resolution_string,
            'viewport_size' => $this->viewport_size_string,
            'country' => $this->country,
            'city' => $this->city,
            'language' => $this->language,
            'visited_at' => $this->visited_at->toISOString(),
            'left_at' => $this->left_at ? $this->left_at->toISOString() : null,
        ];
    }
}

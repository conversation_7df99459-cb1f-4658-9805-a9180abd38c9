<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Database Optimization Settings
    |--------------------------------------------------------------------------
    */
    'database' => [
        'auto_optimize_tables' => env('DB_AUTO_OPTIMIZE', true),
        'slow_query_threshold' => env('DB_SLOW_QUERY_THRESHOLD', 1.0),
        'max_table_size_mb' => env('DB_MAX_TABLE_SIZE_MB', 1000),
        'index_optimization' => env('DB_INDEX_OPTIMIZATION', true),
        'statistics_update_frequency' => env('DB_STATS_UPDATE_FREQUENCY', 'weekly'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Optimization Settings
    |--------------------------------------------------------------------------
    */
    'cache' => [
        'auto_cleanup' => env('CACHE_AUTO_CLEANUP', true),
        'cleanup_frequency' => env('CACHE_CLEANUP_FREQUENCY', 'daily'),
        'memory_threshold_mb' => env('CACHE_MEMORY_THRESHOLD_MB', 512),
        'hit_ratio_threshold' => env('CACHE_HIT_RATIO_THRESHOLD', 80),
        'warm_up_caches' => env('CACHE_WARM_UP', true),
        'defragmentation' => env('CACHE_DEFRAGMENTATION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    */
    'security' => [
        'scan_frequency' => env('SECURITY_SCAN_FREQUENCY', 'weekly'),
        'auto_scan' => env('SECURITY_AUTO_SCAN', true),
        'security_score_threshold' => env('SECURITY_SCORE_THRESHOLD', 70),
        'alert_on_low_score' => env('SECURITY_ALERT_LOW_SCORE', true),
        'gdpr_compliance' => env('SECURITY_GDPR_COMPLIANCE', true),
        'log_data_processing' => env('SECURITY_LOG_DATA_PROCESSING', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Data Retention Settings
    |--------------------------------------------------------------------------
    */
    'data_retention' => [
        'visitor_behaviors_days' => env('RETENTION_VISITOR_BEHAVIORS', 90),
        'chat_sessions_days' => env('RETENTION_CHAT_SESSIONS', 365),
        'contact_messages_days' => env('RETENTION_CONTACT_MESSAGES', 1095), // 3 years
        'webhook_logs_days' => env('RETENTION_WEBHOOK_LOGS', 30),
        'export_files_days' => env('RETENTION_EXPORT_FILES', 7),
        'temp_files_days' => env('RETENTION_TEMP_FILES', 1),
        'log_files_days' => env('RETENTION_LOG_FILES', 30),
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Monitoring Settings
    |--------------------------------------------------------------------------
    */
    'monitoring' => [
        'enabled' => env('MONITORING_ENABLED', true),
        'frequency' => env('MONITORING_FREQUENCY', 'hourly'),
        'memory_threshold' => env('MONITORING_MEMORY_THRESHOLD', 80),
        'cpu_threshold' => env('MONITORING_CPU_THRESHOLD', 80),
        'disk_threshold' => env('MONITORING_DISK_THRESHOLD', 85),
        'response_time_threshold' => env('MONITORING_RESPONSE_TIME_THRESHOLD', 2000), // ms
        'alert_channels' => env('MONITORING_ALERT_CHANNELS', 'log,email'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting Settings
    |--------------------------------------------------------------------------
    */
    'rate_limiting' => [
        'api_requests_per_minute' => env('RATE_LIMIT_API', 60),
        'chat_messages_per_minute' => env('RATE_LIMIT_CHAT', 30),
        'file_uploads_per_hour' => env('RATE_LIMIT_UPLOADS', 10),
        'contact_forms_per_hour' => env('RATE_LIMIT_CONTACT', 5),
        'visitor_tracking_per_minute' => env('RATE_LIMIT_TRACKING', 100),
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Security
    |--------------------------------------------------------------------------
    */
    'file_security' => [
        'scan_uploads' => env('FILE_SCAN_UPLOADS', true),
        'allowed_extensions' => [
            'images' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'documents' => ['pdf', 'doc', 'docx', 'txt', 'rtf'],
            'archives' => ['zip', 'rar'],
        ],
        'max_file_size_mb' => env('FILE_MAX_SIZE_MB', 10),
        'virus_scan' => env('FILE_VIRUS_SCAN', false),
        'quarantine_suspicious' => env('FILE_QUARANTINE_SUSPICIOUS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Security
    |--------------------------------------------------------------------------
    */
    'session_security' => [
        'regenerate_on_login' => env('SESSION_REGENERATE_LOGIN', true),
        'timeout_minutes' => env('SESSION_TIMEOUT_MINUTES', 120),
        'concurrent_sessions' => env('SESSION_CONCURRENT_LIMIT', 3),
        'ip_validation' => env('SESSION_IP_VALIDATION', true),
        'user_agent_validation' => env('SESSION_USER_AGENT_VALIDATION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | API Security
    |--------------------------------------------------------------------------
    */
    'api_security' => [
        'require_https' => env('API_REQUIRE_HTTPS', true),
        'token_expiry_hours' => env('API_TOKEN_EXPIRY', 24),
        'refresh_token_expiry_days' => env('API_REFRESH_TOKEN_EXPIRY', 30),
        'cors_origins' => env('API_CORS_ORIGINS', '*'),
        'webhook_signature_validation' => env('API_WEBHOOK_SIGNATURE_VALIDATION', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup and Recovery
    |--------------------------------------------------------------------------
    */
    'backup' => [
        'enabled' => env('BACKUP_ENABLED', false),
        'frequency' => env('BACKUP_FREQUENCY', 'daily'),
        'retention_days' => env('BACKUP_RETENTION_DAYS', 30),
        'compress' => env('BACKUP_COMPRESS', true),
        'encrypt' => env('BACKUP_ENCRYPT', false),
        'storage_disk' => env('BACKUP_STORAGE_DISK', 'local'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'performance_log' => env('LOG_PERFORMANCE', true),
        'security_log' => env('LOG_SECURITY', true),
        'gdpr_log' => env('LOG_GDPR', true),
        'optimization_log' => env('LOG_OPTIMIZATION', true),
        'log_level' => env('LOG_LEVEL', 'info'),
        'max_log_size_mb' => env('LOG_MAX_SIZE_MB', 100),
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Settings
    |--------------------------------------------------------------------------
    */
    'notifications' => [
        'security_alerts' => env('NOTIFY_SECURITY_ALERTS', true),
        'performance_alerts' => env('NOTIFY_PERFORMANCE_ALERTS', true),
        'optimization_reports' => env('NOTIFY_OPTIMIZATION_REPORTS', false),
        'data_cleanup_reports' => env('NOTIFY_CLEANUP_REPORTS', false),
        'channels' => [
            'email' => env('NOTIFY_EMAIL', true),
            'slack' => env('NOTIFY_SLACK', false),
            'webhook' => env('NOTIFY_WEBHOOK', false),
        ],
        'recipients' => [
            'admin_email' => env('ADMIN_EMAIL', '<EMAIL>'),
            'security_email' => env('SECURITY_EMAIL', '<EMAIL>'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Flags
    |--------------------------------------------------------------------------
    */
    'features' => [
        'auto_optimization' => env('FEATURE_AUTO_OPTIMIZATION', true),
        'real_time_monitoring' => env('FEATURE_REAL_TIME_MONITORING', true),
        'advanced_security_scan' => env('FEATURE_ADVANCED_SECURITY', false),
        'ai_performance_tuning' => env('FEATURE_AI_PERFORMANCE_TUNING', false),
        'predictive_scaling' => env('FEATURE_PREDICTIVE_SCALING', false),
    ],
];

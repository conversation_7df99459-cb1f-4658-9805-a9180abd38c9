<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <!-- Loading overlay -->
    <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span class="text-gray-700">{{ loadingMessage }}</span>
      </div>
    </div>

    <!-- Main app content -->
    <router-view />

    <!-- Global notifications -->
    <NotificationContainer />

    <!-- Chat widget (for visitor pages) -->
    <ChatWidget v-if="showChatWidget" />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from './stores/auth.js'
import { useNotificationStore } from './stores/notifications.js'
import NotificationContainer from './components/common/NotificationContainer.vue'
import ChatWidget from './components/chat/ChatWidget.vue'

export default {
  name: 'App',
  components: {
    NotificationContainer,
    ChatWidget
  },
  setup() {
    const router = useRouter()
    const authStore = useAuthStore()
    const notificationStore = useNotificationStore()

    const isLoading = ref(false)
    const loadingMessage = ref('Loading...')

    // Show chat widget only on visitor pages (not admin pages)
    const showChatWidget = computed(() => {
      return !router.currentRoute.value.path.startsWith('/admin') && 
             !router.currentRoute.value.path.startsWith('/login')
    })

    // Initialize app
    onMounted(async () => {
      try {
        isLoading.value = true
        loadingMessage.value = 'Initializing application...'

        // Check if user is already authenticated
        const token = localStorage.getItem('auth_token')
        if (token) {
          await authStore.fetchUser()
        }

        // Initialize WebSocket connection if authenticated
        if (authStore.isAuthenticated) {
          await authStore.initializeWebSocket()
        }

      } catch (error) {
        console.error('App initialization error:', error)
        notificationStore.addNotification({
          type: 'error',
          title: 'Initialization Error',
          message: 'Failed to initialize application. Please refresh the page.'
        })
      } finally {
        isLoading.value = false
      }
    })

    // Global error handler
    const handleGlobalError = (error) => {
      console.error('Global error:', error)
      notificationStore.addNotification({
        type: 'error',
        title: 'Error',
        message: error.message || 'An unexpected error occurred'
      })
    }

    // Set global loading state
    const setLoading = (loading, message = 'Loading...') => {
      isLoading.value = loading
      loadingMessage.value = message
    }

    // Provide global methods
    window.app = {
      setLoading,
      handleError: handleGlobalError
    }

    return {
      isLoading,
      loadingMessage,
      showChatWidget
    }
  }
}
</script>

<style>
/* Global styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin: 0;
  padding: 0;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Transitions */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-enter-active, .slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}

/* Custom animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Focus styles */
.focus-ring:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px #3b82f6;
}

/* Button styles */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-warning {
  @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn-outline {
  @apply bg-transparent border-gray-300 text-gray-700 hover:bg-gray-50;
}

/* Form styles */
.form-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.form-textarea {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-vertical;
}

.form-select {
  @apply block w-full px-3 py-2 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500;
}

.form-checkbox {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
}

.form-radio {
  @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300;
}

/* Card styles */
.card {
  @apply bg-white overflow-hidden shadow rounded-lg;
}

.card-header {
  @apply px-4 py-5 sm:px-6 border-b border-gray-200;
}

.card-body {
  @apply px-4 py-5 sm:p-6;
}

.card-footer {
  @apply px-4 py-4 sm:px-6 bg-gray-50 border-t border-gray-200;
}

/* Badge styles */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
  @apply bg-blue-100 text-blue-800;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
  @apply bg-red-100 text-red-800;
}

.badge-gray {
  @apply bg-gray-100 text-gray-800;
}

/* Utility classes */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.shadow-soft {
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.05);
}

.shadow-medium {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-strong {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}
</style>

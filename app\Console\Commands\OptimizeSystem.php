<?php

namespace App\Console\Commands;

use App\Services\DatabaseOptimizationService;
use App\Services\CacheOptimizationService;
use App\Services\SecurityScanService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class OptimizeSystem extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'system:optimize 
                            {--database : Optimize database only}
                            {--cache : Optimize cache only}
                            {--security : Run security scan only}
                            {--all : Run all optimizations (default)}
                            {--report : Generate detailed report}';

    /**
     * The console command description.
     */
    protected $description = 'Optimize system performance and security';

    protected $databaseService;
    protected $cacheService;
    protected $securityService;

    /**
     * Create a new command instance.
     */
    public function __construct(
        DatabaseOptimizationService $databaseService,
        CacheOptimizationService $cacheService,
        SecurityScanService $securityService
    ) {
        parent::__construct();
        $this->databaseService = $databaseService;
        $this->cacheService = $cacheService;
        $this->securityService = $securityService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting system optimization...');
        $startTime = microtime(true);

        $results = [];
        $options = $this->options();

        // Determine what to run
        $runAll = $options['all'] || (!$options['database'] && !$options['cache'] && !$options['security']);

        if ($runAll || $options['database']) {
            $results['database'] = $this->optimizeDatabase();
        }

        if ($runAll || $options['cache']) {
            $results['cache'] = $this->optimizeCache();
        }

        if ($runAll || $options['security']) {
            $results['security'] = $this->runSecurityScan();
        }

        // Run Laravel optimizations
        if ($runAll) {
            $results['laravel'] = $this->runLaravelOptimizations();
        }

        $executionTime = round(microtime(true) - $startTime, 2);
        $this->info("System optimization completed in {$executionTime} seconds");

        // Generate report if requested
        if ($options['report']) {
            $this->generateOptimizationReport($results, $executionTime);
        }

        // Display summary
        $this->displaySummary($results);
    }

    /**
     * Optimize database
     */
    protected function optimizeDatabase(): array
    {
        $this->info('Optimizing database...');
        
        try {
            $results = $this->databaseService->optimizeDatabase();
            $this->line('✓ Database optimization completed');
            return $results;
        } catch (\Exception $e) {
            $this->error('✗ Database optimization failed: ' . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Optimize cache
     */
    protected function optimizeCache(): array
    {
        $this->info('Optimizing cache...');
        
        try {
            $results = $this->cacheService->optimizeCache();
            $this->line('✓ Cache optimization completed');
            return $results;
        } catch (\Exception $e) {
            $this->error('✗ Cache optimization failed: ' . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Run security scan
     */
    protected function runSecurityScan(): array
    {
        $this->info('Running security scan...');
        
        try {
            $results = $this->securityService->performSecurityScan();
            $score = $results['security_score'] ?? 0;
            
            if ($score >= 80) {
                $this->line("✓ Security scan completed - Score: {$score}/100 (Good)");
            } elseif ($score >= 60) {
                $this->warn("⚠ Security scan completed - Score: {$score}/100 (Fair)");
            } else {
                $this->error("✗ Security scan completed - Score: {$score}/100 (Poor)");
            }
            
            return $results;
        } catch (\Exception $e) {
            $this->error('✗ Security scan failed: ' . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Run Laravel optimizations
     */
    protected function runLaravelOptimizations(): array
    {
        $this->info('Running Laravel optimizations...');
        $results = [];

        try {
            // Clear and cache config
            Artisan::call('config:clear');
            Artisan::call('config:cache');
            $results['config'] = 'cached';

            // Clear and cache routes
            Artisan::call('route:clear');
            Artisan::call('route:cache');
            $results['routes'] = 'cached';

            // Clear and cache views
            Artisan::call('view:clear');
            Artisan::call('view:cache');
            $results['views'] = 'cached';

            // Optimize autoloader
            $this->call('optimize');
            $results['autoloader'] = 'optimized';

            // Clear application cache
            Artisan::call('cache:clear');
            $results['application_cache'] = 'cleared';

            $this->line('✓ Laravel optimizations completed');

        } catch (\Exception $e) {
            $this->error('✗ Laravel optimizations failed: ' . $e->getMessage());
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Generate optimization report
     */
    protected function generateOptimizationReport(array $results, float $executionTime): void
    {
        $this->info('Generating optimization report...');

        $report = [
            'timestamp' => now()->toISOString(),
            'execution_time_seconds' => $executionTime,
            'results' => $results,
            'summary' => $this->generateSummary($results),
            'recommendations' => $this->generateRecommendations($results)
        ];

        $filename = 'optimization_report_' . now()->format('Y-m-d_H-i-s') . '.json';
        $path = storage_path('logs/' . $filename);
        
        file_put_contents($path, json_encode($report, JSON_PRETTY_PRINT));
        
        $this->line("Report saved to: {$path}");
    }

    /**
     * Display optimization summary
     */
    protected function displaySummary(array $results): void
    {
        $this->info('=== Optimization Summary ===');

        $summary = [];

        if (isset($results['database'])) {
            $dbResults = $results['database'];
            if (isset($dbResults['error'])) {
                $summary[] = ['Database', 'Failed', $dbResults['error']];
            } else {
                $indexCount = count($dbResults['indexes'] ?? []);
                $summary[] = ['Database', 'Optimized', "{$indexCount} indexes processed"];
            }
        }

        if (isset($results['cache'])) {
            $cacheResults = $results['cache'];
            if (isset($cacheResults['error'])) {
                $summary[] = ['Cache', 'Failed', $cacheResults['error']];
            } else {
                $memorySaved = $cacheResults['cleanup']['memory_saved_mb'] ?? 0;
                $summary[] = ['Cache', 'Optimized', "{$memorySaved}MB memory saved"];
            }
        }

        if (isset($results['security'])) {
            $securityResults = $results['security'];
            if (isset($securityResults['error'])) {
                $summary[] = ['Security', 'Failed', $securityResults['error']];
            } else {
                $score = $securityResults['security_score'] ?? 0;
                $summary[] = ['Security', 'Scanned', "Score: {$score}/100"];
            }
        }

        if (isset($results['laravel'])) {
            $laravelResults = $results['laravel'];
            if (isset($laravelResults['error'])) {
                $summary[] = ['Laravel', 'Failed', $laravelResults['error']];
            } else {
                $optimizations = count(array_filter($laravelResults, function($v) { return $v !== 'error'; }));
                $summary[] = ['Laravel', 'Optimized', "{$optimizations} components cached"];
            }
        }

        $this->table(['Component', 'Status', 'Details'], $summary);
    }

    /**
     * Generate summary for report
     */
    protected function generateSummary(array $results): array
    {
        $summary = [
            'total_components' => count($results),
            'successful_optimizations' => 0,
            'failed_optimizations' => 0,
            'warnings' => 0
        ];

        foreach ($results as $component => $result) {
            if (isset($result['error'])) {
                $summary['failed_optimizations']++;
            } else {
                $summary['successful_optimizations']++;
            }

            // Count warnings (security issues, etc.)
            if (isset($result['security_score']) && $result['security_score'] < 70) {
                $summary['warnings']++;
            }
        }

        return $summary;
    }

    /**
     * Generate recommendations
     */
    protected function generateRecommendations(array $results): array
    {
        $recommendations = [];

        // Database recommendations
        if (isset($results['database']) && !isset($results['database']['error'])) {
            $recommendations[] = 'Schedule regular database optimization (weekly)';
            $recommendations[] = 'Monitor slow queries and add indexes as needed';
        }

        // Cache recommendations
        if (isset($results['cache']) && !isset($results['cache']['error'])) {
            $cacheStats = $results['cache']['performance_analysis'] ?? [];
            $hitRatio = $cacheStats['hit_ratio'] ?? 0;
            
            if ($hitRatio < 80) {
                $recommendations[] = 'Improve cache hit ratio by optimizing cache keys';
            }
            
            $recommendations[] = 'Schedule regular cache cleanup (daily)';
        }

        // Security recommendations
        if (isset($results['security']) && !isset($results['security']['error'])) {
            $score = $results['security']['security_score'] ?? 0;
            
            if ($score < 80) {
                $recommendations[] = 'Address security issues to improve security score';
                $recommendations[] = 'Schedule regular security scans (weekly)';
            }
        }

        // General recommendations
        $recommendations[] = 'Monitor system performance metrics regularly';
        $recommendations[] = 'Set up automated optimization tasks';
        $recommendations[] = 'Review and update security policies quarterly';

        return array_unique($recommendations);
    }
}

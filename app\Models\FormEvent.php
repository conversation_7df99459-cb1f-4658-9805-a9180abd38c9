<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class FormEvent extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'event_id',
        'site_id',
        'visitor_id',
        'visitor_tracking_id',
        'session_id',
        'page_url',
        'form_id',
        'form_name',
        'form_action',
        'form_method',
        'field_name',
        'field_type',
        'field_label',
        'event_type',
        'field_value',
        'previous_value',
        'validation_errors',
        'timestamp',
        'time_spent',
        'keystroke_count',
        'focus_count',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'validation_errors' => 'array',
        'timestamp' => 'integer',
        'time_spent' => 'integer',
        'keystroke_count' => 'integer',
        'focus_count' => 'integer',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($event) {
            if (!$event->event_id) {
                $event->event_id = Str::uuid();
            }
        });
    }

    /**
     * Get the site that owns the form event
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Get the visitor that owns the form event
     */
    public function visitor()
    {
        return $this->belongsTo(Visitor::class);
    }

    /**
     * Get the visitor tracking record
     */
    public function visitorTracking()
    {
        return $this->belongsTo(VisitorTracking::class);
    }

    /**
     * Get form display name
     */
    public function getFormDisplayNameAttribute()
    {
        if ($this->form_name) {
            return $this->form_name;
        }

        if ($this->form_id) {
            return 'Form #' . $this->form_id;
        }

        return 'Unnamed Form';
    }

    /**
     * Get field display name
     */
    public function getFieldDisplayNameAttribute()
    {
        if ($this->field_label) {
            return $this->field_label;
        }

        if ($this->field_name) {
            return ucfirst(str_replace(['_', '-'], ' ', $this->field_name));
        }

        return 'Unnamed Field';
    }

    /**
     * Get time spent in seconds
     */
    public function getTimeSpentSecondsAttribute()
    {
        return $this->time_spent ? round($this->time_spent / 1000, 2) : 0;
    }

    /**
     * Get typing speed (characters per minute)
     */
    public function getTypingSpeedAttribute()
    {
        if (!$this->time_spent || !$this->keystroke_count) {
            return 0;
        }

        $minutes = $this->time_spent / 60000; // Convert to minutes
        return round($this->keystroke_count / $minutes, 2);
    }

    /**
     * Check if field has validation errors
     */
    public function hasValidationErrors()
    {
        return !empty($this->validation_errors);
    }

    /**
     * Check if field was abandoned (focused but not completed)
     */
    public function isAbandoned()
    {
        return $this->event_type === 'blur' && 
               empty($this->field_value) && 
               $this->focus_count > 0;
    }

    /**
     * Check if field was corrected (value changed multiple times)
     */
    public function wasCorrected()
    {
        return $this->event_type === 'change' && 
               !empty($this->previous_value) && 
               $this->previous_value !== $this->field_value;
    }

    /**
     * Get field completion rate for a form
     */
    public static function getFieldCompletionRate($siteId, $formId, $startDate = null, $endDate = null)
    {
        $query = self::where('site_id', $siteId)
            ->where('form_id', $formId);

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        $totalFields = $query->distinct('field_name')->count();
        $completedFields = $query->where('event_type', 'submit')
            ->orWhere(function ($q) {
                $q->where('event_type', 'change')
                  ->whereNotNull('field_value')
                  ->where('field_value', '!=', '');
            })
            ->distinct('field_name')
            ->count();

        return $totalFields > 0 ? round(($completedFields / $totalFields) * 100, 2) : 0;
    }

    /**
     * Get form abandonment rate
     */
    public static function getFormAbandonmentRate($siteId, $formId, $startDate = null, $endDate = null)
    {
        $query = self::where('site_id', $siteId)
            ->where('form_id', $formId);

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        $totalSessions = $query->distinct('session_id')->count();
        $submittedSessions = $query->where('event_type', 'submit')
            ->distinct('session_id')
            ->count();

        $abandonedSessions = $totalSessions - $submittedSessions;
        
        return $totalSessions > 0 ? round(($abandonedSessions / $totalSessions) * 100, 2) : 0;
    }

    /**
     * Get most problematic fields (high abandonment, validation errors)
     */
    public static function getProblematicFields($siteId, $formId, $limit = 10, $startDate = null, $endDate = null)
    {
        $query = self::where('site_id', $siteId)
            ->where('form_id', $formId);

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        return $query->selectRaw('
                field_name,
                field_label,
                field_type,
                COUNT(*) as total_interactions,
                SUM(CASE WHEN event_type = "blur" AND (field_value IS NULL OR field_value = "") THEN 1 ELSE 0 END) as abandonment_count,
                SUM(CASE WHEN validation_errors IS NOT NULL AND JSON_LENGTH(validation_errors) > 0 THEN 1 ELSE 0 END) as error_count,
                AVG(time_spent) as avg_time_spent,
                AVG(keystroke_count) as avg_keystrokes
            ')
            ->groupBy(['field_name', 'field_label', 'field_type'])
            ->orderByRaw('(abandonment_count + error_count) DESC')
            ->limit($limit)
            ->get();
    }

    /**
     * Scope for event type
     */
    public function scopeEventType($query, $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope for form ID
     */
    public function scopeForForm($query, $formId)
    {
        return $query->where('form_id', $formId);
    }

    /**
     * Scope for field name
     */
    public function scopeForField($query, $fieldName)
    {
        return $query->where('field_name', $fieldName);
    }

    /**
     * Scope for abandoned fields
     */
    public function scopeAbandoned($query)
    {
        return $query->where('event_type', 'blur')
            ->where(function ($q) {
                $q->whereNull('field_value')
                  ->orWhere('field_value', '');
            })
            ->where('focus_count', '>', 0);
    }

    /**
     * Scope for fields with validation errors
     */
    public function scopeWithErrors($query)
    {
        return $query->whereNotNull('validation_errors')
            ->whereRaw('JSON_LENGTH(validation_errors) > 0');
    }

    /**
     * Scope for completed fields
     */
    public function scopeCompleted($query)
    {
        return $query->where('event_type', 'submit')
            ->orWhere(function ($q) {
                $q->where('event_type', 'change')
                  ->whereNotNull('field_value')
                  ->where('field_value', '!=', '');
            });
    }

    /**
     * Scope for page URL
     */
    public function scopeForPage($query, $pageUrl)
    {
        return $query->where('page_url', $pageUrl);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for recent events
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Convert to API array
     */
    public function toApiArray()
    {
        return [
            'id' => $this->id,
            'event_id' => $this->event_id,
            'page_url' => $this->page_url,
            'form' => [
                'id' => $this->form_id,
                'name' => $this->form_name,
                'display_name' => $this->form_display_name,
                'action' => $this->form_action,
                'method' => $this->form_method,
            ],
            'field' => [
                'name' => $this->field_name,
                'type' => $this->field_type,
                'label' => $this->field_label,
                'display_name' => $this->field_display_name,
            ],
            'event_type' => $this->event_type,
            'field_value' => $this->field_value,
            'previous_value' => $this->previous_value,
            'validation_errors' => $this->validation_errors,
            'timestamp' => $this->timestamp,
            'time_spent' => $this->time_spent,
            'time_spent_seconds' => $this->time_spent_seconds,
            'keystroke_count' => $this->keystroke_count,
            'focus_count' => $this->focus_count,
            'typing_speed' => $this->typing_speed,
            'has_validation_errors' => $this->hasValidationErrors(),
            'is_abandoned' => $this->isAbandoned(),
            'was_corrected' => $this->wasCorrected(),
            'created_at' => $this->created_at->toISOString(),
        ];
    }
}

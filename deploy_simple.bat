@echo off
title Simple Deploy Script
color 0A

echo.
echo ==========================================
echo    Simple Deploy Script
echo ==========================================
echo.

echo Step 1: Find PHP
set PHP_FOUND=0

if exist "D:\BtSoft\php\72\php.exe" (
    echo [OK] Found PHP 7.2
    set PHP_PATH=D:\BtSoft\php\72\php.exe
    set PHP_FOUND=1
)

if exist "D:\BtSoft\php\73\php.exe" (
    echo [OK] Found PHP 7.3
    if %PHP_FOUND%==0 set PHP_PATH=D:\BtSoft\php\73\php.exe
    set PHP_FOUND=1
)

if exist "D:\BtSoft\php\74\php.exe" (
    echo [OK] Found PHP 7.4
    if %PHP_FOUND%==0 set PHP_PATH=D:\BtSoft\php\74\php.exe
    set PHP_FOUND=1
)

if %PHP_FOUND%==0 (
    echo [ERROR] PHP not found!
    echo Please install BT Panel and PHP first
    pause
    exit /b 1
)

echo Using PHP: %PHP_PATH%
echo.

echo Step 2: Check Composer
composer --version >nul 2>&1
if %errorlevel%==0 (
    echo [OK] Composer is available
    set COMPOSER_CMD=composer
) else (
    echo [INFO] System Composer not found, trying to download...
    
    powershell -Command "try { Invoke-WebRequest -Uri 'https://getcomposer.org/composer.phar' -OutFile 'composer.phar' } catch { exit 1 }"
    
    if exist composer.phar (
        echo [OK] Downloaded composer.phar
        set COMPOSER_CMD="%PHP_PATH%" composer.phar
    ) else (
        echo [ERROR] Failed to download Composer
        echo Please install Composer manually
        pause
        exit /b 1
    )
)

echo.
echo Step 3: Configure Composer
%COMPOSER_CMD% config -g secure-http false
%COMPOSER_CMD% config -g repo.packagist composer http://packagist.phpcomposer.com
echo [OK] Composer configured

echo.
echo Step 4: Install Dependencies
if not exist vendor (
    echo Installing dependencies... This may take several minutes...
    %COMPOSER_CMD% install --no-dev --optimize-autoloader
    
    if %errorlevel% neq 0 (
        echo [WARN] Install failed, trying with ignore platform reqs...
        %COMPOSER_CMD% install --no-dev --optimize-autoloader --ignore-platform-reqs
        
        if %errorlevel% neq 0 (
            echo [ERROR] Dependencies installation failed
            pause
            exit /b 1
        )
    )
    echo [OK] Dependencies installed
) else (
    echo [INFO] Dependencies already installed
)

echo.
echo Step 5: Setup Laravel
if not exist .env (
    if exist .env.example (
        copy .env.example .env
        echo [OK] Created .env file
    ) else (
        echo [ERROR] .env.example not found
        pause
        exit /b 1
    )
)

"%PHP_PATH%" artisan key:generate --force
if %errorlevel% neq 0 (
    echo [ERROR] Failed to generate app key
    pause
    exit /b 1
)
echo [OK] App key generated

echo.
echo Step 6: Test Laravel
"%PHP_PATH%" artisan --version
if %errorlevel% neq 0 (
    echo [ERROR] Laravel test failed
    pause
    exit /b 1
)
echo [OK] Laravel is working

echo.
echo ==========================================
echo    Deploy Complete!
echo ==========================================
echo.
echo Next steps:
echo 1. Create website in BT Panel
echo 2. Set document root to: %CD%\public
echo 3. Configure database in .env file
echo 4. Run: php artisan migrate
echo.
echo Press any key to exit...
pause >nul

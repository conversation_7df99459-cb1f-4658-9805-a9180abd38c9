<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('st_languages', function (Blueprint $table) {
            $table->id();
            $table->string('language_id')->unique();
            $table->unsignedBigInteger('site_id');
            $table->string('code', 10); // Language code (e.g., 'en', 'zh', 'es')
            $table->string('name'); // English name (e.g., 'English', 'Chinese')
            $table->string('native_name'); // Native name (e.g., 'English', '中文')
            $table->string('flag_icon', 10)->nullable(); // Flag emoji or icon
            $table->boolean('is_active')->default(true);
            $table->boolean('is_default')->default(false);
            $table->boolean('is_rtl')->default(false); // Right-to-left language
            
            // Formatting settings
            $table->string('date_format', 50)->default('Y-m-d');
            $table->string('time_format', 50)->default('H:i:s');
            $table->string('currency_code', 3)->nullable(); // ISO currency code
            $table->string('currency_symbol', 10)->nullable();
            $table->string('number_format', 50)->default('1,234.56');
            $table->string('timezone', 100)->default('UTC');
            
            // Additional metadata
            $table->json('metadata')->nullable();
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['site_id', 'is_active']);
            $table->index(['site_id', 'is_default']);
            $table->index(['site_id', 'code']);
            $table->index('language_id');
            
            // Unique constraint: only one default language per site
            $table->unique(['site_id', 'code']);
            
            // Foreign key constraint
            $table->foreign('site_id')->references('id')->on('st_sites')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('st_languages');
    }
};

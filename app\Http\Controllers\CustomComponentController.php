<?php

namespace App\Http\Controllers;

use App\Services\CustomComponentService;
use App\Models\CustomComponent;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class CustomComponentController extends Controller
{
    protected $componentService;

    public function __construct(CustomComponentService $componentService)
    {
        $this->componentService = $componentService;
    }

    /**
     * Get all components for a site
     */
    public function index(Request $request, $siteId = null): JsonResponse
    {
        try {
            $query = CustomComponent::query();
            
            if ($siteId) {
                $query->where('site_id', $siteId);
            }
            
            if ($request->has('position')) {
                $query->byPosition($request->position);
            }
            
            if ($request->has('type')) {
                $query->byType($request->type);
            }
            
            if ($request->has('active')) {
                if ($request->boolean('active')) {
                    $query->active();
                } else {
                    $query->where('is_active', false);
                }
            }

            $components = $query->orderBy('position')
                ->orderBy('order_index')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $components,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get components: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get components for specific position
     */
    public function getByPosition(Request $request, $siteId, $position): JsonResponse
    {
        try {
            $context = $request->get('context', []);
            $components = $this->componentService->getComponentsForPosition($siteId, $position, $context);

            return response()->json([
                'success' => true,
                'data' => $components,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get components: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get component by ID
     */
    public function show($componentId): JsonResponse
    {
        try {
            $component = CustomComponent::where('component_id', $componentId)->firstOrFail();

            return response()->json([
                'success' => true,
                'data' => $component,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Component not found',
            ], 404);
        }
    }

    /**
     * Create new component
     */
    public function store(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string|in:' . implode(',', array_keys(CustomComponent::getAvailableTypes())),
            'position' => 'required|string|in:' . implode(',', array_keys(CustomComponent::getAvailablePositions())),
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
            'order_index' => 'nullable|integer|min:0',
            'settings' => 'nullable|array',
            'styles' => 'nullable|array',
            'content' => 'nullable|string',
            'conditions' => 'nullable|array',
        ]);

        try {
            // Validate component settings
            if ($request->has('settings')) {
                $this->componentService->validateComponentSettings($request->type, $request->settings);
            }

            $component = $this->componentService->createComponent($siteId, $request->all());

            return response()->json([
                'success' => true,
                'data' => $component,
                'message' => 'Component created successfully',
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create component: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update component
     */
    public function update(Request $request, $componentId): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'type' => 'sometimes|required|string|in:' . implode(',', array_keys(CustomComponent::getAvailableTypes())),
            'position' => 'sometimes|required|string|in:' . implode(',', array_keys(CustomComponent::getAvailablePositions())),
            'description' => 'nullable|string|max:1000',
            'is_active' => 'boolean',
            'order_index' => 'nullable|integer|min:0',
            'settings' => 'nullable|array',
            'styles' => 'nullable|array',
            'content' => 'nullable|string',
            'conditions' => 'nullable|array',
        ]);

        try {
            // Validate component settings if provided
            if ($request->has('settings') && $request->has('type')) {
                $this->componentService->validateComponentSettings($request->type, $request->settings);
            }

            $component = $this->componentService->updateComponent($componentId, $request->all());

            return response()->json([
                'success' => true,
                'data' => $component,
                'message' => 'Component updated successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update component: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Toggle component active status
     */
    public function toggle($componentId): JsonResponse
    {
        try {
            $component = $this->componentService->toggleComponent($componentId);

            return response()->json([
                'success' => true,
                'data' => $component,
                'message' => 'Component status updated successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle component: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Clone component
     */
    public function clone(Request $request, $componentId): JsonResponse
    {
        $request->validate([
            'name' => 'nullable|string|max:255',
        ]);

        try {
            $component = $this->componentService->cloneComponent($componentId, $request->name);

            return response()->json([
                'success' => true,
                'data' => $component,
                'message' => 'Component cloned successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clone component: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reorder components
     */
    public function reorder(Request $request, $siteId, $position): JsonResponse
    {
        $request->validate([
            'component_ids' => 'required|array',
            'component_ids.*' => 'required|string',
        ]);

        try {
            $this->componentService->reorderComponents($siteId, $position, $request->component_ids);

            return response()->json([
                'success' => true,
                'message' => 'Components reordered successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder components: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete component
     */
    public function destroy($componentId): JsonResponse
    {
        try {
            $this->componentService->deleteComponent($componentId);

            return response()->json([
                'success' => true,
                'message' => 'Component deleted successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete component: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Render components for position
     */
    public function render(Request $request, $siteId, $position): JsonResponse
    {
        try {
            $context = $request->get('context', []);
            $html = $this->componentService->renderComponentsForPosition($siteId, $position, $context);

            return response()->json([
                'success' => true,
                'data' => [
                    'html' => $html,
                    'position' => $position,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to render components: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate CSS for site components
     */
    public function generateCss($siteId): JsonResponse
    {
        try {
            $css = $this->componentService->generateSiteComponentsCss($siteId);

            return response()->json([
                'success' => true,
                'data' => [
                    'css' => $css,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate CSS: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available component types
     */
    public function getAvailableTypes(): JsonResponse
    {
        try {
            $types = CustomComponent::getAvailableTypes();

            return response()->json([
                'success' => true,
                'data' => $types,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get component types: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available positions
     */
    public function getAvailablePositions(): JsonResponse
    {
        try {
            $positions = CustomComponent::getAvailablePositions();

            return response()->json([
                'success' => true,
                'data' => $positions,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get positions: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get component templates
     */
    public function getTemplates(): JsonResponse
    {
        try {
            $templates = $this->componentService->getComponentTemplates();

            return response()->json([
                'success' => true,
                'data' => $templates,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get templates: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create component from template
     */
    public function createFromTemplate(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'template_key' => 'required|string',
            'name' => 'nullable|string|max:255',
            'position' => 'required|string|in:' . implode(',', array_keys(CustomComponent::getAvailablePositions())),
            'custom_data' => 'nullable|array',
        ]);

        try {
            $customData = $request->custom_data ?? [];
            $customData['position'] = $request->position;
            
            if ($request->has('name')) {
                $customData['name'] = $request->name;
            }

            $component = $this->componentService->createFromTemplate(
                $siteId, 
                $request->template_key, 
                $customData
            );

            return response()->json([
                'success' => true,
                'data' => $component,
                'message' => 'Component created from template successfully',
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create component from template: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export components
     */
    public function export($siteId): JsonResponse
    {
        try {
            $exportData = $this->componentService->exportComponents($siteId);

            return response()->json([
                'success' => true,
                'data' => $exportData,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export components: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Import components
     */
    public function import(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'import_data' => 'required|array',
            'import_data.components' => 'required|array',
        ]);

        try {
            $components = $this->componentService->importComponents($siteId, $request->import_data);

            return response()->json([
                'success' => true,
                'data' => $components,
                'message' => 'Components imported successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to import components: ' . $e->getMessage(),
            ], 500);
        }
    }
}

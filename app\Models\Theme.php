<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Theme extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'st_themes';

    protected $fillable = [
        'theme_id',
        'site_id',
        'name',
        'description',
        'is_active',
        'is_default',
        'color_scheme',
        'typography',
        'layout_settings',
        'custom_css',
        'logo_url',
        'favicon_url',
        'background_settings',
        'button_styles',
        'chat_bubble_styles',
        'animation_settings',
        'responsive_settings',
        'metadata',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'color_scheme' => 'array',
        'typography' => 'array',
        'layout_settings' => 'array',
        'background_settings' => 'array',
        'button_styles' => 'array',
        'chat_bubble_styles' => 'array',
        'animation_settings' => 'array',
        'responsive_settings' => 'array',
        'metadata' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($theme) {
            if (empty($theme->theme_id)) {
                $theme->theme_id = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the site that owns the theme
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Get default color scheme
     */
    public function getDefaultColorScheme()
    {
        return [
            'primary' => '#3B82F6',
            'secondary' => '#6B7280',
            'accent' => '#10B981',
            'background' => '#FFFFFF',
            'surface' => '#F9FAFB',
            'text_primary' => '#111827',
            'text_secondary' => '#6B7280',
            'border' => '#E5E7EB',
            'success' => '#10B981',
            'warning' => '#F59E0B',
            'error' => '#EF4444',
            'info' => '#3B82F6',
        ];
    }

    /**
     * Get default typography settings
     */
    public function getDefaultTypography()
    {
        return [
            'font_family' => 'Inter, system-ui, sans-serif',
            'font_size_base' => '14px',
            'font_size_small' => '12px',
            'font_size_large' => '16px',
            'font_size_heading' => '18px',
            'font_weight_normal' => '400',
            'font_weight_medium' => '500',
            'font_weight_bold' => '600',
            'line_height' => '1.5',
            'letter_spacing' => '0',
        ];
    }

    /**
     * Get default layout settings
     */
    public function getDefaultLayoutSettings()
    {
        return [
            'chat_position' => 'bottom-right',
            'chat_width' => '350px',
            'chat_height' => '500px',
            'chat_margin' => '20px',
            'border_radius' => '12px',
            'shadow' => '0 10px 25px rgba(0, 0, 0, 0.1)',
            'header_height' => '60px',
            'footer_height' => '50px',
            'message_spacing' => '8px',
            'padding' => '16px',
        ];
    }

    /**
     * Get default button styles
     */
    public function getDefaultButtonStyles()
    {
        return [
            'primary' => [
                'background' => '#3B82F6',
                'color' => '#FFFFFF',
                'border' => 'none',
                'border_radius' => '6px',
                'padding' => '8px 16px',
                'font_weight' => '500',
                'hover_background' => '#2563EB',
            ],
            'secondary' => [
                'background' => 'transparent',
                'color' => '#6B7280',
                'border' => '1px solid #E5E7EB',
                'border_radius' => '6px',
                'padding' => '8px 16px',
                'font_weight' => '500',
                'hover_background' => '#F9FAFB',
            ],
            'chat_trigger' => [
                'background' => '#3B82F6',
                'color' => '#FFFFFF',
                'border' => 'none',
                'border_radius' => '50%',
                'width' => '60px',
                'height' => '60px',
                'shadow' => '0 4px 12px rgba(59, 130, 246, 0.4)',
                'hover_scale' => '1.05',
            ],
        ];
    }

    /**
     * Get default chat bubble styles
     */
    public function getDefaultChatBubbleStyles()
    {
        return [
            'visitor' => [
                'background' => '#3B82F6',
                'color' => '#FFFFFF',
                'border_radius' => '18px 18px 4px 18px',
                'padding' => '12px 16px',
                'margin' => '4px 0',
                'max_width' => '80%',
                'align' => 'right',
            ],
            'agent' => [
                'background' => '#F3F4F6',
                'color' => '#111827',
                'border_radius' => '18px 18px 18px 4px',
                'padding' => '12px 16px',
                'margin' => '4px 0',
                'max_width' => '80%',
                'align' => 'left',
            ],
            'system' => [
                'background' => '#FEF3C7',
                'color' => '#92400E',
                'border_radius' => '12px',
                'padding' => '8px 12px',
                'margin' => '8px 0',
                'max_width' => '90%',
                'align' => 'center',
                'font_size' => '12px',
            ],
        ];
    }

    /**
     * Get default animation settings
     */
    public function getDefaultAnimationSettings()
    {
        return [
            'enable_animations' => true,
            'chat_open_animation' => 'slideUp',
            'message_animation' => 'fadeIn',
            'typing_animation' => 'bounce',
            'transition_duration' => '300ms',
            'easing' => 'cubic-bezier(0.4, 0, 0.2, 1)',
        ];
    }

    /**
     * Get default responsive settings
     */
    public function getDefaultResponsiveSettings()
    {
        return [
            'mobile_breakpoint' => '768px',
            'tablet_breakpoint' => '1024px',
            'mobile_chat_width' => '100%',
            'mobile_chat_height' => '100%',
            'mobile_chat_position' => 'fullscreen',
            'tablet_chat_width' => '400px',
            'tablet_chat_height' => '600px',
        ];
    }

    /**
     * Apply default settings to theme
     */
    public function applyDefaults()
    {
        if (empty($this->color_scheme)) {
            $this->color_scheme = $this->getDefaultColorScheme();
        }

        if (empty($this->typography)) {
            $this->typography = $this->getDefaultTypography();
        }

        if (empty($this->layout_settings)) {
            $this->layout_settings = $this->getDefaultLayoutSettings();
        }

        if (empty($this->button_styles)) {
            $this->button_styles = $this->getDefaultButtonStyles();
        }

        if (empty($this->chat_bubble_styles)) {
            $this->chat_bubble_styles = $this->getDefaultChatBubbleStyles();
        }

        if (empty($this->animation_settings)) {
            $this->animation_settings = $this->getDefaultAnimationSettings();
        }

        if (empty($this->responsive_settings)) {
            $this->responsive_settings = $this->getDefaultResponsiveSettings();
        }
    }

    /**
     * Generate CSS from theme settings
     */
    public function generateCss()
    {
        $css = '';
        
        // Color scheme CSS variables
        if ($this->color_scheme) {
            $css .= ":root {\n";
            foreach ($this->color_scheme as $key => $value) {
                $css .= "  --color-{$key}: {$value};\n";
            }
            $css .= "}\n\n";
        }

        // Typography
        if ($this->typography) {
            $css .= ".chat-widget {\n";
            $css .= "  font-family: {$this->typography['font_family']};\n";
            $css .= "  font-size: {$this->typography['font_size_base']};\n";
            $css .= "  line-height: {$this->typography['line_height']};\n";
            $css .= "}\n\n";
        }

        // Layout settings
        if ($this->layout_settings) {
            $css .= ".chat-container {\n";
            $css .= "  width: {$this->layout_settings['chat_width']};\n";
            $css .= "  height: {$this->layout_settings['chat_height']};\n";
            $css .= "  border-radius: {$this->layout_settings['border_radius']};\n";
            $css .= "  box-shadow: {$this->layout_settings['shadow']};\n";
            $css .= "}\n\n";
        }

        // Add custom CSS
        if ($this->custom_css) {
            $css .= $this->custom_css . "\n";
        }

        return $css;
    }

    /**
     * Scope to get active themes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get default themes
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Set as active theme for site
     */
    public function setAsActive()
    {
        // Deactivate other themes for this site
        static::where('site_id', $this->site_id)
            ->where('id', '!=', $this->id)
            ->update(['is_active' => false]);

        // Activate this theme
        $this->update(['is_active' => true]);
    }

    /**
     * Clone theme
     */
    public function cloneTheme($newName = null)
    {
        $clone = $this->replicate();
        $clone->theme_id = (string) Str::uuid();
        $clone->name = $newName ?? $this->name . ' (Copy)';
        $clone->is_active = false;
        $clone->is_default = false;
        $clone->save();

        return $clone;
    }
}

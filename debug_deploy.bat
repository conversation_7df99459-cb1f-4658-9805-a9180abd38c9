@echo off
chcp 65001 >nul
title 调试部署脚本 - 不会闪退
color 0A

echo.
echo ========================================
echo    调试部署脚本 - 详细显示所有信息
echo ========================================
echo.

echo 按任意键开始检查...
pause >nul

:: 显示当前目录
echo [调试] 当前目录: %CD%
echo [调试] 脚本目录: %~dp0

:: 设置项目目录
set PROJECT_DIR=%~dp0
set PROJECT_DIR=%PROJECT_DIR:~0,-1%
echo [调试] 项目目录: %PROJECT_DIR%

echo.
echo ========================================
echo 第一步：检查PHP环境
echo ========================================

set PHP_FOUND=0

echo [检查] D:\BtSoft\php\72\php.exe
if exist "D:\BtSoft\php\72\php.exe" (
    echo ✅ 找到 PHP 7.2
    set PHP_PATH=D:\BtSoft\php\72\php.exe
    set PHP_FOUND=1
    "D:\BtSoft\php\72\php.exe" --version
) else (
    echo ❌ PHP 7.2 不存在
)

echo [检查] D:\BtSoft\php\73\php.exe
if exist "D:\BtSoft\php\73\php.exe" (
    echo ✅ 找到 PHP 7.3
    if %PHP_FOUND%==0 (
        set PHP_PATH=D:\BtSoft\php\73\php.exe
        set PHP_FOUND=1
    )
) else (
    echo ❌ PHP 7.3 不存在
)

echo [检查] D:\BtSoft\php\74\php.exe
if exist "D:\BtSoft\php\74\php.exe" (
    echo ✅ 找到 PHP 7.4
    if %PHP_FOUND%==0 (
        set PHP_PATH=D:\BtSoft\php\74\php.exe
        set PHP_FOUND=1
    )
) else (
    echo ❌ PHP 7.4 不存在
)

if %PHP_FOUND%==0 (
    echo.
    echo ❌❌❌ 严重错误：未找到任何PHP安装 ❌❌❌
    echo.
    echo 可能的原因：
    echo 1. 宝塔面板未安装
    echo 2. PHP未安装
    echo 3. PHP安装路径不是标准路径
    echo.
    echo 解决方案：
    echo 1. 安装宝塔面板
    echo 2. 在宝塔面板中安装PHP 7.2
    echo 3. 或者告诉我您的PHP安装路径
    echo.
    echo 按任意键退出...
    pause >nul
    exit /b 1
)

echo.
echo ✅ 使用PHP: %PHP_PATH%

echo.
echo ========================================
echo 第二步：检查Composer
echo ========================================

echo [检查] 系统Composer命令
composer --version >nul 2>&1
if %errorlevel%==0 (
    echo ✅ 系统已安装Composer
    composer --version
    set COMPOSER_CMD=composer
    set COMPOSER_FOUND=1
) else (
    echo ❌ 系统未安装Composer
    set COMPOSER_FOUND=0
)

echo [检查] 本地composer.phar文件
if exist composer.phar (
    echo ✅ 找到本地composer.phar
    "%PHP_PATH%" composer.phar --version
    set COMPOSER_CMD="%PHP_PATH%" composer.phar
    set COMPOSER_FOUND=1
) else (
    echo ❌ 本地composer.phar不存在
)

if %COMPOSER_FOUND%==0 (
    echo.
    echo ⚠️ Composer未找到，尝试下载...
    echo [下载] 正在下载composer.phar...
    
    powershell -Command "Invoke-WebRequest -Uri 'https://getcomposer.org/composer.phar' -OutFile 'composer.phar'"
    
    if exist composer.phar (
        echo ✅ Composer下载成功
        "%PHP_PATH%" composer.phar --version
        set COMPOSER_CMD="%PHP_PATH%" composer.phar
        set COMPOSER_FOUND=1
    ) else (
        echo ❌ Composer下载失败
        echo.
        echo 手动解决方案：
        echo 1. 访问 https://getcomposer.org/download/
        echo 2. 下载 Composer-Setup.exe
        echo 3. 安装到系统PATH中
        echo.
        echo 按任意键继续（跳过Composer步骤）...
        pause >nul
        set COMPOSER_FOUND=0
    )
)

echo.
echo ========================================
echo 第三步：检查项目文件
echo ========================================

echo [检查] composer.json
if exist composer.json (
    echo ✅ composer.json 存在
) else (
    echo ❌ composer.json 不存在
    echo 这不是一个有效的Laravel项目
)

echo [检查] .env.example
if exist .env.example (
    echo ✅ .env.example 存在
) else (
    echo ❌ .env.example 不存在
)

echo [检查] artisan
if exist artisan (
    echo ✅ artisan 文件存在
) else (
    echo ❌ artisan 文件不存在
)

echo [检查] vendor目录
if exist vendor (
    echo ✅ vendor 目录存在
    echo [信息] 依赖已安装
) else (
    echo ❌ vendor 目录不存在
    echo [信息] 需要运行 composer install
)

echo.
echo ========================================
echo 第四步：网络连接测试
echo ========================================

echo [测试] ping packagist.phpcomposer.com
ping -n 1 packagist.phpcomposer.com >nul 2>&1
if %errorlevel%==0 (
    echo ✅ 网络连接正常
) else (
    echo ❌ 网络连接失败
)

echo [测试] ping mirrors.aliyun.com
ping -n 1 mirrors.aliyun.com >nul 2>&1
if %errorlevel%==0 (
    echo ✅ 阿里云镜像可访问
) else (
    echo ❌ 阿里云镜像不可访问
)

echo.
echo ========================================
echo 第五步：尝试安装依赖（如果需要）
echo ========================================

if %COMPOSER_FOUND%==1 (
    if not exist vendor (
        echo [操作] 配置Composer镜像...
        %COMPOSER_CMD% config -g secure-http false
        %COMPOSER_CMD% config -g repo.packagist composer http://packagist.phpcomposer.com
        
        echo [操作] 开始安装依赖...
        echo 这可能需要几分钟时间，请耐心等待...
        
        %COMPOSER_CMD% install --no-dev --optimize-autoloader
        
        if %errorlevel%==0 (
            echo ✅ 依赖安装成功
        ) else (
            echo ❌ 依赖安装失败
            echo [尝试] 使用忽略平台要求的方式安装...
            %COMPOSER_CMD% install --no-dev --optimize-autoloader --ignore-platform-reqs
            
            if %errorlevel%==0 (
                echo ✅ 依赖安装成功（忽略平台要求）
            ) else (
                echo ❌ 依赖安装仍然失败
            )
        )
    ) else (
        echo [信息] vendor目录已存在，跳过依赖安装
    )
) else (
    echo [跳过] Composer不可用，跳过依赖安装
)

echo.
echo ========================================
echo 第六步：Laravel配置
echo ========================================

if exist vendor (
    echo [检查] .env文件
    if not exist .env (
        if exist .env.example (
            echo [操作] 复制.env.example到.env
            copy .env.example .env
            echo ✅ .env文件创建成功
        ) else (
            echo ❌ .env.example不存在，无法创建.env
        )
    ) else (
        echo ✅ .env文件已存在
    )
    
    if exist artisan (
        echo [操作] 生成应用密钥...
        "%PHP_PATH%" artisan key:generate --force
        
        if %errorlevel%==0 (
            echo ✅ 应用密钥生成成功
        ) else (
            echo ❌ 应用密钥生成失败
        )
        
        echo [测试] 测试artisan命令...
        "%PHP_PATH%" artisan --version
        
        if %errorlevel%==0 (
            echo ✅ Laravel运行正常
        ) else (
            echo ❌ Laravel运行异常
        )
    ) else (
        echo [跳过] artisan文件不存在
    )
) else (
    echo [跳过] vendor目录不存在，跳过Laravel配置
)

echo.
echo ========================================
echo 检查完成 - 结果总结
echo ========================================

echo PHP环境: 
if %PHP_FOUND%==1 (
    echo ✅ 正常 - %PHP_PATH%
) else (
    echo ❌ 异常 - 未找到PHP
)

echo Composer:
if %COMPOSER_FOUND%==1 (
    echo ✅ 正常 - %COMPOSER_CMD%
) else (
    echo ❌ 异常 - 未找到Composer
)

echo 项目文件:
if exist vendor (
    echo ✅ 正常 - 依赖已安装
) else (
    echo ❌ 异常 - 依赖未安装
)

echo Laravel:
if exist .env (
    echo ✅ 正常 - 配置文件存在
) else (
    echo ❌ 异常 - 配置文件缺失
)

echo.
echo 下一步建议：
if %PHP_FOUND%==1 (
    if %COMPOSER_FOUND%==1 (
        if exist vendor (
            echo 1. 在宝塔面板创建网站，根目录指向: %PROJECT_DIR%\public
            echo 2. 配置数据库连接信息
            echo 3. 运行数据库迁移
        ) else (
            echo 1. 手动运行: composer install
            echo 2. 然后继续后续配置
        )
    ) else (
        echo 1. 先安装Composer
        echo 2. 然后重新运行此脚本
    )
) else (
    echo 1. 先安装宝塔面板和PHP
    echo 2. 然后重新运行此脚本
)

echo.
echo ========================================
echo 脚本执行完成，窗口不会关闭
echo 按任意键退出...
echo ========================================
pause >nul

# 本地部署教程 - Windows 10 + 宝塔面板

## 环境信息
- **操作系统**: Windows 10
- **宝塔面板**: 免费版 8.3.3
- **Web服务器**: Nginx 1.20.0
- **PHP版本**: 7.2
- **数据库**: MySQL 5.7.38
- **缓存**: Redis 5.0.10

## 第一步：安装宝塔面板

### 1.1 下载宝塔面板
1. 访问宝塔官网：https://www.bt.cn/
2. 下载Windows版宝塔面板 8.3.3
3. 以管理员身份运行安装程序

### 1.2 安装宝塔面板
```bash
# 下载后双击安装包
# 选择安装路径（建议默认：D:\BtSoft）
# 等待安装完成
```

### 1.3 初始化宝塔面板
1. 安装完成后会自动打开浏览器
2. 访问：http://localhost:888
3. 使用默认账号密码登录（安装时会显示）
4. 绑定宝塔账号（可选）

## 第二步：安装运行环境

### 2.1 一键安装LNMP环境
1. 登录宝塔面板
2. 点击"软件商店"
3. 选择"一键部署"
4. 安装以下组件：
   - **Nginx 1.20.0** - 选择编译安装
   - **MySQL 5.7.38** - 选择编译安装
   - **PHP 7.2** - 选择编译安装
   - **Redis 5.0.10** - 选择编译安装
   - **phpMyAdmin 5.0** - 选择安装

### 2.2 配置PHP扩展
1. 进入"软件商店" → "已安装"
2. 找到PHP 7.2，点击"设置"
3. 点击"安装扩展"，安装以下扩展：
   ```
   ✅ opcache      # PHP加速器
   ✅ redis        # Redis扩展
   ✅ memcached    # 缓存扩展
   ✅ imagemagick  # 图像处理
   ✅ fileinfo     # 文件信息
   ✅ exif         # 图片信息
   ✅ intl         # 国际化
   ✅ zip          # 压缩文件
   ✅ curl         # 网络请求
   ✅ gd           # 图像处理
   ```

### 2.3 配置PHP参数
在PHP设置中修改以下参数：
```ini
# 基础配置
memory_limit = 256M
max_execution_time = 300
max_input_time = 300
post_max_size = 50M
upload_max_filesize = 50M
max_file_uploads = 20

# 错误报告（开发环境）
display_errors = On
error_reporting = E_ALL

# 时区设置
date.timezone = Asia/Shanghai
```

## 第三步：创建网站和数据库

### 3.1 创建网站
1. 点击"网站" → "添加站点"
2. 填写信息：
   - **域名**: `chat.local`（或您的本地域名）
   - **根目录**: `D:\wwwroot\chat_web`
   - **PHP版本**: 7.2
   - **数据库**: MySQL（创建同名数据库）
   - **数据库密码**: 自动生成或自定义

### 3.2 配置数据库
1. 记录数据库信息：
   ```
   数据库名: chat_web
   用户名: chat_web
   密码: fcpHBiscJRBCznnw（或宝塔生成的密码）
   主机: 127.0.0.1
   端口: 3306
   ```

### 3.3 配置Redis
1. 进入"软件商店" → "已安装" → "Redis"
2. 点击"设置" → "配置修改"
3. 修改配置：
   ```redis
   # 绑定地址
   bind 127.0.0.1
   
   # 端口
   port 6379
   
   # 密码（可选）
   # requirepass your_password
   
   # 最大内存
   maxmemory 256mb
   maxmemory-policy allkeys-lru
   ```

## 第四步：部署项目代码

### 4.1 上传项目文件
1. 将项目文件上传到：`D:\wwwroot\chat_web`
2. 或者使用Git克隆：
   ```bash
   cd D:\wwwroot
   git clone https://github.com/your-repo/chat_web.git
   ```

### 4.2 安装Composer
1. 下载Composer：https://getcomposer.org/download/
2. 安装到系统PATH中
3. 验证安装：
   ```bash
   composer --version
   ```

### 4.3 安装项目依赖
在项目根目录执行：
```bash
# 进入项目目录
cd D:\wwwroot\chat_web

# 安装PHP依赖
composer install

# 如果需要安装前端依赖
npm install
npm run dev
```

### 4.4 配置环境变量
1. 复制环境配置文件：
   ```bash
   copy .env.example .env
   ```

2. 编辑`.env`文件：
   ```env
   APP_NAME="Customer Service System"
   APP_ENV=local
   APP_KEY=
   APP_DEBUG=true
   APP_URL=http://chat.local

   LOG_CHANNEL=stack
   LOG_LEVEL=debug

   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=chat_web
   DB_USERNAME=chat_web
   DB_PASSWORD=fcpHBiscJRBCznnw

   BROADCAST_DRIVER=redis
   CACHE_DRIVER=redis
   FILESYSTEM_DRIVER=local
   QUEUE_CONNECTION=redis
   SESSION_DRIVER=redis
   SESSION_LIFETIME=120

   REDIS_HOST=127.0.0.1
   REDIS_PASSWORD=null
   REDIS_PORT=6379

   WEBSOCKET_HOST=127.0.0.1
   WEBSOCKET_PORT=8080
   ```

### 4.5 生成应用密钥
```bash
php artisan key:generate
```

### 4.6 运行数据库迁移
```bash
# 运行迁移
php artisan migrate

# 运行数据填充
php artisan db:seed
```

## 第五步：配置Nginx

### 5.1 配置网站
1. 在宝塔面板中，点击网站名称后的"设置"
2. 点击"配置文件"
3. 替换为以下配置：

```nginx
server {
    listen 80;
    server_name chat.local;
    root D:/wwwroot/chat_web/public;
    index index.php index.html index.htm;

    # 字符集
    charset utf-8;

    # 日志文件
    access_log D:/wwwroot/chat_web/storage/logs/nginx_access.log;
    error_log D:/wwwroot/chat_web/storage/logs/nginx_error.log;

    # 文件上传大小限制
    client_max_body_size 50M;

    # Laravel路由
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 超时设置
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;
        fastcgi_read_timeout 300;
    }

    # WebSocket代理
    location /ws {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 隐藏敏感文件
    location ~ /\.(ht|env) {
        deny all;
    }

    # 禁止访问敏感目录
    location ~ ^/(storage|vendor|tests|database)/ {
        deny all;
    }
}
```

### 5.2 配置hosts文件
1. 以管理员身份打开记事本
2. 打开文件：`C:\Windows\System32\drivers\etc\hosts`
3. 添加一行：
   ```
   127.0.0.1 chat.local
   ```

## 第六步：启动服务

### 6.1 启动WebSocket服务
在项目根目录创建启动脚本`start_websocket.bat`：
```batch
@echo off
cd /d D:\wwwroot\chat_web
php artisan websocket:serve
pause
```

### 6.2 启动队列处理器
创建队列处理脚本`start_queue.bat`：
```batch
@echo off
cd /d D:\wwwroot\chat_web
php artisan queue:work --sleep=3 --tries=3
pause
```

### 6.3 设置定时任务
1. 打开Windows任务计划程序
2. 创建基本任务
3. 设置每分钟运行一次
4. 操作：启动程序
5. 程序：`php`
6. 参数：`D:\wwwroot\chat_web\artisan schedule:run`

## 第七步：测试验证

### 7.1 访问网站
1. 打开浏览器访问：http://chat.local
2. 应该能看到登录页面

### 7.2 运行系统测试
```bash
cd D:\wwwroot\chat_web
php tests/run_tests.php http://chat.local
```

### 7.3 检查服务状态
1. **Web服务**: 访问网站是否正常
2. **数据库**: 在phpMyAdmin中检查表是否创建
3. **Redis**: 在宝塔面板中检查Redis是否运行
4. **WebSocket**: 检查端口8080是否监听
5. **队列**: 检查队列处理器是否运行

## 第八步：创建管理员账户

### 8.1 使用Artisan命令创建
```bash
php artisan tinker
```

在tinker中执行：
```php
$user = new App\Models\User();
$user->name = 'Administrator';
$user->email = '<EMAIL>';
$user->password = Hash::make('admin123456');
$user->role = 'admin';
$user->is_active = true;
$user->site_id = 1; // 假设已有站点
$user->save();
```

### 8.2 或使用数据库直接插入
在phpMyAdmin中执行：
```sql
INSERT INTO users (name, email, password, role, is_active, site_id, created_at, updated_at) 
VALUES ('Administrator', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin', 1, 1, NOW(), NOW());
```

## 常见问题解决

### Q1: Composer安装失败
```bash
# 使用国内镜像
composer config -g repo.packagist composer https://mirrors.aliyun.com/composer/
```

### Q2: 文件权限问题
确保以下目录可写：
- `storage/` 及其子目录
- `bootstrap/cache/`
- `public/storage/`

### Q3: WebSocket连接失败
1. 检查防火墙是否开放8080端口
2. 检查WebSocket服务是否启动
3. 检查Nginx配置中的WebSocket代理

### Q4: 数据库连接失败
1. 检查MySQL服务是否启动
2. 验证数据库连接信息
3. 检查数据库用户权限

### Q5: Redis连接失败
1. 检查Redis服务是否启动
2. 验证Redis配置
3. 检查PHP Redis扩展是否安装

## 开发调试

### 启用调试模式
在`.env`文件中设置：
```env
APP_DEBUG=true
LOG_LEVEL=debug
```

### 查看日志
日志文件位置：
- Laravel日志: `storage/logs/laravel.log`
- Nginx访问日志: `storage/logs/nginx_access.log`
- Nginx错误日志: `storage/logs/nginx_error.log`

### 清除缓存
```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

---

**本地部署完成！** 🎉

现在您可以通过 http://chat.local 访问系统，使用创建的管理员账户登录开始使用。

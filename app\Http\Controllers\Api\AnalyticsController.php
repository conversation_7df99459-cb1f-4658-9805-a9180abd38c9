<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\Visitor;
use App\Models\VisitorBehavior;
use App\Models\User;
use App\Models\ContactMessage;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    /**
     * Get dashboard overview
     */
    public function getDashboardOverview(Request $request): JsonResponse
    {
        $cacheKey = 'dashboard_overview_' . $request->user()->id;
        
        $data = Cache::remember($cacheKey, 300, function () {
            $today = Carbon::today();
            $yesterday = Carbon::yesterday();
            $thisWeek = Carbon::now()->startOfWeek();
            $lastWeek = Carbon::now()->subWeek()->startOfWeek();
            $thisMonth = Carbon::now()->startOfMonth();
            $lastMonth = Carbon::now()->subMonth()->startOfMonth();

            return [
                'active_sessions' => ChatSession::where('status', 'active')->count(),
                'waiting_sessions' => ChatSession::where('status', 'waiting')->count(),
                'online_agents' => User::where('role', 'agent')
                    ->where('status', 'online')
                    ->where('is_active', true)
                    ->count(),
                'today_sessions' => ChatSession::whereDate('started_at', $today)->count(),
                'today_messages' => ChatMessage::whereDate('sent_at', $today)->count(),
                'today_visitors' => Visitor::whereDate('first_visit_at', $today)->count(),
                'average_response_time' => $this->getAverageResponseTime($today),
                'satisfaction_rating' => $this->getAverageSatisfactionRating($thisWeek),
                'session_trends' => $this->getSessionTrends(7),
                'top_pages' => $this->getTopPages(7),
                'recent_sessions' => $this->getRecentSessions(10)
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get dashboard statistics
     */
    public function getDashboardStats(Request $request): JsonResponse
    {
        $period = $request->get('period', '7d'); // 1d, 7d, 30d, 90d
        $startDate = $this->getStartDateForPeriod($period);

        $stats = [
            'total_sessions' => ChatSession::where('started_at', '>=', $startDate)->count(),
            'completed_sessions' => ChatSession::where('started_at', '>=', $startDate)
                ->where('status', 'ended')
                ->count(),
            'total_messages' => ChatMessage::where('sent_at', '>=', $startDate)->count(),
            'unique_visitors' => Visitor::where('first_visit_at', '>=', $startDate)->count(),
            'average_session_duration' => $this->getAverageSessionDuration($startDate),
            'conversion_rate' => $this->getConversionRate($startDate),
            'agent_utilization' => $this->getAgentUtilization($startDate),
            'peak_hours' => $this->getPeakHours($startDate)
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get system status
     */
    public function getSystemStatus(Request $request): JsonResponse
    {
        $status = [
            'database' => $this->checkDatabaseStatus(),
            'redis' => $this->checkRedisStatus(),
            'websocket' => $this->checkWebSocketStatus(),
            'storage' => $this->checkStorageStatus(),
            'queue' => $this->checkQueueStatus(),
            'last_updated' => now()->toISOString()
        ];

        return response()->json([
            'success' => true,
            'data' => $status
        ]);
    }

    /**
     * Get chat metrics
     */
    public function getChatMetrics(Request $request): JsonResponse
    {
        $period = $request->get('period', '30d');
        $startDate = $this->getStartDateForPeriod($period);

        $metrics = [
            'session_volume' => $this->getSessionVolumeData($startDate),
            'response_times' => $this->getResponseTimeData($startDate),
            'resolution_rates' => $this->getResolutionRateData($startDate),
            'satisfaction_scores' => $this->getSatisfactionScoreData($startDate),
            'agent_performance' => $this->getAgentPerformanceData($startDate),
            'busiest_hours' => $this->getBusiestHoursData($startDate)
        ];

        return response()->json([
            'success' => true,
            'data' => $metrics
        ]);
    }

    /**
     * Get visitor metrics
     */
    public function getVisitorMetrics(Request $request): JsonResponse
    {
        $period = $request->get('period', '30d');
        $startDate = $this->getStartDateForPeriod($period);

        $metrics = [
            'visitor_flow' => $this->getVisitorFlowData($startDate),
            'page_analytics' => $this->getPageAnalyticsData($startDate),
            'behavior_patterns' => $this->getBehaviorPatternsData($startDate),
            'device_breakdown' => $this->getDeviceBreakdownData($startDate),
            'geographic_data' => $this->getGeographicData($startDate),
            'referrer_sources' => $this->getReferrerSourcesData($startDate)
        ];

        return response()->json([
            'success' => true,
            'data' => $metrics
        ]);
    }

    /**
     * Get agent performance
     */
    public function getAgentPerformance(Request $request): JsonResponse
    {
        $period = $request->get('period', '30d');
        $startDate = $this->getStartDateForPeriod($period);

        $agents = User::where('role', 'agent')
            ->where('is_active', true)
            ->with(['chatSessions' => function ($query) use ($startDate) {
                $query->where('started_at', '>=', $startDate);
            }])
            ->get()
            ->map(function ($agent) use ($startDate) {
                $sessions = $agent->chatSessions;
                
                return [
                    'id' => $agent->id,
                    'name' => $agent->name,
                    'avatar' => $agent->avatar,
                    'status' => $agent->status,
                    'total_sessions' => $sessions->count(),
                    'completed_sessions' => $sessions->where('status', 'ended')->count(),
                    'average_response_time' => $this->getAgentAverageResponseTime($agent->id, $startDate),
                    'satisfaction_rating' => $this->getAgentSatisfactionRating($agent->id, $startDate),
                    'resolution_rate' => $this->getAgentResolutionRate($agent->id, $startDate),
                    'online_time' => $this->getAgentOnlineTime($agent->id, $startDate)
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $agents
        ]);
    }

    /**
     * Get satisfaction ratings
     */
    public function getSatisfactionRatings(Request $request): JsonResponse
    {
        $period = $request->get('period', '30d');
        $startDate = $this->getStartDateForPeriod($period);

        $ratings = ChatSession::where('started_at', '>=', $startDate)
            ->whereNotNull('rating')
            ->select([
                DB::raw('DATE(started_at) as date'),
                DB::raw('AVG(rating) as average_rating'),
                DB::raw('COUNT(*) as total_ratings'),
                DB::raw('SUM(CASE WHEN rating >= 4 THEN 1 ELSE 0 END) as positive_ratings'),
                DB::raw('SUM(CASE WHEN rating <= 2 THEN 1 ELSE 0 END) as negative_ratings')
            ])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $summary = [
            'overall_average' => ChatSession::where('started_at', '>=', $startDate)
                ->whereNotNull('rating')
                ->avg('rating'),
            'total_ratings' => ChatSession::where('started_at', '>=', $startDate)
                ->whereNotNull('rating')
                ->count(),
            'rating_distribution' => ChatSession::where('started_at', '>=', $startDate)
                ->whereNotNull('rating')
                ->groupBy('rating')
                ->select('rating', DB::raw('COUNT(*) as count'))
                ->orderBy('rating')
                ->get()
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'daily_ratings' => $ratings,
                'summary' => $summary
            ]
        ]);
    }

    /**
     * Get response times
     */
    public function getResponseTimes(Request $request): JsonResponse
    {
        $period = $request->get('period', '30d');
        $startDate = $this->getStartDateForPeriod($period);

        // This would require more complex query to calculate actual response times
        // For now, returning sample data structure
        $responseTimes = [
            'average_first_response' => 120, // seconds
            'average_response_time' => 45, // seconds
            'response_time_trends' => $this->getResponseTimeTrends($startDate),
            'response_time_by_agent' => $this->getResponseTimeByAgent($startDate),
            'sla_compliance' => $this->getSLACompliance($startDate)
        ];

        return response()->json([
            'success' => true,
            'data' => $responseTimes
        ]);
    }

    /**
     * Export analytics data
     */
    public function exportAnalytics(Request $request): JsonResponse
    {
        $format = $request->get('format', 'csv'); // csv, xlsx, pdf
        $period = $request->get('period', '30d');
        $startDate = $this->getStartDateForPeriod($period);

        // Generate export file
        $filename = 'analytics_' . $period . '_' . now()->format('Y-m-d') . '.' . $format;
        
        // This would generate the actual export file
        // For now, returning the download URL
        
        return response()->json([
            'success' => true,
            'data' => [
                'download_url' => '/api/v1/admin/analytics/download/' . $filename,
                'filename' => $filename,
                'expires_at' => now()->addHours(24)->toISOString()
            ]
        ]);
    }

    /**
     * Clean up old data
     */
    public function cleanupOldData(Request $request): JsonResponse
    {
        $days = $request->get('days', 90);
        $cutoffDate = Carbon::now()->subDays($days);

        $deletedCounts = [
            'visitor_behaviors' => VisitorBehavior::where('created_at', '<', $cutoffDate)->delete(),
            'old_sessions' => ChatSession::where('started_at', '<', $cutoffDate)
                ->where('status', 'ended')
                ->delete(),
            'old_messages' => ChatMessage::whereHas('session', function ($query) use ($cutoffDate) {
                $query->where('started_at', '<', $cutoffDate);
            })->delete()
        ];

        return response()->json([
            'success' => true,
            'message' => 'Data cleanup completed',
            'data' => $deletedCounts
        ]);
    }

    // Helper methods
    private function getStartDateForPeriod(string $period): Carbon
    {
        switch ($period) {
            case '1d':
                return Carbon::today();
            case '7d':
                return Carbon::now()->subDays(7);
            case '30d':
                return Carbon::now()->subDays(30);
            case '90d':
                return Carbon::now()->subDays(90);
            default:
                return Carbon::now()->subDays(7);
        }
    }

    private function getAverageResponseTime(Carbon $date): float
    {
        // Simplified calculation - would need more complex logic in production
        return 120.5; // seconds
    }

    private function getAverageSatisfactionRating(Carbon $date): float
    {
        return ChatSession::where('started_at', '>=', $date)
            ->whereNotNull('rating')
            ->avg('rating') ?? 0;
    }

    private function getSessionTrends(int $days): array
    {
        return ChatSession::where('started_at', '>=', Carbon::now()->subDays($days))
            ->groupBy(DB::raw('DATE(started_at)'))
            ->select(DB::raw('DATE(started_at) as date'), DB::raw('COUNT(*) as count'))
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    private function getTopPages(int $days): array
    {
        return VisitorBehavior::where('created_at', '>=', Carbon::now()->subDays($days))
            ->where('event_type', 'page_view')
            ->groupBy('page_url')
            ->select('page_url', DB::raw('COUNT(*) as views'))
            ->orderBy('views', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    private function getRecentSessions(int $limit): array
    {
        return ChatSession::with(['visitor', 'agent'])
            ->orderBy('started_at', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    private function checkDatabaseStatus(): string
    {
        try {
            DB::connection()->getPdo();
            return 'connected';
        } catch (\Exception $e) {
            return 'disconnected';
        }
    }

    private function checkRedisStatus(): string
    {
        try {
            Cache::store('redis')->put('health_check', 'ok', 1);
            return Cache::store('redis')->get('health_check') === 'ok' ? 'connected' : 'error';
        } catch (\Exception $e) {
            return 'disconnected';
        }
    }

    private function checkWebSocketStatus(): string
    {
        // This would check if WebSocket server is running
        return 'running';
    }

    private function checkStorageStatus(): string
    {
        try {
            \Storage::disk('public')->put('health_check.txt', 'ok');
            return \Storage::disk('public')->exists('health_check.txt') ? 'available' : 'error';
        } catch (\Exception $e) {
            return 'unavailable';
        }
    }

    private function checkQueueStatus(): string
    {
        // This would check queue status
        return 'running';
    }

    // Additional helper methods would be implemented here for various analytics calculations
    private function getAverageSessionDuration(Carbon $startDate): float { return 15.5; }
    private function getConversionRate(Carbon $startDate): float { return 0.85; }
    private function getAgentUtilization(Carbon $startDate): float { return 0.75; }
    private function getPeakHours(Carbon $startDate): array { return []; }
    private function getSessionVolumeData(Carbon $startDate): array { return []; }
    private function getResponseTimeData(Carbon $startDate): array { return []; }
    private function getResolutionRateData(Carbon $startDate): array { return []; }
    private function getSatisfactionScoreData(Carbon $startDate): array { return []; }
    private function getAgentPerformanceData(Carbon $startDate): array { return []; }
    private function getBusiestHoursData(Carbon $startDate): array { return []; }
    private function getVisitorFlowData(Carbon $startDate): array { return []; }
    private function getPageAnalyticsData(Carbon $startDate): array { return []; }
    private function getBehaviorPatternsData(Carbon $startDate): array { return []; }
    private function getDeviceBreakdownData(Carbon $startDate): array { return []; }
    private function getGeographicData(Carbon $startDate): array { return []; }
    private function getReferrerSourcesData(Carbon $startDate): array { return []; }
    private function getAgentAverageResponseTime(int $agentId, Carbon $startDate): float { return 120.0; }
    private function getAgentSatisfactionRating(int $agentId, Carbon $startDate): float { return 4.2; }
    private function getAgentResolutionRate(int $agentId, Carbon $startDate): float { return 0.85; }
    private function getAgentOnlineTime(int $agentId, Carbon $startDate): int { return 480; }
    private function getResponseTimeTrends(Carbon $startDate): array { return []; }
    private function getResponseTimeByAgent(Carbon $startDate): array { return []; }
    private function getSLACompliance(Carbon $startDate): array { return []; }
}

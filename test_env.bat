@echo off
title Environment Test - No Chinese Characters
color 0F

echo.
echo ==========================================
echo    Environment Test Script
echo ==========================================
echo.

echo Test 1: Current Directory
echo Current Dir: %CD%
echo.

echo Test 2: Check PHP Files
if exist "D:\BtSoft\php\72\php.exe" (
    echo [OK] Found D:\BtSoft\php\72\php.exe
) else (
    echo [NO] Not found D:\BtSoft\php\72\php.exe
)

if exist "D:\BtSoft\php\73\php.exe" (
    echo [OK] Found D:\BtSoft\php\73\php.exe
) else (
    echo [NO] Not found D:\BtSoft\php\73\php.exe
)

if exist "D:\BtSoft\php\74\php.exe" (
    echo [OK] Found D:\BtSoft\php\74\php.exe
) else (
    echo [NO] Not found D:\BtSoft\php\74\php.exe
)

echo.
echo Test 3: Check Project Files
if exist composer.json (
    echo [OK] composer.json exists
) else (
    echo [NO] composer.json missing
)

if exist artisan (
    echo [OK] artisan exists
) else (
    echo [NO] artisan missing
)

if exist .env.example (
    echo [OK] .env.example exists
) else (
    echo [NO] .env.example missing
)

if exist vendor (
    echo [OK] vendor directory exists
) else (
    echo [NO] vendor directory missing
)

echo.
echo Test 4: Check Composer
composer --version >nul 2>&1
if %errorlevel%==0 (
    echo [OK] Composer command available
    composer --version
) else (
    echo [NO] Composer command not available
)

if exist composer.phar (
    echo [OK] composer.phar exists in current directory
) else (
    echo [NO] composer.phar not found
)

echo.
echo Test 5: Check Network
ping -n 1 baidu.com >nul 2>&1
if %errorlevel%==0 (
    echo [OK] Network connection works
) else (
    echo [NO] Network connection failed
)

echo.
echo Test 6: Check BT Panel Services
tasklist /FI "IMAGENAME eq nginx.exe" 2>NUL | find /I /N "nginx.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [OK] Nginx is running
) else (
    echo [NO] Nginx is not running
)

tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [OK] MySQL is running
) else (
    echo [NO] MySQL is not running
)

echo.
echo ==========================================
echo Test Complete!
echo.
echo Please take a screenshot of this window
echo and send it to me so I can help you
echo.
echo This window will NOT close automatically
echo Press any key to close manually...
echo ==========================================

pause >nul

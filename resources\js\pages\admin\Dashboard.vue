<template>
  <div class="space-y-6">
    <!-- Page header -->
    <div class="md:flex md:items-center md:justify-between">
      <div class="flex-1 min-w-0">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
          Dashboard
        </h2>
        <p class="mt-1 text-sm text-gray-500">
          Welcome back, {{ userName }}! Here's what's happening today.
        </p>
      </div>
      <div class="mt-4 flex md:mt-0 md:ml-4">
        <button @click="refreshData" :disabled="isLoading" class="btn btn-primary">
          <ArrowPathIcon class="h-4 w-4 mr-2" :class="{ 'animate-spin': isLoading }" />
          Refresh
        </button>
      </div>
    </div>

    <!-- Stats overview -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <div v-for="stat in stats" :key="stat.name" class="card">
        <div class="card-body">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <component :is="stat.icon" class="h-8 w-8" :class="stat.iconColor" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">{{ stat.name }}</dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-gray-900">{{ stat.value }}</div>
                  <div v-if="stat.change" class="ml-2 flex items-baseline text-sm font-semibold"
                       :class="stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'">
                    <component :is="stat.changeType === 'increase' ? ArrowUpIcon : ArrowDownIcon" class="self-center flex-shrink-0 h-4 w-4" />
                    <span class="sr-only">{{ stat.changeType === 'increase' ? 'Increased' : 'Decreased' }} by</span>
                    {{ stat.change }}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts and recent activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Chat activity chart -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Chat Activity</h3>
          <p class="mt-1 text-sm text-gray-500">Daily chat sessions over the last 7 days</p>
        </div>
        <div class="card-body">
          <div class="h-64 flex items-center justify-center">
            <div v-if="chartLoading" class="text-gray-500">Loading chart...</div>
            <canvas v-else ref="chatChart" class="w-full h-full"></canvas>
          </div>
        </div>
      </div>

      <!-- Recent sessions -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Sessions</h3>
          <p class="mt-1 text-sm text-gray-500">Latest chat sessions</p>
        </div>
        <div class="card-body p-0">
          <div class="flow-root">
            <ul class="divide-y divide-gray-200">
              <li v-for="session in recentSessions" :key="session.id" class="px-6 py-4">
                <div class="flex items-center justify-between">
                  <div class="flex items-center">
                    <div class="flex-shrink-0">
                      <img :src="session.visitor_avatar || '/images/default-avatar.png'" 
                           :alt="session.visitor_name" 
                           class="h-8 w-8 rounded-full">
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">
                        {{ session.visitor_name || 'Anonymous' }}
                      </div>
                      <div class="text-sm text-gray-500">
                        {{ session.subject || 'No subject' }}
                      </div>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span class="badge" :class="getStatusBadgeClass(session.status)">
                      {{ session.status }}
                    </span>
                    <span class="text-xs text-gray-500">
                      {{ formatTime(session.created_at) }}
                    </span>
                  </div>
                </div>
              </li>
            </ul>
          </div>
          <div v-if="recentSessions.length === 0" class="px-6 py-8 text-center text-gray-500">
            No recent sessions
          </div>
        </div>
      </div>
    </div>

    <!-- System status -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg leading-6 font-medium text-gray-900">System Status</h3>
        <p class="mt-1 text-sm text-gray-500">Current system health and performance</p>
      </div>
      <div class="card-body">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div v-for="status in systemStatus" :key="status.name" class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-3 w-3 rounded-full" :class="status.healthy ? 'bg-green-400' : 'bg-red-400'"></div>
            </div>
            <div class="ml-3">
              <div class="text-sm font-medium text-gray-900">{{ status.name }}</div>
              <div class="text-xs text-gray-500">{{ status.status }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick actions (for agents) -->
    <div v-if="userRole === 'agent'" class="card">
      <div class="card-header">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Quick Actions</h3>
      </div>
      <div class="card-body">
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <router-link to="/admin/workspace" class="btn btn-primary">
            <ChatBubbleLeftRightIcon class="h-5 w-5 mr-2" />
            Go to Workspace
          </router-link>
          <button @click="updateStatus('online')" class="btn btn-success">
            <CheckCircleIcon class="h-5 w-5 mr-2" />
            Set Online
          </button>
          <button @click="updateStatus('away')" class="btn btn-warning">
            <ClockIcon class="h-5 w-5 mr-2" />
            Set Away
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useAuthStore } from '../../stores/auth.js'
import { useNotificationStore } from '../../stores/notifications.js'
import axios from 'axios'

// Icons
import {
  ArrowPathIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ChatBubbleLeftRightIcon,
  UsersIcon,
  ChartBarIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/vue/24/outline'

export default {
  name: 'Dashboard',
  components: {
    ArrowPathIcon,
    ArrowUpIcon,
    ArrowDownIcon,
    ChatBubbleLeftRightIcon,
    UsersIcon,
    ChartBarIcon,
    ClockIcon,
    CheckCircleIcon
  },
  setup() {
    const authStore = useAuthStore()
    const notificationStore = useNotificationStore()

    // State
    const isLoading = ref(false)
    const chartLoading = ref(true)
    const dashboardData = ref({})
    const systemStatus = ref([])
    const recentSessions = ref([])
    const chatChart = ref(null)

    // Computed
    const userName = computed(() => authStore.userName)
    const userRole = computed(() => authStore.userRole)

    const stats = computed(() => {
      const data = dashboardData.value
      return [
        {
          name: 'Total Sessions',
          value: data.total_sessions || 0,
          change: data.sessions_change || null,
          changeType: data.sessions_change_type || 'increase',
          icon: ChatBubbleLeftRightIcon,
          iconColor: 'text-blue-600'
        },
        {
          name: 'Active Visitors',
          value: data.active_visitors || 0,
          change: data.visitors_change || null,
          changeType: data.visitors_change_type || 'increase',
          icon: UsersIcon,
          iconColor: 'text-green-600'
        },
        {
          name: 'Avg Response Time',
          value: data.avg_response_time ? `${Math.round(data.avg_response_time)}s` : '0s',
          change: data.response_time_change || null,
          changeType: data.response_time_change_type || 'decrease',
          icon: ClockIcon,
          iconColor: 'text-yellow-600'
        },
        {
          name: 'Satisfaction',
          value: data.satisfaction_rating ? `${Math.round(data.satisfaction_rating * 20)}%` : '0%',
          change: data.satisfaction_change || null,
          changeType: data.satisfaction_change_type || 'increase',
          icon: ChartBarIcon,
          iconColor: 'text-purple-600'
        }
      ]
    })

    // Methods
    const fetchDashboardData = async () => {
      try {
        isLoading.value = true
        const response = await axios.get('/admin/dashboard/overview')
        
        if (response.data.success) {
          dashboardData.value = response.data.data
          recentSessions.value = response.data.data.recent_sessions || []
        }
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error)
        notificationStore.error('Error', 'Failed to load dashboard data')
      } finally {
        isLoading.value = false
      }
    }

    const fetchSystemStatus = async () => {
      try {
        const response = await axios.get('/admin/system/status')
        
        if (response.data.success) {
          const status = response.data.data
          systemStatus.value = [
            {
              name: 'Database',
              status: status.database.status,
              healthy: status.database.healthy
            },
            {
              name: 'Redis',
              status: status.redis.status,
              healthy: status.redis.healthy
            },
            {
              name: 'WebSocket',
              status: status.websocket.status,
              healthy: status.websocket.healthy
            },
            {
              name: 'Storage',
              status: status.storage.status,
              healthy: status.storage.healthy
            }
          ]
        }
      } catch (error) {
        console.error('Failed to fetch system status:', error)
      }
    }

    const refreshData = async () => {
      await Promise.all([
        fetchDashboardData(),
        fetchSystemStatus()
      ])
      
      // Refresh chart
      await nextTick()
      renderChart()
    }

    const renderChart = () => {
      if (!chatChart.value || !dashboardData.value.daily_sessions) {
        chartLoading.value = false
        return
      }

      // Simple chart rendering (you can replace with Chart.js or other library)
      const canvas = chatChart.value
      const ctx = canvas.getContext('2d')
      const data = dashboardData.value.daily_sessions

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Set canvas size
      canvas.width = canvas.offsetWidth
      canvas.height = canvas.offsetHeight

      if (data && data.length > 0) {
        const maxValue = Math.max(...data.map(d => d.sessions))
        const padding = 40
        const chartWidth = canvas.width - padding * 2
        const chartHeight = canvas.height - padding * 2

        // Draw bars
        data.forEach((item, index) => {
          const barWidth = chartWidth / data.length - 10
          const barHeight = (item.sessions / maxValue) * chartHeight
          const x = padding + index * (chartWidth / data.length) + 5
          const y = canvas.height - padding - barHeight

          ctx.fillStyle = '#3B82F6'
          ctx.fillRect(x, y, barWidth, barHeight)

          // Draw labels
          ctx.fillStyle = '#6B7280'
          ctx.font = '12px sans-serif'
          ctx.textAlign = 'center'
          ctx.fillText(
            new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
            x + barWidth / 2,
            canvas.height - 10
          )
        })
      }

      chartLoading.value = false
    }

    const updateStatus = async (status) => {
      try {
        const result = await authStore.updateStatus(status)
        if (result.success) {
          notificationStore.success('Status Updated', `Your status has been set to ${status}`)
        } else {
          notificationStore.error('Error', result.message)
        }
      } catch (error) {
        console.error('Failed to update status:', error)
        notificationStore.error('Error', 'Failed to update status')
      }
    }

    const getStatusBadgeClass = (status) => {
      switch (status) {
        case 'active':
          return 'badge-success'
        case 'waiting':
          return 'badge-warning'
        case 'ended':
          return 'badge-gray'
        default:
          return 'badge-primary'
      }
    }

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // Lifecycle
    onMounted(async () => {
      await refreshData()
    })

    return {
      // State
      isLoading,
      chartLoading,
      dashboardData,
      systemStatus,
      recentSessions,
      chatChart,
      
      // Computed
      userName,
      userRole,
      stats,
      
      // Methods
      refreshData,
      updateStatus,
      getStatusBadgeClass,
      formatTime
    }
  }
}
</script>

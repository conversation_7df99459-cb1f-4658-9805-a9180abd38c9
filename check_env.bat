@echo off
chcp 65001 >nul
title 环境检查工具
color 0E

echo.
echo ========================================
echo    环境检查工具
echo ========================================
echo.

set PROJECT_DIR=%~dp0
set PROJECT_DIR=%PROJECT_DIR:~0,-1%

echo 项目目录: %PROJECT_DIR%
echo.

echo [检查1] PHP环境检查
echo ----------------------------------------
set PHP_FOUND=0

if exist "D:\BtSoft\php\72\php.exe" (
    echo ✅ PHP 7.2: D:\BtSoft\php\72\php.exe
    "D:\BtSoft\php\72\php.exe" --version | findstr "PHP"
    set PHP_PATH=D:\BtSoft\php\72\php.exe
    set PHP_FOUND=1
)

if exist "D:\BtSoft\php\73\php.exe" (
    echo ✅ PHP 7.3: D:\BtSoft\php\73\php.exe
    if %PHP_FOUND%==0 set PHP_PATH=D:\BtSoft\php\73\php.exe
    set PHP_FOUND=1
)

if exist "D:\BtSoft\php\74\php.exe" (
    echo ✅ PHP 7.4: D:\BtSoft\php\74\php.exe
    if %PHP_FOUND%==0 set PHP_PATH=D:\BtSoft\php\74\php.exe
    set PHP_FOUND=1
)

if %PHP_FOUND%==0 (
    echo ❌ 未找到PHP安装
    echo 请先在宝塔面板中安装PHP
)

echo.
echo [检查2] Composer检查
echo ----------------------------------------
composer --version >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Composer已安装
    composer --version
) else (
    echo ❌ Composer未安装
    if exist composer.phar (
        echo ✅ 找到本地composer.phar
        "%PHP_PATH%" composer.phar --version
    ) else (
        echo 建议：运行 deploy.bat 会自动下载Composer
    )
)

echo.
echo [检查3] 项目文件检查
echo ----------------------------------------
if exist composer.json (
    echo ✅ composer.json 存在
) else (
    echo ❌ composer.json 不存在
)

if exist artisan (
    echo ✅ artisan 文件存在
) else (
    echo ❌ artisan 文件不存在
)

if exist .env (
    echo ✅ .env 文件存在
) else if exist .env.example (
    echo ⚠️ .env 不存在，但 .env.example 存在
) else (
    echo ❌ 环境配置文件都不存在
)

if exist vendor (
    echo ✅ vendor 目录存在
) else (
    echo ❌ vendor 目录不存在，需要运行 composer install
)

echo.
echo [检查4] 宝塔面板服务检查
echo ----------------------------------------

:: 检查MySQL
tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ MySQL 服务运行中
) else (
    echo ❌ MySQL 服务未运行
)

:: 检查Redis
tasklist /FI "IMAGENAME eq redis-server.exe" 2>NUL | find /I /N "redis-server.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ Redis 服务运行中
) else (
    echo ❌ Redis 服务未运行
)

:: 检查Nginx
tasklist /FI "IMAGENAME eq nginx.exe" 2>NUL | find /I /N "nginx.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ Nginx 服务运行中
) else (
    echo ❌ Nginx 服务未运行
)

echo.
echo [检查5] 网络连接检查
echo ----------------------------------------
ping -n 1 packagist.phpcomposer.com >nul 2>&1
if %errorlevel%==0 (
    echo ✅ 网络连接正常
) else (
    echo ❌ 网络连接有问题
)

echo.
echo [检查6] 端口检查
echo ----------------------------------------
netstat -an | findstr ":80 " >nul
if %errorlevel%==0 (
    echo ✅ 端口80已监听
) else (
    echo ❌ 端口80未监听
)

netstat -an | findstr ":3306 " >nul
if %errorlevel%==0 (
    echo ✅ MySQL端口3306已监听
) else (
    echo ❌ MySQL端口3306未监听
)

netstat -an | findstr ":6379 " >nul
if %errorlevel%==0 (
    echo ✅ Redis端口6379已监听
) else (
    echo ❌ Redis端口6379未监听
)

echo.
echo ========================================
echo    检查完成
echo ========================================
echo.

if %PHP_FOUND%==1 (
    echo 建议操作：
    echo 1. 如果vendor目录不存在，运行: deploy.bat
    echo 2. 如果需要WebSocket功能，运行: setup_websocket.bat
    echo 3. 在宝塔面板中创建网站，根目录指向: %PROJECT_DIR%\public
) else (
    echo 请先安装宝塔面板和PHP环境
)

echo.
pause

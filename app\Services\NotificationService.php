<?php

namespace App\Services;

use App\Models\ChatSession;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class NotificationService
{
    /**
     * Broadcast agent status change
     */
    public function broadcastAgentStatusChange(User $agent)
    {
        $data = [
            'type' => 'agent_status_change',
            'agent_id' => $agent->id,
            'agent_name' => $agent->name,
            'status' => $agent->status,
            'status_message' => $agent->status_message,
            'timestamp' => now()->toISOString(),
        ];

        // Broadcast to all agents in the same site
        $this->broadcastToSite($agent->site_id, 'agent_status', $data);

        Log::info('Agent status change broadcasted', [
            'agent_id' => $agent->id,
            'status' => $agent->status,
        ]);
    }

    /**
     * Notify visitor that agent joined
     */
    public function notifyVisitorAgentJoined(ChatSession $session, User $agent)
    {
        $data = [
            'type' => 'agent_joined',
            'session_id' => $session->session_id,
            'agent_id' => $agent->id,
            'agent_name' => $agent->name,
            'agent_avatar' => $agent->avatar,
            'timestamp' => now()->toISOString(),
        ];

        // Send to visitor
        $this->sendToVisitor($session->visitor_id, $data);

        Log::info('Agent joined notification sent', [
            'session_id' => $session->session_id,
            'agent_id' => $agent->id,
            'visitor_id' => $session->visitor_id,
        ]);
    }

    /**
     * Notify about chat transfer
     */
    public function notifyChatTransferred(ChatSession $session, User $fromAgent, User $toAgent)
    {
        // Notify the target agent
        $agentData = [
            'type' => 'chat_transferred_to_you',
            'session_id' => $session->session_id,
            'from_agent' => $fromAgent->name,
            'visitor_name' => $session->visitor->name ?? 'Anonymous',
            'transfer_reason' => $session->transfer_reason,
            'timestamp' => now()->toISOString(),
        ];

        $this->sendToAgent($toAgent->id, $agentData);

        // Notify the visitor
        $visitorData = [
            'type' => 'chat_transferred',
            'session_id' => $session->session_id,
            'new_agent_name' => $toAgent->name,
            'new_agent_avatar' => $toAgent->avatar,
            'timestamp' => now()->toISOString(),
        ];

        $this->sendToVisitor($session->visitor_id, $visitorData);

        // Notify other agents in the site
        $siteData = [
            'type' => 'chat_transferred',
            'session_id' => $session->session_id,
            'from_agent' => $fromAgent->name,
            'to_agent' => $toAgent->name,
            'timestamp' => now()->toISOString(),
        ];

        $this->broadcastToSite($session->site_id, 'chat_transfer', $siteData, [$fromAgent->id, $toAgent->id]);

        Log::info('Chat transfer notifications sent', [
            'session_id' => $session->session_id,
            'from_agent' => $fromAgent->id,
            'to_agent' => $toAgent->id,
        ]);
    }

    /**
     * Notify visitor that chat ended
     */
    public function notifyVisitorChatEnded(ChatSession $session)
    {
        $data = [
            'type' => 'chat_ended',
            'session_id' => $session->session_id,
            'end_reason' => $session->end_reason,
            'timestamp' => now()->toISOString(),
        ];

        $this->sendToVisitor($session->visitor_id, $data);

        Log::info('Chat ended notification sent', [
            'session_id' => $session->session_id,
            'visitor_id' => $session->visitor_id,
        ]);
    }

    /**
     * Notify about new chat request
     */
    public function notifyNewChatRequest(ChatSession $session)
    {
        $data = [
            'type' => 'new_chat_request',
            'session_id' => $session->session_id,
            'visitor_name' => $session->visitor->name ?? 'Anonymous',
            'visitor_email' => $session->visitor->email ?? null,
            'subject' => $session->subject,
            'waiting_time' => now()->diffInSeconds($session->created_at),
            'timestamp' => now()->toISOString(),
        ];

        // Broadcast to all available agents
        $this->broadcastToAvailableAgents($session->site_id, $data);

        Log::info('New chat request notification sent', [
            'session_id' => $session->session_id,
            'site_id' => $session->site_id,
        ]);
    }

    /**
     * Notify about new message
     */
    public function notifyNewMessage($sessionId, $message, $senderType, $recipientType)
    {
        $data = [
            'type' => 'new_message',
            'session_id' => $sessionId,
            'message_id' => $message->message_id,
            'content' => $message->content,
            'sender_type' => $senderType,
            'sender_name' => $message->sender_name,
            'timestamp' => $message->created_at->toISOString(),
        ];

        if ($recipientType === 'visitor') {
            $session = ChatSession::where('session_id', $sessionId)->first();
            if ($session) {
                $this->sendToVisitor($session->visitor_id, $data);
            }
        } elseif ($recipientType === 'agent') {
            $session = ChatSession::where('session_id', $sessionId)->first();
            if ($session && $session->agent_id) {
                $this->sendToAgent($session->agent_id, $data);
            }
        }

        Log::info('New message notification sent', [
            'session_id' => $sessionId,
            'sender_type' => $senderType,
            'recipient_type' => $recipientType,
        ]);
    }

    /**
     * Send notification to specific agent
     */
    private function sendToAgent($agentId, array $data)
    {
        $channel = "agent:{$agentId}";
        $this->publishToChannel($channel, $data);
    }

    /**
     * Send notification to specific visitor
     */
    private function sendToVisitor($visitorId, array $data)
    {
        $channel = "visitor:{$visitorId}";
        $this->publishToChannel($channel, $data);
    }

    /**
     * Broadcast to all agents in a site
     */
    private function broadcastToSite($siteId, $eventType, array $data, array $excludeAgents = [])
    {
        $agents = User::where('site_id', $siteId)
            ->where('role', 'agent')
            ->where('status', '!=', 'offline')
            ->whereNotIn('id', $excludeAgents)
            ->get();

        foreach ($agents as $agent) {
            $this->sendToAgent($agent->id, $data);
        }
    }

    /**
     * Broadcast to available agents
     */
    private function broadcastToAvailableAgents($siteId, array $data)
    {
        $agents = User::where('site_id', $siteId)
            ->where('role', 'agent')
            ->where('status', 'online')
            ->get();

        foreach ($agents as $agent) {
            // Check if agent has capacity for more chats
            $activeSessions = ChatSession::where('agent_id', $agent->id)
                ->where('status', 'active')
                ->count();

            if ($activeSessions < ($agent->max_concurrent_chats ?? 5)) {
                $this->sendToAgent($agent->id, $data);
            }
        }
    }

    /**
     * Publish message to Redis channel
     */
    private function publishToChannel($channel, array $data)
    {
        try {
            Redis::publish($channel, json_encode($data));
        } catch (\Exception $e) {
            Log::error('Failed to publish notification', [
                'channel' => $channel,
                'data' => $data,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send system notification
     */
    public function sendSystemNotification($siteId, $type, $title, $message, $targetType = 'all', $targetIds = [])
    {
        $data = [
            'type' => 'system_notification',
            'notification_type' => $type,
            'title' => $title,
            'message' => $message,
            'timestamp' => now()->toISOString(),
        ];

        switch ($targetType) {
            case 'all':
                $this->broadcastToSite($siteId, 'system_notification', $data);
                break;
            case 'agents':
                if (empty($targetIds)) {
                    $this->broadcastToSite($siteId, 'system_notification', $data);
                } else {
                    foreach ($targetIds as $agentId) {
                        $this->sendToAgent($agentId, $data);
                    }
                }
                break;
            case 'visitors':
                foreach ($targetIds as $visitorId) {
                    $this->sendToVisitor($visitorId, $data);
                }
                break;
        }

        Log::info('System notification sent', [
            'site_id' => $siteId,
            'type' => $type,
            'target_type' => $targetType,
            'target_count' => count($targetIds),
        ]);
    }

    /**
     * Send typing indicator
     */
    public function sendTypingIndicator($sessionId, $senderType, $isTyping = true)
    {
        $session = ChatSession::where('session_id', $sessionId)->first();
        if (!$session) {
            return;
        }

        $data = [
            'type' => 'typing_indicator',
            'session_id' => $sessionId,
            'sender_type' => $senderType,
            'is_typing' => $isTyping,
            'timestamp' => now()->toISOString(),
        ];

        if ($senderType === 'visitor' && $session->agent_id) {
            $this->sendToAgent($session->agent_id, $data);
        } elseif ($senderType === 'agent') {
            $this->sendToVisitor($session->visitor_id, $data);
        }
    }

    /**
     * Send file upload notification
     */
    public function notifyFileUploaded($sessionId, $fileName, $fileSize, $senderType)
    {
        $session = ChatSession::where('session_id', $sessionId)->first();
        if (!$session) {
            return;
        }

        $data = [
            'type' => 'file_uploaded',
            'session_id' => $sessionId,
            'file_name' => $fileName,
            'file_size' => $fileSize,
            'sender_type' => $senderType,
            'timestamp' => now()->toISOString(),
        ];

        if ($senderType === 'visitor' && $session->agent_id) {
            $this->sendToAgent($session->agent_id, $data);
        } elseif ($senderType === 'agent') {
            $this->sendToVisitor($session->visitor_id, $data);
        }

        Log::info('File upload notification sent', [
            'session_id' => $sessionId,
            'file_name' => $fileName,
            'sender_type' => $senderType,
        ]);
    }
}

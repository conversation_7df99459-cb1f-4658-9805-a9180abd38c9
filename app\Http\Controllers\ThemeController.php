<?php

namespace App\Http\Controllers;

use App\Services\ThemeService;
use App\Models\Theme;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class ThemeController extends Controller
{
    protected $themeService;

    public function __construct(ThemeService $themeService)
    {
        $this->themeService = $themeService;
    }

    /**
     * Get all themes for a site
     */
    public function index(Request $request, $siteId = null): JsonResponse
    {
        try {
            $query = Theme::query();
            
            if ($siteId) {
                $query->where('site_id', $siteId);
            }
            
            $themes = $query->orderBy('is_active', 'desc')
                ->orderBy('is_default', 'desc')
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $themes,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get themes: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get active theme for site
     */
    public function getActiveTheme($siteId): JsonResponse
    {
        try {
            $theme = $this->themeService->getActiveTheme($siteId);

            return response()->json([
                'success' => true,
                'data' => $theme,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get active theme: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get theme by ID
     */
    public function show($themeId): JsonResponse
    {
        try {
            $theme = Theme::where('theme_id', $themeId)->firstOrFail();

            return response()->json([
                'success' => true,
                'data' => $theme,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found',
            ], 404);
        }
    }

    /**
     * Create new theme
     */
    public function store(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'color_scheme' => 'nullable|array',
            'typography' => 'nullable|array',
            'layout_settings' => 'nullable|array',
            'button_styles' => 'nullable|array',
            'chat_bubble_styles' => 'nullable|array',
            'animation_settings' => 'nullable|array',
            'responsive_settings' => 'nullable|array',
            'background_settings' => 'nullable|array',
            'custom_css' => 'nullable|string',
        ]);

        try {
            $theme = $this->themeService->createTheme($siteId, $request->all());

            return response()->json([
                'success' => true,
                'data' => $theme,
                'message' => 'Theme created successfully',
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create theme: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update theme
     */
    public function update(Request $request, $themeId): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'color_scheme' => 'nullable|array',
            'typography' => 'nullable|array',
            'layout_settings' => 'nullable|array',
            'button_styles' => 'nullable|array',
            'chat_bubble_styles' => 'nullable|array',
            'animation_settings' => 'nullable|array',
            'responsive_settings' => 'nullable|array',
            'background_settings' => 'nullable|array',
            'custom_css' => 'nullable|string',
        ]);

        try {
            $theme = $this->themeService->updateTheme($themeId, $request->all());

            return response()->json([
                'success' => true,
                'data' => $theme,
                'message' => 'Theme updated successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update theme: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Activate theme
     */
    public function activate($themeId): JsonResponse
    {
        try {
            $theme = $this->themeService->activateTheme($themeId);

            return response()->json([
                'success' => true,
                'data' => $theme,
                'message' => 'Theme activated successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to activate theme: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Clone theme
     */
    public function clone(Request $request, $themeId): JsonResponse
    {
        $request->validate([
            'name' => 'nullable|string|max:255',
        ]);

        try {
            $theme = $this->themeService->cloneTheme($themeId, $request->name);

            return response()->json([
                'success' => true,
                'data' => $theme,
                'message' => 'Theme cloned successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clone theme: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete theme
     */
    public function destroy($themeId): JsonResponse
    {
        try {
            $theme = Theme::where('theme_id', $themeId)->firstOrFail();
            
            if ($theme->is_active) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete active theme',
                ], 400);
            }
            
            if ($theme->is_default) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete default theme',
                ], 400);
            }

            $theme->delete();

            return response()->json([
                'success' => true,
                'message' => 'Theme deleted successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete theme: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Upload logo
     */
    public function uploadLogo(Request $request, $themeId): JsonResponse
    {
        $request->validate([
            'logo' => 'required|image|mimes:jpeg,png,gif,webp|max:2048',
        ]);

        try {
            $theme = $this->themeService->uploadLogo($themeId, $request->file('logo'));

            return response()->json([
                'success' => true,
                'data' => $theme,
                'message' => 'Logo uploaded successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload logo: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Upload favicon
     */
    public function uploadFavicon(Request $request, $themeId): JsonResponse
    {
        $request->validate([
            'favicon' => 'required|image|mimes:jpeg,png,gif,webp,ico|max:1024',
        ]);

        try {
            $theme = $this->themeService->uploadFavicon($themeId, $request->file('favicon'));

            return response()->json([
                'success' => true,
                'data' => $theme,
                'message' => 'Favicon uploaded successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload favicon: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Generate theme CSS
     */
    public function generateCss($themeId): JsonResponse
    {
        try {
            $result = $this->themeService->generateThemeCss($themeId);

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate CSS: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get theme preview
     */
    public function preview($themeId): JsonResponse
    {
        try {
            $preview = $this->themeService->getThemePreview($themeId);

            return response()->json([
                'success' => true,
                'data' => $preview,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate preview: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get predefined themes
     */
    public function getPredefinedThemes(): JsonResponse
    {
        try {
            $themes = $this->themeService->getPredefinedThemes();

            return response()->json([
                'success' => true,
                'data' => $themes,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get predefined themes: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Apply predefined theme
     */
    public function applyPredefinedTheme(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'theme_key' => 'required|string',
        ]);

        try {
            $theme = $this->themeService->applyPredefinedTheme($siteId, $request->theme_key);

            return response()->json([
                'success' => true,
                'data' => $theme,
                'message' => 'Predefined theme applied successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to apply predefined theme: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export theme
     */
    public function export($themeId): JsonResponse
    {
        try {
            $exportData = $this->themeService->exportTheme($themeId);

            return response()->json([
                'success' => true,
                'data' => $exportData,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export theme: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Import theme
     */
    public function import(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'theme_data' => 'required|array',
            'theme_data.name' => 'required|string|max:255',
        ]);

        try {
            $theme = $this->themeService->importTheme($siteId, $request->theme_data);

            return response()->json([
                'success' => true,
                'data' => $theme,
                'message' => 'Theme imported successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to import theme: ' . $e->getMessage(),
            ], 500);
        }
    }
}

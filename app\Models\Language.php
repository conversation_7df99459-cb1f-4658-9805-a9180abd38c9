<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Language extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'st_languages';

    protected $fillable = [
        'language_id',
        'site_id',
        'code',
        'name',
        'native_name',
        'flag_icon',
        'is_active',
        'is_default',
        'is_rtl',
        'date_format',
        'time_format',
        'currency_code',
        'currency_symbol',
        'number_format',
        'timezone',
        'metadata',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'is_rtl' => 'boolean',
        'metadata' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($language) {
            if (empty($language->language_id)) {
                $language->language_id = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the site that owns the language
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Get translations for this language
     */
    public function translations()
    {
        return $this->hasMany(Translation::class);
    }

    /**
     * Scope to get active languages
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get default language
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Get available languages list
     */
    public static function getAvailableLanguages()
    {
        return [
            'en' => [
                'name' => 'English',
                'native_name' => 'English',
                'flag_icon' => '🇺🇸',
                'is_rtl' => false,
                'date_format' => 'Y-m-d',
                'time_format' => 'H:i:s',
                'currency_code' => 'USD',
                'currency_symbol' => '$',
                'number_format' => '1,234.56',
                'timezone' => 'UTC',
            ],
            'zh' => [
                'name' => 'Chinese (Simplified)',
                'native_name' => '简体中文',
                'flag_icon' => '🇨🇳',
                'is_rtl' => false,
                'date_format' => 'Y年m月d日',
                'time_format' => 'H:i:s',
                'currency_code' => 'CNY',
                'currency_symbol' => '¥',
                'number_format' => '1,234.56',
                'timezone' => 'Asia/Shanghai',
            ],
            'zh-TW' => [
                'name' => 'Chinese (Traditional)',
                'native_name' => '繁體中文',
                'flag_icon' => '🇹🇼',
                'is_rtl' => false,
                'date_format' => 'Y年m月d日',
                'time_format' => 'H:i:s',
                'currency_code' => 'TWD',
                'currency_symbol' => 'NT$',
                'number_format' => '1,234.56',
                'timezone' => 'Asia/Taipei',
            ],
            'ja' => [
                'name' => 'Japanese',
                'native_name' => '日本語',
                'flag_icon' => '🇯🇵',
                'is_rtl' => false,
                'date_format' => 'Y年m月d日',
                'time_format' => 'H:i:s',
                'currency_code' => 'JPY',
                'currency_symbol' => '¥',
                'number_format' => '1,234',
                'timezone' => 'Asia/Tokyo',
            ],
            'ko' => [
                'name' => 'Korean',
                'native_name' => '한국어',
                'flag_icon' => '🇰🇷',
                'is_rtl' => false,
                'date_format' => 'Y년 m월 d일',
                'time_format' => 'H:i:s',
                'currency_code' => 'KRW',
                'currency_symbol' => '₩',
                'number_format' => '1,234',
                'timezone' => 'Asia/Seoul',
            ],
            'es' => [
                'name' => 'Spanish',
                'native_name' => 'Español',
                'flag_icon' => '🇪🇸',
                'is_rtl' => false,
                'date_format' => 'd/m/Y',
                'time_format' => 'H:i:s',
                'currency_code' => 'EUR',
                'currency_symbol' => '€',
                'number_format' => '1.234,56',
                'timezone' => 'Europe/Madrid',
            ],
            'fr' => [
                'name' => 'French',
                'native_name' => 'Français',
                'flag_icon' => '🇫🇷',
                'is_rtl' => false,
                'date_format' => 'd/m/Y',
                'time_format' => 'H:i:s',
                'currency_code' => 'EUR',
                'currency_symbol' => '€',
                'number_format' => '1 234,56',
                'timezone' => 'Europe/Paris',
            ],
            'de' => [
                'name' => 'German',
                'native_name' => 'Deutsch',
                'flag_icon' => '🇩🇪',
                'is_rtl' => false,
                'date_format' => 'd.m.Y',
                'time_format' => 'H:i:s',
                'currency_code' => 'EUR',
                'currency_symbol' => '€',
                'number_format' => '1.234,56',
                'timezone' => 'Europe/Berlin',
            ],
            'it' => [
                'name' => 'Italian',
                'native_name' => 'Italiano',
                'flag_icon' => '🇮🇹',
                'is_rtl' => false,
                'date_format' => 'd/m/Y',
                'time_format' => 'H:i:s',
                'currency_code' => 'EUR',
                'currency_symbol' => '€',
                'number_format' => '1.234,56',
                'timezone' => 'Europe/Rome',
            ],
            'pt' => [
                'name' => 'Portuguese',
                'native_name' => 'Português',
                'flag_icon' => '🇵🇹',
                'is_rtl' => false,
                'date_format' => 'd/m/Y',
                'time_format' => 'H:i:s',
                'currency_code' => 'EUR',
                'currency_symbol' => '€',
                'number_format' => '1.234,56',
                'timezone' => 'Europe/Lisbon',
            ],
            'ru' => [
                'name' => 'Russian',
                'native_name' => 'Русский',
                'flag_icon' => '🇷🇺',
                'is_rtl' => false,
                'date_format' => 'd.m.Y',
                'time_format' => 'H:i:s',
                'currency_code' => 'RUB',
                'currency_symbol' => '₽',
                'number_format' => '1 234,56',
                'timezone' => 'Europe/Moscow',
            ],
            'ar' => [
                'name' => 'Arabic',
                'native_name' => 'العربية',
                'flag_icon' => '🇸🇦',
                'is_rtl' => true,
                'date_format' => 'd/m/Y',
                'time_format' => 'H:i:s',
                'currency_code' => 'SAR',
                'currency_symbol' => 'ر.س',
                'number_format' => '1,234.56',
                'timezone' => 'Asia/Riyadh',
            ],
            'hi' => [
                'name' => 'Hindi',
                'native_name' => 'हिन्दी',
                'flag_icon' => '🇮🇳',
                'is_rtl' => false,
                'date_format' => 'd/m/Y',
                'time_format' => 'H:i:s',
                'currency_code' => 'INR',
                'currency_symbol' => '₹',
                'number_format' => '1,23,456.78',
                'timezone' => 'Asia/Kolkata',
            ],
            'th' => [
                'name' => 'Thai',
                'native_name' => 'ไทย',
                'flag_icon' => '🇹🇭',
                'is_rtl' => false,
                'date_format' => 'd/m/Y',
                'time_format' => 'H:i:s',
                'currency_code' => 'THB',
                'currency_symbol' => '฿',
                'number_format' => '1,234.56',
                'timezone' => 'Asia/Bangkok',
            ],
            'vi' => [
                'name' => 'Vietnamese',
                'native_name' => 'Tiếng Việt',
                'flag_icon' => '🇻🇳',
                'is_rtl' => false,
                'date_format' => 'd/m/Y',
                'time_format' => 'H:i:s',
                'currency_code' => 'VND',
                'currency_symbol' => '₫',
                'number_format' => '1.234,56',
                'timezone' => 'Asia/Ho_Chi_Minh',
            ],
        ];
    }

    /**
     * Set as default language for site
     */
    public function setAsDefault()
    {
        // Deactivate other default languages for this site
        static::where('site_id', $this->site_id)
            ->where('id', '!=', $this->id)
            ->update(['is_default' => false]);

        // Set this language as default
        $this->update(['is_default' => true, 'is_active' => true]);
    }

    /**
     * Format date according to language settings
     */
    public function formatDate($date, $format = null)
    {
        $format = $format ?? $this->date_format;
        return $date->format($format);
    }

    /**
     * Format time according to language settings
     */
    public function formatTime($time, $format = null)
    {
        $format = $format ?? $this->time_format;
        return $time->format($format);
    }

    /**
     * Format number according to language settings
     */
    public function formatNumber($number, $decimals = 2)
    {
        switch ($this->code) {
            case 'en':
            case 'zh':
            case 'zh-TW':
            case 'ja':
            case 'ko':
            case 'ar':
            case 'hi':
            case 'th':
            case 'vi':
                return number_format($number, $decimals, '.', ',');
            case 'es':
            case 'fr':
            case 'de':
            case 'it':
            case 'pt':
            case 'ru':
                return number_format($number, $decimals, ',', '.');
            default:
                return number_format($number, $decimals);
        }
    }

    /**
     * Format currency according to language settings
     */
    public function formatCurrency($amount, $showSymbol = true)
    {
        $formatted = $this->formatNumber($amount, 2);
        
        if (!$showSymbol) {
            return $formatted;
        }

        $symbol = $this->currency_symbol;
        
        // Position currency symbol based on language
        switch ($this->code) {
            case 'en':
                return $symbol . $formatted;
            case 'zh':
            case 'zh-TW':
            case 'ja':
            case 'ko':
                return $symbol . $formatted;
            case 'es':
            case 'fr':
            case 'de':
            case 'it':
            case 'pt':
            case 'ru':
                return $formatted . ' ' . $symbol;
            case 'ar':
                return $formatted . ' ' . $symbol;
            case 'hi':
            case 'th':
            case 'vi':
                return $symbol . $formatted;
            default:
                return $symbol . $formatted;
        }
    }

    /**
     * Get text direction
     */
    public function getTextDirection()
    {
        return $this->is_rtl ? 'rtl' : 'ltr';
    }

    /**
     * Clone language for another site
     */
    public function cloneForSite($siteId)
    {
        $clone = $this->replicate();
        $clone->language_id = (string) Str::uuid();
        $clone->site_id = $siteId;
        $clone->is_default = false;
        $clone->save();

        // Clone translations
        foreach ($this->translations as $translation) {
            $translationClone = $translation->replicate();
            $translationClone->translation_id = (string) Str::uuid();
            $translationClone->language_id = $clone->id;
            $translationClone->save();
        }

        return $clone;
    }
}

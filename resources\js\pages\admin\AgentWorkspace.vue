<template>
  <div class="h-full flex">
    <!-- Left sidebar - Session list -->
    <div class="w-80 bg-white border-r border-gray-200 flex flex-col">
      <!-- Header -->
      <div class="flex-shrink-0 px-4 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900">Chat Sessions</h2>
          <button @click="refreshSessions" :disabled="isLoading" class="p-2 text-gray-400 hover:text-gray-600">
            <ArrowPathIcon class="h-5 w-5" :class="{ 'animate-spin': isLoading }" />
          </button>
        </div>
        
        <!-- Status selector -->
        <div class="mt-3">
          <select v-model="currentStatus" @change="updateAgentStatus" class="form-select text-sm">
            <option value="online">🟢 Online</option>
            <option value="away">🟡 Away</option>
            <option value="busy">🔴 Busy</option>
            <option value="offline">⚫ Offline</option>
          </select>
        </div>
      </div>

      <!-- Session tabs -->
      <div class="flex-shrink-0 border-b border-gray-200">
        <nav class="flex -mb-px">
          <button
            @click="activeTab = 'waiting'"
            class="flex-1 py-2 px-4 text-center border-b-2 font-medium text-sm"
            :class="activeTab === 'waiting' 
              ? 'border-blue-500 text-blue-600' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          >
            Waiting ({{ waitingSessions.length }})
          </button>
          <button
            @click="activeTab = 'active'"
            class="flex-1 py-2 px-4 text-center border-b-2 font-medium text-sm"
            :class="activeTab === 'active' 
              ? 'border-blue-500 text-blue-600' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          >
            Active ({{ activeSessions.length }})
          </button>
        </nav>
      </div>

      <!-- Session list -->
      <div class="flex-1 overflow-y-auto">
        <div v-if="currentSessions.length === 0" class="p-4 text-center text-gray-500">
          <ChatBubbleLeftRightIcon class="mx-auto h-12 w-12 text-gray-300" />
          <p class="mt-2">No {{ activeTab }} sessions</p>
        </div>
        
        <div v-else class="divide-y divide-gray-200">
          <div
            v-for="session in currentSessions"
            :key="session.session_id"
            @click="selectSession(session)"
            class="p-4 hover:bg-gray-50 cursor-pointer"
            :class="{ 'bg-blue-50 border-r-2 border-blue-500': selectedSession?.session_id === session.session_id }"
          >
            <div class="flex items-start space-x-3">
              <img :src="session.visitor_avatar || '/images/default-avatar.png'" 
                   :alt="session.visitor_name" 
                   class="h-8 w-8 rounded-full">
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <p class="text-sm font-medium text-gray-900 truncate">
                    {{ session.visitor_name || 'Anonymous' }}
                  </p>
                  <span class="text-xs text-gray-500">
                    {{ formatTime(session.created_at) }}
                  </span>
                </div>
                <p class="text-sm text-gray-500 truncate">
                  {{ session.subject || 'No subject' }}
                </p>
                <p v-if="session.last_message" class="text-xs text-gray-400 truncate mt-1">
                  {{ session.last_message }}
                </p>
                <div class="flex items-center justify-between mt-2">
                  <span class="badge badge-sm" :class="getStatusBadgeClass(session.status)">
                    {{ session.status }}
                  </span>
                  <div class="flex space-x-1">
                    <button
                      v-if="session.status === 'waiting'"
                      @click.stop="acceptChat(session.session_id)"
                      class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded hover:bg-green-200"
                    >
                      Accept
                    </button>
                    <button
                      v-if="session.status === 'active'"
                      @click.stop="showTransferModal(session)"
                      class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded hover:bg-blue-200"
                    >
                      Transfer
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main chat area -->
    <div class="flex-1 flex flex-col">
      <div v-if="!selectedSession" class="flex-1 flex items-center justify-center bg-gray-50">
        <div class="text-center">
          <ChatBubbleLeftRightIcon class="mx-auto h-12 w-12 text-gray-300" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">No session selected</h3>
          <p class="mt-1 text-sm text-gray-500">Select a chat session to start messaging</p>
        </div>
      </div>

      <div v-else class="flex-1 flex flex-col">
        <!-- Chat header -->
        <div class="flex-shrink-0 px-6 py-4 border-b border-gray-200 bg-white">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <img :src="selectedSession.visitor_avatar || '/images/default-avatar.png'" 
                   :alt="selectedSession.visitor_name" 
                   class="h-10 w-10 rounded-full">
              <div>
                <h3 class="text-lg font-medium text-gray-900">
                  {{ selectedSession.visitor_name || 'Anonymous' }}
                </h3>
                <p class="text-sm text-gray-500">
                  {{ selectedSession.visitor_email || 'No email' }}
                </p>
              </div>
            </div>
            
            <div class="flex items-center space-x-2">
              <button
                v-if="selectedSession.status === 'active'"
                @click="showTransferModal(selectedSession)"
                class="btn btn-outline btn-sm"
              >
                Transfer
              </button>
              <button
                v-if="selectedSession.status === 'active'"
                @click="showEndChatModal(selectedSession)"
                class="btn btn-danger btn-sm"
              >
                End Chat
              </button>
            </div>
          </div>
        </div>

        <!-- Messages area -->
        <div class="flex-1 overflow-y-auto p-6 space-y-4" ref="messagesContainer">
          <div
            v-for="message in messages"
            :key="message.id"
            class="flex"
            :class="message.sender_type === 'agent' ? 'justify-end' : 'justify-start'"
          >
            <div class="max-w-xs lg:max-w-md">
              <div
                class="px-4 py-2 rounded-lg"
                :class="message.sender_type === 'agent' 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-200 text-gray-900'"
              >
                <p class="text-sm">{{ message.content }}</p>
                <p class="text-xs mt-1 opacity-75">
                  {{ formatTime(message.created_at) }}
                </p>
              </div>
            </div>
          </div>
          
          <!-- Typing indicator -->
          <div v-if="typingUsers.length > 0" class="flex justify-start">
            <div class="max-w-xs lg:max-w-md">
              <div class="px-4 py-2 rounded-lg bg-gray-200">
                <div class="flex space-x-1">
                  <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce"></div>
                  <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                  <div class="w-2 h-2 bg-gray-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Message input -->
        <div class="flex-shrink-0 border-t border-gray-200 px-6 py-4 bg-white">
          <form @submit.prevent="sendMessage" class="flex space-x-3">
            <div class="flex-1">
              <textarea
                v-model="newMessage"
                @keydown="handleKeyDown"
                @input="handleTyping"
                placeholder="Type your message..."
                rows="2"
                class="form-textarea resize-none"
              ></textarea>
            </div>
            <button
              type="submit"
              :disabled="!newMessage.trim() || sendingMessage"
              class="btn btn-primary"
            >
              <PaperAirplaneIcon class="h-5 w-5" />
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- Transfer Modal -->
    <TransferChatModal
      v-if="showTransferModalFlag"
      :session="sessionToTransfer"
      @close="showTransferModalFlag = false"
      @transfer="handleTransfer"
    />

    <!-- End Chat Modal -->
    <EndChatModal
      v-if="showEndChatModalFlag"
      :session="sessionToEnd"
      @close="showEndChatModalFlag = false"
      @end="handleEndChat"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useChatStore } from '../../stores/chat.js'
import { useAuthStore } from '../../stores/auth.js'
import { useNotificationStore } from '../../stores/notifications.js'

// Icons
import {
  ArrowPathIcon,
  ChatBubbleLeftRightIcon,
  PaperAirplaneIcon
} from '@heroicons/vue/24/outline'

// Components
import TransferChatModal from '../../components/modals/TransferChatModal.vue'
import EndChatModal from '../../components/modals/EndChatModal.vue'

export default {
  name: 'AgentWorkspace',
  components: {
    ArrowPathIcon,
    ChatBubbleLeftRightIcon,
    PaperAirplaneIcon,
    TransferChatModal,
    EndChatModal
  },
  setup() {
    const chatStore = useChatStore()
    const authStore = useAuthStore()
    const notificationStore = useNotificationStore()

    // State
    const activeTab = ref('waiting')
    const selectedSession = ref(null)
    const newMessage = ref('')
    const sendingMessage = ref(false)
    const currentStatus = ref(authStore.user?.status || 'online')
    const messagesContainer = ref(null)
    const typingTimer = ref(null)
    const isTyping = ref(false)

    // Modal states
    const showTransferModalFlag = ref(false)
    const showEndChatModalFlag = ref(false)
    const sessionToTransfer = ref(null)
    const sessionToEnd = ref(null)

    // Computed
    const isLoading = computed(() => chatStore.isLoading)
    const activeSessions = computed(() => chatStore.activeSessions)
    const waitingSessions = computed(() => chatStore.waitingSessions)
    const messages = computed(() => chatStore.messages)
    const typingUsers = computed(() => chatStore.typingUsers)

    const currentSessions = computed(() => {
      return activeTab.value === 'waiting' ? waitingSessions.value : activeSessions.value
    })

    // Methods
    const refreshSessions = async () => {
      await chatStore.fetchWorkspaceOverview()
    }

    const selectSession = async (session) => {
      selectedSession.value = session
      await chatStore.loadChatSession(session.session_id)
      scrollToBottom()
    }

    const acceptChat = async (sessionId) => {
      await chatStore.acceptChat(sessionId)
      // Switch to active tab and select the session
      activeTab.value = 'active'
      const session = activeSessions.value.find(s => s.session_id === sessionId)
      if (session) {
        await selectSession(session)
      }
    }

    const sendMessage = async () => {
      if (!newMessage.value.trim() || !selectedSession.value || sendingMessage.value) {
        return
      }

      try {
        sendingMessage.value = true
        await chatStore.sendMessage(selectedSession.value.session_id, newMessage.value.trim())
        newMessage.value = ''
        scrollToBottom()
      } catch (error) {
        console.error('Failed to send message:', error)
      } finally {
        sendingMessage.value = false
      }
    }

    const handleKeyDown = (event) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        sendMessage()
      }
    }

    const handleTyping = () => {
      if (!selectedSession.value) return

      if (!isTyping.value) {
        isTyping.value = true
        chatStore.updateTypingStatus(selectedSession.value.session_id, true)
      }

      // Clear existing timer
      if (typingTimer.value) {
        clearTimeout(typingTimer.value)
      }

      // Set new timer to stop typing indicator
      typingTimer.value = setTimeout(() => {
        isTyping.value = false
        chatStore.updateTypingStatus(selectedSession.value.session_id, false)
      }, 1000)
    }

    const updateAgentStatus = async () => {
      try {
        const result = await authStore.updateStatus(currentStatus.value)
        if (result.success) {
          notificationStore.success('Status Updated', `Your status has been set to ${currentStatus.value}`)
        } else {
          notificationStore.error('Error', result.message)
          // Revert status on error
          currentStatus.value = authStore.user?.status || 'online'
        }
      } catch (error) {
        console.error('Failed to update status:', error)
        notificationStore.error('Error', 'Failed to update status')
        currentStatus.value = authStore.user?.status || 'online'
      }
    }

    const showTransferModal = (session) => {
      sessionToTransfer.value = session
      showTransferModalFlag.value = true
    }

    const showEndChatModal = (session) => {
      sessionToEnd.value = session
      showEndChatModalFlag.value = true
    }

    const handleTransfer = async (data) => {
      await chatStore.transferChat(data.sessionId, data.targetAgentId, data.reason)
      showTransferModalFlag.value = false
      sessionToTransfer.value = null
      
      // Clear selected session if it was transferred
      if (selectedSession.value?.session_id === data.sessionId) {
        selectedSession.value = null
        chatStore.clearCurrentSession()
      }
    }

    const handleEndChat = async (data) => {
      await chatStore.endChat(data.sessionId, data.reason, data.internalNote)
      showEndChatModalFlag.value = false
      sessionToEnd.value = null
      
      // Clear selected session if it was ended
      if (selectedSession.value?.session_id === data.sessionId) {
        selectedSession.value = null
        chatStore.clearCurrentSession()
      }
    }

    const scrollToBottom = () => {
      nextTick(() => {
        if (messagesContainer.value) {
          messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
        }
      })
    }

    const getStatusBadgeClass = (status) => {
      switch (status) {
        case 'active':
          return 'badge-success'
        case 'waiting':
          return 'badge-warning'
        case 'ended':
          return 'badge-gray'
        default:
          return 'badge-primary'
      }
    }

    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // Watch for new messages to scroll to bottom
    watch(messages, () => {
      scrollToBottom()
    }, { deep: true })

    // Lifecycle
    onMounted(async () => {
      await refreshSessions()
      
      // Set up periodic refresh
      const refreshInterval = setInterval(refreshSessions, 30000) // 30 seconds
      
      onUnmounted(() => {
        clearInterval(refreshInterval)
        if (typingTimer.value) {
          clearTimeout(typingTimer.value)
        }
      })
    })

    return {
      // State
      activeTab,
      selectedSession,
      newMessage,
      sendingMessage,
      currentStatus,
      messagesContainer,
      showTransferModalFlag,
      showEndChatModalFlag,
      sessionToTransfer,
      sessionToEnd,
      
      // Computed
      isLoading,
      activeSessions,
      waitingSessions,
      messages,
      typingUsers,
      currentSessions,
      
      // Methods
      refreshSessions,
      selectSession,
      acceptChat,
      sendMessage,
      handleKeyDown,
      handleTyping,
      updateAgentStatus,
      showTransferModal,
      showEndChatModal,
      handleTransfer,
      handleEndChat,
      getStatusBadgeClass,
      formatTime
    }
  }
}
</script>

<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
              <UserIcon class="h-6 w-6 text-blue-600" />
            </div>
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                Change Status
              </h3>
              <div class="mt-4">
                <p class="text-sm text-gray-500 mb-4">
                  Update your availability status for chat sessions.
                </p>
                
                <div class="space-y-3">
                  <label
                    v-for="status in statusOptions"
                    :key="status.value"
                    class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                    :class="{ 'border-blue-500 bg-blue-50': selectedStatus === status.value }"
                  >
                    <input
                      v-model="selectedStatus"
                      :value="status.value"
                      type="radio"
                      class="sr-only"
                    />
                    <div class="flex items-center space-x-3">
                      <div class="h-3 w-3 rounded-full" :class="status.color"></div>
                      <div>
                        <div class="text-sm font-medium text-gray-900">{{ status.label }}</div>
                        <div class="text-xs text-gray-500">{{ status.description }}</div>
                      </div>
                    </div>
                  </label>
                </div>

                <!-- Auto-status settings -->
                <div class="mt-6 border-t pt-4">
                  <h4 class="text-sm font-medium text-gray-900 mb-3">Auto Status</h4>
                  
                  <div class="space-y-3">
                    <label class="flex items-center">
                      <input
                        v-model="autoSettings.enableAutoAway"
                        type="checkbox"
                        class="form-checkbox h-4 w-4 text-blue-600"
                      />
                      <span class="ml-2 text-sm text-gray-700">
                        Auto set to Away after
                        <input
                          v-model="autoSettings.awayAfterMinutes"
                          type="number"
                          min="1"
                          max="60"
                          class="mx-1 w-16 px-2 py-1 text-sm border border-gray-300 rounded"
                          :disabled="!autoSettings.enableAutoAway"
                        />
                        minutes of inactivity
                      </span>
                    </label>
                    
                    <label class="flex items-center">
                      <input
                        v-model="autoSettings.enableAutoOffline"
                        type="checkbox"
                        class="form-checkbox h-4 w-4 text-blue-600"
                      />
                      <span class="ml-2 text-sm text-gray-700">
                        Auto set to Offline after
                        <input
                          v-model="autoSettings.offlineAfterMinutes"
                          type="number"
                          min="1"
                          max="120"
                          class="mx-1 w-16 px-2 py-1 text-sm border border-gray-300 rounded"
                          :disabled="!autoSettings.enableAutoOffline"
                        />
                        minutes of inactivity
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="updateStatus"
            :disabled="isLoading"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
          >
            <span v-if="isLoading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Updating...
            </span>
            <span v-else>Update Status</span>
          </button>
          <button
            @click="$emit('close')"
            type="button"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '../../stores/auth.js'
import { useNotificationStore } from '../../stores/notifications.js'
import { UserIcon } from '@heroicons/vue/24/outline'

export default {
  name: 'StatusModal',
  components: {
    UserIcon
  },
  emits: ['close'],
  setup(props, { emit }) {
    const authStore = useAuthStore()
    const notificationStore = useNotificationStore()

    const selectedStatus = ref(authStore.user?.status || 'online')
    const isLoading = ref(false)

    const autoSettings = reactive({
      enableAutoAway: false,
      awayAfterMinutes: 10,
      enableAutoOffline: false,
      offlineAfterMinutes: 30
    })

    const statusOptions = [
      {
        value: 'online',
        label: 'Online',
        description: 'Available to accept new chats',
        color: 'bg-green-400'
      },
      {
        value: 'away',
        label: 'Away',
        description: 'Temporarily unavailable',
        color: 'bg-yellow-400'
      },
      {
        value: 'busy',
        label: 'Busy',
        description: 'Do not disturb - no new chats',
        color: 'bg-red-400'
      },
      {
        value: 'offline',
        label: 'Offline',
        description: 'Not available for chats',
        color: 'bg-gray-400'
      }
    ]

    const updateStatus = async () => {
      try {
        isLoading.value = true

        const result = await authStore.updateStatus(selectedStatus.value, autoSettings)

        if (result.success) {
          notificationStore.success('Status Updated', `Your status has been set to ${selectedStatus.value}`)
          emit('close')
        } else {
          notificationStore.error('Error', result.message)
        }
      } catch (error) {
        console.error('Failed to update status:', error)
        notificationStore.error('Error', 'Failed to update status')
      } finally {
        isLoading.value = false
      }
    }

    onMounted(() => {
      // Load current auto settings if available
      if (authStore.user?.auto_settings) {
        Object.assign(autoSettings, authStore.user.auto_settings)
      }
    })

    return {
      selectedStatus,
      isLoading,
      autoSettings,
      statusOptions,
      updateStatus
    }
  }
}
</script>

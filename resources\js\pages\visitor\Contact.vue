<template>
  <div class="max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:px-8">
    <div class="max-w-lg mx-auto md:max-w-none md:grid md:grid-cols-2 md:gap-8">
      <!-- Contact form -->
      <div>
        <h2 class="text-2xl font-extrabold text-gray-900 sm:text-3xl">
          Send us a message
        </h2>
        <div class="mt-3">
          <p class="text-lg text-gray-500">
            Fill out the form below and we'll get back to you as soon as possible.
          </p>
        </div>
        
        <form @submit.prevent="submitForm" class="mt-9 grid grid-cols-1 gap-y-6">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">Full name</label>
            <div class="mt-1">
              <input
                id="name"
                v-model="form.name"
                type="text"
                required
                class="py-3 px-4 block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md"
                :class="{ 'border-red-300': errors.name }"
              />
              <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
            </div>
          </div>
          
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
            <div class="mt-1">
              <input
                id="email"
                v-model="form.email"
                type="email"
                required
                class="py-3 px-4 block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md"
                :class="{ 'border-red-300': errors.email }"
              />
              <p v-if="errors.email" class="mt-1 text-sm text-red-600">{{ errors.email }}</p>
            </div>
          </div>
          
          <div>
            <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
            <div class="mt-1">
              <input
                id="phone"
                v-model="form.phone"
                type="tel"
                class="py-3 px-4 block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md"
                :class="{ 'border-red-300': errors.phone }"
              />
              <p v-if="errors.phone" class="mt-1 text-sm text-red-600">{{ errors.phone }}</p>
            </div>
          </div>
          
          <div>
            <label for="subject" class="block text-sm font-medium text-gray-700">Subject</label>
            <div class="mt-1">
              <select
                id="subject"
                v-model="form.subject"
                required
                class="py-3 px-4 block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md"
                :class="{ 'border-red-300': errors.subject }"
              >
                <option value="">Select a subject</option>
                <option value="general">General Inquiry</option>
                <option value="technical">Technical Support</option>
                <option value="billing">Billing Question</option>
                <option value="feedback">Feedback</option>
                <option value="complaint">Complaint</option>
                <option value="other">Other</option>
              </select>
              <p v-if="errors.subject" class="mt-1 text-sm text-red-600">{{ errors.subject }}</p>
            </div>
          </div>
          
          <div>
            <label for="priority" class="block text-sm font-medium text-gray-700">Priority</label>
            <div class="mt-1">
              <select
                id="priority"
                v-model="form.priority"
                class="py-3 px-4 block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>
          </div>
          
          <div>
            <label for="message" class="block text-sm font-medium text-gray-700">Message</label>
            <div class="mt-1">
              <textarea
                id="message"
                v-model="form.message"
                rows="4"
                required
                class="py-3 px-4 block w-full shadow-sm focus:ring-blue-500 focus:border-blue-500 border-gray-300 rounded-md"
                :class="{ 'border-red-300': errors.message }"
                placeholder="Please describe your question or concern in detail..."
              ></textarea>
              <p v-if="errors.message" class="mt-1 text-sm text-red-600">{{ errors.message }}</p>
            </div>
          </div>

          <!-- File attachment -->
          <div>
            <label class="block text-sm font-medium text-gray-700">Attachments (optional)</label>
            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
              <div class="space-y-1 text-center">
                <PaperClipIcon class="mx-auto h-12 w-12 text-gray-400" />
                <div class="flex text-sm text-gray-600">
                  <label for="file-upload" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                    <span>Upload files</span>
                    <input
                      id="file-upload"
                      ref="fileInput"
                      type="file"
                      multiple
                      accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.txt"
                      @change="handleFileUpload"
                      class="sr-only"
                    />
                  </label>
                  <p class="pl-1">or drag and drop</p>
                </div>
                <p class="text-xs text-gray-500">PNG, JPG, PDF, DOC up to 10MB each</p>
              </div>
            </div>
            
            <!-- File list -->
            <div v-if="form.files.length > 0" class="mt-3">
              <ul class="divide-y divide-gray-200">
                <li v-for="(file, index) in form.files" :key="index" class="py-2 flex items-center justify-between">
                  <div class="flex items-center">
                    <PaperClipIcon class="h-4 w-4 text-gray-400 mr-2" />
                    <span class="text-sm text-gray-900">{{ file.name }}</span>
                    <span class="text-xs text-gray-500 ml-2">({{ formatFileSize(file.size) }})</span>
                  </div>
                  <button
                    type="button"
                    @click="removeFile(index)"
                    class="text-red-600 hover:text-red-500"
                  >
                    <XMarkIcon class="h-4 w-4" />
                  </button>
                </li>
              </ul>
            </div>
          </div>

          <!-- GDPR consent -->
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <input
                id="consent"
                v-model="form.consent"
                type="checkbox"
                required
                class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded"
              />
            </div>
            <div class="ml-3 text-sm">
              <label for="consent" class="text-gray-700">
                I agree to the processing of my personal data for the purpose of handling my inquiry.
                <a href="#" class="text-blue-600 hover:text-blue-500">Privacy Policy</a>
              </label>
            </div>
          </div>

          <div>
            <button
              type="submit"
              :disabled="isSubmitting"
              class="w-full inline-flex justify-center py-3 px-6 border border-transparent shadow-sm text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="isSubmitting" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending...
              </span>
              <span v-else>Send Message</span>
            </button>
          </div>
        </form>
      </div>

      <!-- Contact information -->
      <div class="mt-12 sm:mt-16 md:mt-0">
        <h2 class="text-2xl font-extrabold text-gray-900 sm:text-3xl">
          Get in touch
        </h2>
        <div class="mt-3">
          <p class="text-lg text-gray-500">
            We'd love to hear from you. Choose the method that works best for you.
          </p>
        </div>
        
        <div class="mt-9">
          <div class="flex">
            <div class="flex-shrink-0">
              <PhoneIcon class="h-6 w-6 text-gray-400" />
            </div>
            <div class="ml-3 text-base text-gray-500">
              <p>+****************</p>
              <p class="text-sm">Mon-Fri 9am to 6pm EST</p>
            </div>
          </div>
          <div class="mt-6 flex">
            <div class="flex-shrink-0">
              <EnvelopeIcon class="h-6 w-6 text-gray-400" />
            </div>
            <div class="ml-3 text-base text-gray-500">
              <p><EMAIL></p>
              <p class="text-sm">We'll respond within 24 hours</p>
            </div>
          </div>
          <div class="mt-6 flex">
            <div class="flex-shrink-0">
              <ChatBubbleLeftRightIcon class="h-6 w-6 text-gray-400" />
            </div>
            <div class="ml-3 text-base text-gray-500">
              <p>Live Chat</p>
              <p class="text-sm">Available 24/7</p>
              <router-link to="/chat" class="text-blue-600 hover:text-blue-500 font-medium">
                Start chatting →
              </router-link>
            </div>
          </div>
        </div>

        <!-- Business hours -->
        <div class="mt-8 p-6 bg-gray-50 rounded-lg">
          <h3 class="text-lg font-medium text-gray-900">Business Hours</h3>
          <div class="mt-4 space-y-2 text-sm text-gray-600">
            <div class="flex justify-between">
              <span>Monday - Friday</span>
              <span>9:00 AM - 6:00 PM EST</span>
            </div>
            <div class="flex justify-between">
              <span>Saturday</span>
              <span>10:00 AM - 4:00 PM EST</span>
            </div>
            <div class="flex justify-between">
              <span>Sunday</span>
              <span>Closed</span>
            </div>
          </div>
          <p class="mt-4 text-xs text-gray-500">
            Live chat support is available 24/7
          </p>
        </div>
      </div>
    </div>

    <!-- Success message -->
    <div v-if="showSuccess" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
        <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-sm sm:w-full sm:p-6">
          <div>
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
              <CheckIcon class="h-6 w-6 text-green-600" />
            </div>
            <div class="mt-3 text-center sm:mt-5">
              <h3 class="text-lg leading-6 font-medium text-gray-900">Message Sent!</h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  Thank you for contacting us. We'll get back to you within 24 hours.
                </p>
              </div>
            </div>
          </div>
          <div class="mt-5 sm:mt-6">
            <button
              @click="showSuccess = false"
              class="inline-flex justify-center w-full rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:text-sm"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import axios from 'axios'
import {
  PhoneIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  PaperClipIcon,
  XMarkIcon,
  CheckIcon
} from '@heroicons/vue/24/outline'

export default {
  name: 'Contact',
  components: {
    PhoneIcon,
    EnvelopeIcon,
    ChatBubbleLeftRightIcon,
    PaperClipIcon,
    XMarkIcon,
    CheckIcon
  },
  setup() {
    const isSubmitting = ref(false)
    const showSuccess = ref(false)
    const errors = reactive({})

    const form = reactive({
      name: '',
      email: '',
      phone: '',
      subject: '',
      priority: 'medium',
      message: '',
      files: [],
      consent: false
    })

    const validateForm = () => {
      Object.keys(errors).forEach(key => delete errors[key])

      if (!form.name.trim()) {
        errors.name = 'Name is required'
      }

      if (!form.email.trim()) {
        errors.email = 'Email is required'
      } else if (!/\S+@\S+\.\S+/.test(form.email)) {
        errors.email = 'Email is invalid'
      }

      if (!form.subject) {
        errors.subject = 'Subject is required'
      }

      if (!form.message.trim()) {
        errors.message = 'Message is required'
      }

      return Object.keys(errors).length === 0
    }

    const handleFileUpload = (event) => {
      const files = Array.from(event.target.files)
      const maxSize = 10 * 1024 * 1024 // 10MB
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain']

      files.forEach(file => {
        if (file.size > maxSize) {
          alert(`File ${file.name} is too large. Maximum size is 10MB.`)
          return
        }

        if (!allowedTypes.includes(file.type)) {
          alert(`File ${file.name} is not a supported format.`)
          return
        }

        form.files.push(file)
      })

      // Clear the input
      event.target.value = ''
    }

    const removeFile = (index) => {
      form.files.splice(index, 1)
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const submitForm = async () => {
      if (!validateForm()) {
        return
      }

      try {
        isSubmitting.value = true

        const formData = new FormData()
        Object.keys(form).forEach(key => {
          if (key === 'files') {
            form.files.forEach(file => {
              formData.append('files[]', file)
            })
          } else {
            formData.append(key, form[key])
          }
        })

        const response = await axios.post('/contact', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        if (response.data.success) {
          showSuccess.value = true
          // Reset form
          Object.keys(form).forEach(key => {
            if (key === 'files') {
              form[key] = []
            } else if (key === 'priority') {
              form[key] = 'medium'
            } else if (key === 'consent') {
              form[key] = false
            } else {
              form[key] = ''
            }
          })
        } else {
          if (response.data.errors) {
            Object.assign(errors, response.data.errors)
          } else {
            alert('Failed to send message. Please try again.')
          }
        }
      } catch (error) {
        console.error('Failed to submit contact form:', error)
        if (error.response?.data?.errors) {
          Object.assign(errors, error.response.data.errors)
        } else {
          alert('Failed to send message. Please try again.')
        }
      } finally {
        isSubmitting.value = false
      }
    }

    return {
      form,
      errors,
      isSubmitting,
      showSuccess,
      validateForm,
      handleFileUpload,
      removeFile,
      formatFileSize,
      submitForm
    }
  }
}
</script>

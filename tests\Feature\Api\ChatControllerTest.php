<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use App\Models\User;
use App\Models\Site;
use App\Models\Visitor;
use App\Models\ChatSession;
use App\Models\ChatMessage;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\Sanctum;

class ChatControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $site;
    protected $visitor;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->site = Site::factory()->create();
        $this->user = User::factory()->create([
            'site_id' => $this->site->id,
            'role' => 'agent'
        ]);
        $this->visitor = Visitor::factory()->create([
            'site_id' => $this->site->id
        ]);
    }

    public function test_visitor_can_start_chat_session()
    {
        $response = $this->postJson('/api/v1/chat/start-session', [
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'visitor_name' => 'Test Visitor',
            'visitor_email' => '<EMAIL>',
            'initial_message' => 'Hello, I need help'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'session' => [
                            'id',
                            'session_id',
                            'status',
                            'queue_position',
                            'estimated_wait_time'
                        ]
                    ]
                ]);

        $this->assertDatabaseHas('chat_sessions', [
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'status' => 'waiting'
        ]);
    }

    public function test_start_session_requires_visitor_data()
    {
        $response = $this->postJson('/api/v1/chat/start-session', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['visitor_id', 'site_id', 'visitor_name']);
    }

    public function test_can_get_session_messages()
    {
        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id
        ]);

        ChatMessage::factory()->count(5)->create([
            'session_id' => $session->id
        ]);

        $response = $this->getJson("/api/v1/chat/sessions/{$session->id}/messages");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'messages' => [
                            '*' => [
                                'id',
                                'message',
                                'sender_type',
                                'sent_at'
                            ]
                        ],
                        'pagination'
                    ]
                ]);

        $this->assertCount(5, $response->json('data.messages'));
    }

    public function test_can_send_message_to_session()
    {
        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'status' => 'active'
        ]);

        $response = $this->postJson("/api/v1/chat/sessions/{$session->id}/messages", [
            'message' => 'Hello from visitor',
            'sender_type' => 'visitor',
            'message_type' => 'text'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'message' => [
                            'id',
                            'message',
                            'sender_type',
                            'sent_at'
                        ]
                    ]
                ]);

        $this->assertDatabaseHas('chat_messages', [
            'session_id' => $session->id,
            'message' => 'Hello from visitor',
            'sender_type' => 'visitor'
        ]);
    }

    public function test_agent_can_send_message()
    {
        Sanctum::actingAs($this->user);

        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'agent_id' => $this->user->id,
            'status' => 'active'
        ]);

        $response = $this->postJson("/api/v1/chat/sessions/{$session->id}/messages", [
            'message' => 'Hello from agent',
            'sender_type' => 'agent',
            'message_type' => 'text'
        ]);

        $response->assertStatus(200);

        $this->assertDatabaseHas('chat_messages', [
            'session_id' => $session->id,
            'message' => 'Hello from agent',
            'sender_type' => 'agent',
            'sender_id' => $this->user->id
        ]);
    }

    public function test_can_upload_file_in_chat()
    {
        Storage::fake('public');

        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'status' => 'active'
        ]);

        $file = UploadedFile::fake()->image('test.jpg', 100, 100);

        $response = $this->postJson("/api/v1/chat/sessions/{$session->id}/messages", [
            'message' => 'Here is an image',
            'sender_type' => 'visitor',
            'message_type' => 'file',
            'file' => $file
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'message' => [
                            'id',
                            'message',
                            'file_url',
                            'file_name',
                            'file_size'
                        ]
                    ]
                ]);

        Storage::disk('public')->assertExists('chat_files/' . $file->hashName());
    }

    public function test_can_set_typing_indicator()
    {
        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'status' => 'active'
        ]);

        $response = $this->postJson("/api/v1/chat/sessions/{$session->id}/typing", [
            'is_typing' => true,
            'sender_type' => 'visitor'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Typing indicator updated'
                ]);
    }

    public function test_agent_can_end_session()
    {
        Sanctum::actingAs($this->user);

        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'agent_id' => $this->user->id,
            'status' => 'active'
        ]);

        $response = $this->postJson("/api/v1/chat/sessions/{$session->id}/end", [
            'reason' => 'Issue resolved'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Session ended successfully'
                ]);

        $session->refresh();
        $this->assertEquals('ended', $session->status);
        $this->assertNotNull($session->ended_at);
    }

    public function test_visitor_can_rate_session()
    {
        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'status' => 'ended'
        ]);

        $response = $this->postJson("/api/v1/chat/sessions/{$session->id}/rate", [
            'rating' => 5,
            'feedback' => 'Great service!'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Rating submitted successfully'
                ]);

        $session->refresh();
        $this->assertEquals(5, $session->rating);
        $this->assertEquals('Great service!', $session->feedback);
    }

    public function test_rating_validation()
    {
        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'status' => 'ended'
        ]);

        $response = $this->postJson("/api/v1/chat/sessions/{$session->id}/rate", [
            'rating' => 6, // Invalid rating (should be 1-5)
            'feedback' => 'Great service!'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['rating']);
    }

    public function test_can_get_queue_position()
    {
        // Create waiting sessions
        ChatSession::factory()->count(3)->create([
            'site_id' => $this->site->id,
            'status' => 'waiting',
            'started_at' => now()->subMinutes(10)
        ]);

        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'status' => 'waiting',
            'started_at' => now()
        ]);

        $response = $this->getJson("/api/v1/chat/sessions/{$session->id}/queue-position");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'position',
                        'estimated_wait_time'
                    ]
                ]);

        $this->assertEquals(4, $response->json('data.position'));
    }

    public function test_cannot_send_message_to_ended_session()
    {
        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'status' => 'ended'
        ]);

        $response = $this->postJson("/api/v1/chat/sessions/{$session->id}/messages", [
            'message' => 'This should fail',
            'sender_type' => 'visitor',
            'message_type' => 'text'
        ]);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'Cannot send message to ended session'
                ]);
    }

    public function test_cannot_rate_active_session()
    {
        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'status' => 'active'
        ]);

        $response = $this->postJson("/api/v1/chat/sessions/{$session->id}/rate", [
            'rating' => 5,
            'feedback' => 'Great service!'
        ]);

        $response->assertStatus(400)
                ->assertJson([
                    'success' => false,
                    'message' => 'Can only rate ended sessions'
                ]);
    }

    public function test_file_upload_validation()
    {
        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'status' => 'active'
        ]);

        // Test file size limit
        $largeFile = UploadedFile::fake()->create('large.pdf', 15000); // 15MB

        $response = $this->postJson("/api/v1/chat/sessions/{$session->id}/messages", [
            'message' => 'Large file',
            'sender_type' => 'visitor',
            'message_type' => 'file',
            'file' => $largeFile
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['file']);
    }

    public function test_message_pagination()
    {
        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id
        ]);

        ChatMessage::factory()->count(25)->create([
            'session_id' => $session->id
        ]);

        $response = $this->getJson("/api/v1/chat/sessions/{$session->id}/messages?per_page=10");

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertCount(10, $data['messages']);
        $this->assertEquals(3, $data['pagination']['last_page']);
        $this->assertEquals(25, $data['pagination']['total']);
    }
}

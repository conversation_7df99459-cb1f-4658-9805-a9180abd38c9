<?php

namespace Tests\Feature\Api;

use Tests\TestCase;
use App\Models\User;
use App\Models\Site;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Hash;
use Lara<PERSON>\Sanctum\Sanctum;

class AuthControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $site;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->site = Site::factory()->create();
        $this->user = User::factory()->create([
            'site_id' => $this->site->id,
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'is_active' => true
        ]);
    }

    public function test_user_can_login_with_valid_credentials()
    {
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'user' => [
                            'id',
                            'name',
                            'email',
                            'role'
                        ],
                        'token',
                        'expires_at'
                    ]
                ]);

        $this->assertTrue($response->json('success'));
    }

    public function test_user_cannot_login_with_invalid_credentials()
    {
        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword'
        ]);

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                    'message' => 'Invalid credentials'
                ]);
    }

    public function test_user_cannot_login_when_inactive()
    {
        $this->user->update(['is_active' => false]);

        $response = $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        $response->assertStatus(401)
                ->assertJson([
                    'success' => false,
                    'message' => 'Account is inactive'
                ]);
    }

    public function test_login_requires_email_and_password()
    {
        $response = $this->postJson('/api/v1/auth/login', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['email', 'password']);
    }

    public function test_authenticated_user_can_get_profile()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/auth/profile');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'name',
                        'email',
                        'role',
                        'avatar',
                        'status'
                    ]
                ]);
    }

    public function test_unauthenticated_user_cannot_get_profile()
    {
        $response = $this->getJson('/api/v1/auth/profile');

        $response->assertStatus(401);
    }

    public function test_user_can_update_profile()
    {
        Sanctum::actingAs($this->user);

        $response = $this->putJson('/api/v1/auth/profile', [
            'name' => 'Updated Name',
            'phone' => '+1234567890'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Profile updated successfully'
                ]);

        $this->user->refresh();
        $this->assertEquals('Updated Name', $this->user->name);
        $this->assertEquals('+1234567890', $this->user->phone);
    }

    public function test_user_can_update_password()
    {
        Sanctum::actingAs($this->user);

        $response = $this->putJson('/api/v1/auth/password', [
            'current_password' => 'password123',
            'new_password' => 'newpassword123',
            'new_password_confirmation' => 'newpassword123'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Password updated successfully'
                ]);

        $this->user->refresh();
        $this->assertTrue(Hash::check('newpassword123', $this->user->password));
    }

    public function test_user_cannot_update_password_with_wrong_current_password()
    {
        Sanctum::actingAs($this->user);

        $response = $this->putJson('/api/v1/auth/password', [
            'current_password' => 'wrongpassword',
            'new_password' => 'newpassword123',
            'new_password_confirmation' => 'newpassword123'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['current_password']);
    }

    public function test_password_update_requires_confirmation()
    {
        Sanctum::actingAs($this->user);

        $response = $this->putJson('/api/v1/auth/password', [
            'current_password' => 'password123',
            'new_password' => 'newpassword123',
            'new_password_confirmation' => 'differentpassword'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['new_password']);
    }

    public function test_user_can_update_status()
    {
        Sanctum::actingAs($this->user);

        $response = $this->putJson('/api/v1/auth/status', [
            'status' => 'away'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Status updated successfully'
                ]);

        $this->user->refresh();
        $this->assertEquals('away', $this->user->status);
    }

    public function test_status_update_validates_status_value()
    {
        Sanctum::actingAs($this->user);

        $response = $this->putJson('/api/v1/auth/status', [
            'status' => 'invalid_status'
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['status']);
    }

    public function test_user_can_logout()
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/v1/auth/logout');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Logged out successfully'
                ]);

        // Verify token is revoked
        $response = $this->getJson('/api/v1/auth/profile');
        $response->assertStatus(401);
    }

    public function test_user_can_refresh_token()
    {
        Sanctum::actingAs($this->user);

        $response = $this->postJson('/api/v1/auth/refresh');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'token',
                        'expires_at'
                    ]
                ]);
    }

    public function test_can_get_available_agents()
    {
        // Create additional agents
        User::factory()->count(3)->create([
            'site_id' => $this->site->id,
            'role' => 'agent',
            'status' => 'online',
            'is_active' => true
        ]);

        User::factory()->create([
            'site_id' => $this->site->id,
            'role' => 'agent',
            'status' => 'offline',
            'is_active' => true
        ]);

        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/auth/available-agents');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'status',
                            'current_sessions'
                        ]
                    ]
                ]);

        // Should return 3 online agents (excluding the offline one)
        $this->assertCount(3, $response->json('data'));
    }

    public function test_login_tracks_last_login_time()
    {
        $originalLastLogin = $this->user->last_login_at;

        $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        $this->user->refresh();
        $this->assertNotEquals($originalLastLogin, $this->user->last_login_at);
        $this->assertNotNull($this->user->last_login_at);
    }

    public function test_failed_login_increments_failed_attempts()
    {
        $originalFailedAttempts = $this->user->failed_login_attempts;

        $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword'
        ]);

        $this->user->refresh();
        $this->assertEquals($originalFailedAttempts + 1, $this->user->failed_login_attempts);
    }

    public function test_successful_login_resets_failed_attempts()
    {
        $this->user->update(['failed_login_attempts' => 3]);

        $this->postJson('/api/v1/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ]);

        $this->user->refresh();
        $this->assertEquals(0, $this->user->failed_login_attempts);
    }
}

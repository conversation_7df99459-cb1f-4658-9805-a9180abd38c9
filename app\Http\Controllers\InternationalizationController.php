<?php

namespace App\Http\Controllers;

use App\Services\InternationalizationService;
use App\Models\Language;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class InternationalizationController extends Controller
{
    protected $i18nService;

    public function __construct(InternationalizationService $i18nService)
    {
        $this->i18nService = $i18nService;
    }

    /**
     * Get all languages for a site
     */
    public function getLanguages(Request $request, $siteId): JsonResponse
    {
        try {
            $activeOnly = $request->boolean('active_only', false);
            $languages = $this->i18nService->getLanguagesForSite($siteId, $activeOnly);

            return response()->json([
                'success' => true,
                'data' => $languages,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get languages: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get default language for a site
     */
    public function getDefaultLanguage($siteId): JsonResponse
    {
        try {
            $language = $this->i18nService->getDefaultLanguage($siteId);

            if (!$language) {
                return response()->json([
                    'success' => false,
                    'message' => 'No default language found',
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $language,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get default language: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a new language
     */
    public function createLanguage(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'code' => 'required|string|max:10',
            'name' => 'required|string|max:255',
            'native_name' => 'required|string|max:255',
            'flag_icon' => 'nullable|string|max:10',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'is_rtl' => 'boolean',
            'date_format' => 'nullable|string|max:50',
            'time_format' => 'nullable|string|max:50',
            'currency_code' => 'nullable|string|max:3',
            'currency_symbol' => 'nullable|string|max:10',
            'number_format' => 'nullable|string|max:50',
            'timezone' => 'nullable|string|max:100',
        ]);

        try {
            $language = $this->i18nService->createLanguage($siteId, $request->all());

            return response()->json([
                'success' => true,
                'data' => $language,
                'message' => 'Language created successfully',
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create language: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update language
     */
    public function updateLanguage(Request $request, $languageId): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'native_name' => 'sometimes|required|string|max:255',
            'flag_icon' => 'nullable|string|max:10',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
            'is_rtl' => 'boolean',
            'date_format' => 'nullable|string|max:50',
            'time_format' => 'nullable|string|max:50',
            'currency_code' => 'nullable|string|max:3',
            'currency_symbol' => 'nullable|string|max:10',
            'number_format' => 'nullable|string|max:50',
            'timezone' => 'nullable|string|max:100',
        ]);

        try {
            $language = $this->i18nService->updateLanguage($languageId, $request->all());

            return response()->json([
                'success' => true,
                'data' => $language,
                'message' => 'Language updated successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update language: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete language
     */
    public function deleteLanguage($languageId): JsonResponse
    {
        try {
            $this->i18nService->deleteLanguage($languageId);

            return response()->json([
                'success' => true,
                'message' => 'Language deleted successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete language: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available languages list
     */
    public function getAvailableLanguages(): JsonResponse
    {
        try {
            $languages = Language::getAvailableLanguages();

            return response()->json([
                'success' => true,
                'data' => $languages,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get available languages: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get translation
     */
    public function getTranslation(Request $request, $siteId, $languageCode, $key): JsonResponse
    {
        try {
            $default = $request->get('default');
            $replacements = $request->get('replacements', []);
            
            $translation = $this->i18nService->getTranslation(
                $siteId, 
                $languageCode, 
                $key, 
                $default, 
                $replacements
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'key' => $key,
                    'value' => $translation,
                    'language' => $languageCode,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get translation: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get all translations for a language
     */
    public function getAllTranslations(Request $request, $siteId, $languageCode): JsonResponse
    {
        try {
            $namespace = $request->get('namespace');
            $group = $request->get('group');
            
            $translations = $this->i18nService->getAllTranslations(
                $siteId, 
                $languageCode, 
                $namespace, 
                $group
            );

            return response()->json([
                'success' => true,
                'data' => [
                    'language' => $languageCode,
                    'namespace' => $namespace,
                    'group' => $group,
                    'translations' => $translations,
                    'count' => count($translations),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get translations: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Set translation
     */
    public function setTranslation(Request $request, $siteId, $languageCode): JsonResponse
    {
        $request->validate([
            'key' => 'required|string',
            'value' => 'required|string',
            'context' => 'nullable|string',
            'is_system' => 'boolean',
        ]);

        try {
            $translation = $this->i18nService->setTranslation(
                $siteId,
                $languageCode,
                $request->key,
                $request->value,
                $request->context,
                $request->boolean('is_system', false)
            );

            return response()->json([
                'success' => true,
                'data' => $translation,
                'message' => 'Translation saved successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to set translation: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Bulk set translations
     */
    public function bulkSetTranslations(Request $request, $siteId, $languageCode): JsonResponse
    {
        $request->validate([
            'translations' => 'required|array',
            'is_system' => 'boolean',
        ]);

        try {
            $result = $this->i18nService->bulkSetTranslations(
                $siteId,
                $languageCode,
                $request->translations,
                $request->boolean('is_system', false)
            );

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Translations saved successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save translations: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export translations
     */
    public function exportTranslations(Request $request, $siteId, $languageCode): JsonResponse
    {
        $request->validate([
            'format' => 'sometimes|required|string|in:json,php',
        ]);

        try {
            $format = $request->get('format', 'json');
            $exportData = $this->i18nService->exportTranslations($siteId, $languageCode, $format);

            $filename = "translations_{$languageCode}_{$siteId}." . ($format === 'php' ? 'php' : 'json');

            return response($exportData)
                ->header('Content-Type', $format === 'php' ? 'text/plain' : 'application/json')
                ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export translations: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Import translations
     */
    public function importTranslations(Request $request, $siteId, $languageCode): JsonResponse
    {
        $request->validate([
            'data' => 'required',
            'overwrite' => 'boolean',
        ]);

        try {
            $result = $this->i18nService->importTranslations(
                $siteId,
                $languageCode,
                $request->data,
                $request->boolean('overwrite', false)
            );

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Translations imported successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to import translations: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get translation statistics
     */
    public function getTranslationStats(Request $request, $siteId, $languageCode = null): JsonResponse
    {
        try {
            $stats = $this->i18nService->getTranslationStats($siteId, $languageCode);

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get translation stats: ' . $e->getMessage(),
            ], 500);
        }
    }
}

<?php

namespace App\Services;

use App\Models\Site;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ConfigurationService
{
    const CACHE_TTL = 3600; // 1 hour
    const CACHE_PREFIX = 'config:';

    /**
     * Get chat widget configuration
     */
    public function getChatWidgetConfig($siteId)
    {
        $cacheKey = self::CACHE_PREFIX . "widget:{$siteId}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function() use ($siteId) {
            $site = Site::find($siteId);
            
            if (!$site) {
                throw new \Exception('Site not found');
            }

            $defaultConfig = $this->getDefaultWidgetConfig();
            $siteConfig = $site->settings['chat_widget'] ?? [];

            return array_merge($defaultConfig, $siteConfig);
        });
    }

    /**
     * Update chat widget configuration
     */
    public function updateChatWidgetConfig($siteId, array $config)
    {
        $site = Site::find($siteId);
        
        if (!$site) {
            throw new \Exception('Site not found');
        }

        // Validate configuration
        $validatedConfig = $this->validateWidgetConfig($config);

        // Update site settings
        $settings = $site->settings ?? [];
        $settings['chat_widget'] = $validatedConfig;
        
        $site->update(['settings' => $settings]);

        // Clear cache
        $cacheKey = self::CACHE_PREFIX . "widget:{$siteId}";
        Cache::forget($cacheKey);

        Log::info('Chat widget configuration updated', [
            'site_id' => $siteId,
            'config' => $validatedConfig
        ]);

        return $validatedConfig;
    }

    /**
     * Get default widget configuration
     */
    private function getDefaultWidgetConfig()
    {
        return [
            'enabled' => true,
            'position' => 'bottom-right',
            'theme_color' => '#007bff',
            'welcome_message' => 'Hello! How can we help you today?',
            'offline_message' => 'We are currently offline. Please leave a message and we will get back to you.',
            'show_agent_avatar' => true,
            'show_typing_indicator' => true,
            'enable_file_upload' => true,
            'enable_emoji' => true,
            'enable_sound' => true,
            'auto_open' => false,
            'auto_open_delay' => 5,
            'max_file_size' => 10, // MB
            'allowed_file_types' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt'],
            'pre_chat_form' => [
                'enabled' => true,
                'fields' => [
                    [
                        'name' => 'name',
                        'label' => 'Your Name',
                        'type' => 'text',
                        'required' => true,
                        'placeholder' => 'Enter your name'
                    ],
                    [
                        'name' => 'email',
                        'label' => 'Email Address',
                        'type' => 'email',
                        'required' => true,
                        'placeholder' => 'Enter your email'
                    ],
                    [
                        'name' => 'subject',
                        'label' => 'Subject',
                        'type' => 'text',
                        'required' => false,
                        'placeholder' => 'What can we help you with?'
                    ]
                ]
            ],
            'post_chat_survey' => [
                'enabled' => true,
                'fields' => [
                    [
                        'name' => 'satisfaction',
                        'label' => 'How satisfied were you with our service?',
                        'type' => 'rating',
                        'required' => true,
                        'max_rating' => 5
                    ],
                    [
                        'name' => 'feedback',
                        'label' => 'Additional feedback',
                        'type' => 'textarea',
                        'required' => false,
                        'placeholder' => 'Please share your feedback...'
                    ]
                ]
            ],
            'appearance' => [
                'widget_size' => 'medium',
                'border_radius' => 8,
                'shadow' => true,
                'animation' => 'slide',
                'custom_css' => ''
            ],
            'behavior' => [
                'minimize_on_close' => true,
                'remember_visitor' => true,
                'show_unread_count' => true,
                'typing_timeout' => 3000,
                'message_sound' => true
            ],
            'security' => [
                'enable_captcha' => false,
                'rate_limit' => 60, // messages per hour
                'block_spam' => true,
                'allowed_domains' => []
            ]
        ];
    }

    /**
     * Validate widget configuration
     */
    private function validateWidgetConfig(array $config)
    {
        $validated = [];

        // Basic settings
        $validated['enabled'] = (bool) ($config['enabled'] ?? true);
        $validated['position'] = in_array($config['position'] ?? 'bottom-right', 
            ['bottom-right', 'bottom-left', 'top-right', 'top-left']) 
            ? $config['position'] : 'bottom-right';
        
        $validated['theme_color'] = $this->validateColor($config['theme_color'] ?? '#007bff');
        $validated['welcome_message'] = substr($config['welcome_message'] ?? '', 0, 500);
        $validated['offline_message'] = substr($config['offline_message'] ?? '', 0, 500);
        
        // Boolean settings
        $booleanFields = [
            'show_agent_avatar', 'show_typing_indicator', 'enable_file_upload',
            'enable_emoji', 'enable_sound', 'auto_open'
        ];
        
        foreach ($booleanFields as $field) {
            $validated[$field] = (bool) ($config[$field] ?? true);
        }

        // Numeric settings
        $validated['auto_open_delay'] = max(0, min(60, (int) ($config['auto_open_delay'] ?? 5)));
        $validated['max_file_size'] = max(1, min(50, (int) ($config['max_file_size'] ?? 10)));

        // File types
        $validated['allowed_file_types'] = $this->validateFileTypes($config['allowed_file_types'] ?? []);

        // Forms
        $validated['pre_chat_form'] = $this->validateFormConfig($config['pre_chat_form'] ?? []);
        $validated['post_chat_survey'] = $this->validateFormConfig($config['post_chat_survey'] ?? []);

        // Appearance
        $validated['appearance'] = $this->validateAppearanceConfig($config['appearance'] ?? []);

        // Behavior
        $validated['behavior'] = $this->validateBehaviorConfig($config['behavior'] ?? []);

        // Security
        $validated['security'] = $this->validateSecurityConfig($config['security'] ?? []);

        return $validated;
    }

    /**
     * Validate color value
     */
    private function validateColor($color)
    {
        if (preg_match('/^#[a-f0-9]{6}$/i', $color)) {
            return $color;
        }
        return '#007bff';
    }

    /**
     * Validate file types
     */
    private function validateFileTypes($types)
    {
        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'txt', 'zip', 'rar'];
        
        if (!is_array($types)) {
            return ['jpg', 'jpeg', 'png', 'gif', 'pdf'];
        }

        return array_intersect($types, $allowedTypes);
    }

    /**
     * Validate form configuration
     */
    private function validateFormConfig($formConfig)
    {
        $validated = [
            'enabled' => (bool) ($formConfig['enabled'] ?? true),
            'fields' => []
        ];

        if (isset($formConfig['fields']) && is_array($formConfig['fields'])) {
            foreach ($formConfig['fields'] as $field) {
                if (isset($field['name']) && isset($field['type'])) {
                    $validatedField = [
                        'name' => $field['name'],
                        'label' => $field['label'] ?? '',
                        'type' => $field['type'],
                        'required' => (bool) ($field['required'] ?? false),
                        'placeholder' => $field['placeholder'] ?? ''
                    ];

                    if ($field['type'] === 'rating') {
                        $validatedField['max_rating'] = max(1, min(10, (int) ($field['max_rating'] ?? 5)));
                    }

                    $validated['fields'][] = $validatedField;
                }
            }
        }

        return $validated;
    }

    /**
     * Validate appearance configuration
     */
    private function validateAppearanceConfig($appearance)
    {
        return [
            'widget_size' => in_array($appearance['widget_size'] ?? 'medium', ['small', 'medium', 'large']) 
                ? $appearance['widget_size'] : 'medium',
            'border_radius' => max(0, min(20, (int) ($appearance['border_radius'] ?? 8))),
            'shadow' => (bool) ($appearance['shadow'] ?? true),
            'animation' => in_array($appearance['animation'] ?? 'slide', ['slide', 'fade', 'bounce', 'none']) 
                ? $appearance['animation'] : 'slide',
            'custom_css' => substr($appearance['custom_css'] ?? '', 0, 5000)
        ];
    }

    /**
     * Validate behavior configuration
     */
    private function validateBehaviorConfig($behavior)
    {
        return [
            'minimize_on_close' => (bool) ($behavior['minimize_on_close'] ?? true),
            'remember_visitor' => (bool) ($behavior['remember_visitor'] ?? true),
            'show_unread_count' => (bool) ($behavior['show_unread_count'] ?? true),
            'typing_timeout' => max(1000, min(10000, (int) ($behavior['typing_timeout'] ?? 3000))),
            'message_sound' => (bool) ($behavior['message_sound'] ?? true)
        ];
    }

    /**
     * Validate security configuration
     */
    private function validateSecurityConfig($security)
    {
        return [
            'enable_captcha' => (bool) ($security['enable_captcha'] ?? false),
            'rate_limit' => max(10, min(1000, (int) ($security['rate_limit'] ?? 60))),
            'block_spam' => (bool) ($security['block_spam'] ?? true),
            'allowed_domains' => is_array($security['allowed_domains'] ?? []) 
                ? $security['allowed_domains'] : []
        ];
    }

    /**
     * Get site configuration
     */
    public function getSiteConfig($siteId)
    {
        $cacheKey = self::CACHE_PREFIX . "site:{$siteId}";
        
        return Cache::remember($cacheKey, self::CACHE_TTL, function() use ($siteId) {
            $site = Site::with(['workingHours', 'theme', 'languages'])->find($siteId);
            
            if (!$site) {
                throw new \Exception('Site not found');
            }

            return $site;
        });
    }

    /**
     * Update site configuration
     */
    public function updateSiteConfig($siteId, array $config)
    {
        $site = Site::find($siteId);
        
        if (!$site) {
            throw new \Exception('Site not found');
        }

        $site->update($config);

        // Clear cache
        $cacheKey = self::CACHE_PREFIX . "site:{$siteId}";
        Cache::forget($cacheKey);

        Log::info('Site configuration updated', [
            'site_id' => $siteId,
            'config' => $config
        ]);

        return $site->fresh();
    }

    /**
     * Clear all configuration cache
     */
    public function clearCache($siteId = null)
    {
        if ($siteId) {
            Cache::forget(self::CACHE_PREFIX . "site:{$siteId}");
            Cache::forget(self::CACHE_PREFIX . "widget:{$siteId}");
        } else {
            // Clear all config cache
            $tags = [self::CACHE_PREFIX . '*'];
            foreach ($tags as $tag) {
                Cache::flush();
            }
        }

        Log::info('Configuration cache cleared', ['site_id' => $siteId]);
    }

    /**
     * Get widget embed code
     */
    public function getWidgetEmbedCode($siteId)
    {
        $site = Site::find($siteId);
        
        if (!$site) {
            throw new \Exception('Site not found');
        }

        $baseUrl = config('app.url');
        $widgetUrl = "{$baseUrl}/widget/{$site->site_id}";

        return [
            'script_tag' => "<script src=\"{$widgetUrl}/widget.js\" data-site-id=\"{$site->site_id}\"></script>",
            'iframe_tag' => "<iframe src=\"{$widgetUrl}\" width=\"350\" height=\"500\" frameborder=\"0\"></iframe>",
            'widget_url' => $widgetUrl,
            'api_endpoint' => "{$baseUrl}/api/chat/{$site->site_id}",
        ];
    }
}

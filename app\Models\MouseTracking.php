<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class MouseTracking extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tracking_id',
        'site_id',
        'visitor_id',
        'visitor_tracking_id',
        'session_id',
        'page_url',
        'mouse_data',
        'heatmap_data',
        'scroll_data',
        'viewport_size',
        'recorded_at',
        'duration',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'mouse_data' => 'array',
        'heatmap_data' => 'array',
        'scroll_data' => 'array',
        'viewport_size' => 'array',
        'recorded_at' => 'datetime',
        'duration' => 'integer',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($tracking) {
            if (!$tracking->tracking_id) {
                $tracking->tracking_id = Str::uuid();
            }
            if (!$tracking->recorded_at) {
                $tracking->recorded_at = now();
            }
        });
    }

    /**
     * Get the site that owns the mouse tracking
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Get the visitor that owns the mouse tracking
     */
    public function visitor()
    {
        return $this->belongsTo(Visitor::class);
    }

    /**
     * Get the visitor tracking record
     */
    public function visitorTracking()
    {
        return $this->belongsTo(VisitorTracking::class);
    }

    /**
     * Add mouse movement data
     */
    public function addMouseMovement($x, $y, $timestamp)
    {
        $mouseData = $this->mouse_data ?? [];
        $mouseData[] = [
            'x' => $x,
            'y' => $y,
            'timestamp' => $timestamp,
            'type' => 'move'
        ];
        
        $this->update(['mouse_data' => $mouseData]);
        return $this;
    }

    /**
     * Add click data
     */
    public function addClick($x, $y, $element, $timestamp)
    {
        $mouseData = $this->mouse_data ?? [];
        $mouseData[] = [
            'x' => $x,
            'y' => $y,
            'element' => $element,
            'timestamp' => $timestamp,
            'type' => 'click'
        ];
        
        $this->update(['mouse_data' => $mouseData]);
        return $this;
    }

    /**
     * Add scroll data
     */
    public function addScroll($scrollTop, $scrollLeft, $timestamp)
    {
        $scrollData = $this->scroll_data ?? [];
        $scrollData[] = [
            'scroll_top' => $scrollTop,
            'scroll_left' => $scrollLeft,
            'timestamp' => $timestamp
        ];
        
        $this->update(['scroll_data' => $scrollData]);
        return $this;
    }

    /**
     * Generate heatmap data from mouse movements
     */
    public function generateHeatmapData($gridSize = 20)
    {
        if (!$this->mouse_data) {
            return [];
        }

        $viewport = $this->viewport_size ?? ['width' => 1920, 'height' => 1080];
        $gridWidth = ceil($viewport['width'] / $gridSize);
        $gridHeight = ceil($viewport['height'] / $gridSize);
        
        $heatmap = [];
        
        // Initialize grid
        for ($y = 0; $y < $gridHeight; $y++) {
            for ($x = 0; $x < $gridWidth; $x++) {
                $heatmap[$y][$x] = 0;
            }
        }
        
        // Process mouse movements
        foreach ($this->mouse_data as $point) {
            if ($point['type'] === 'move' || $point['type'] === 'click') {
                $gridX = floor($point['x'] / $gridSize);
                $gridY = floor($point['y'] / $gridSize);
                
                if ($gridX >= 0 && $gridX < $gridWidth && $gridY >= 0 && $gridY < $gridHeight) {
                    $weight = $point['type'] === 'click' ? 5 : 1; // Clicks have more weight
                    $heatmap[$gridY][$gridX] += $weight;
                }
            }
        }
        
        $this->update(['heatmap_data' => $heatmap]);
        return $heatmap;
    }

    /**
     * Get click density map
     */
    public function getClickDensityMap()
    {
        if (!$this->mouse_data) {
            return [];
        }

        $clicks = array_filter($this->mouse_data, function ($point) {
            return $point['type'] === 'click';
        });

        $density = [];
        foreach ($clicks as $click) {
            $key = floor($click['x'] / 50) . ',' . floor($click['y'] / 50); // 50px grid
            $density[$key] = ($density[$key] ?? 0) + 1;
        }

        return $density;
    }

    /**
     * Get scroll behavior analysis
     */
    public function getScrollBehavior()
    {
        if (!$this->scroll_data) {
            return [
                'max_scroll' => 0,
                'scroll_events' => 0,
                'scroll_speed' => 0,
                'scroll_pattern' => 'none'
            ];
        }

        $scrollEvents = count($this->scroll_data);
        $maxScroll = max(array_column($this->scroll_data, 'scroll_top'));
        
        // Calculate scroll speed
        $totalScrollDistance = 0;
        $totalTime = 0;
        
        for ($i = 1; $i < count($this->scroll_data); $i++) {
            $prev = $this->scroll_data[$i - 1];
            $curr = $this->scroll_data[$i];
            
            $distance = abs($curr['scroll_top'] - $prev['scroll_top']);
            $time = $curr['timestamp'] - $prev['timestamp'];
            
            $totalScrollDistance += $distance;
            $totalTime += $time;
        }
        
        $scrollSpeed = $totalTime > 0 ? $totalScrollDistance / $totalTime : 0;
        
        // Determine scroll pattern
        $pattern = 'linear';
        if ($scrollSpeed > 1000) {
            $pattern = 'fast';
        } elseif ($scrollSpeed < 100) {
            $pattern = 'slow';
        }
        
        return [
            'max_scroll' => $maxScroll,
            'scroll_events' => $scrollEvents,
            'scroll_speed' => round($scrollSpeed, 2),
            'scroll_pattern' => $pattern,
            'total_distance' => $totalScrollDistance,
            'total_time' => $totalTime
        ];
    }

    /**
     * Get mouse movement statistics
     */
    public function getMovementStatistics()
    {
        if (!$this->mouse_data) {
            return [
                'total_movements' => 0,
                'total_clicks' => 0,
                'movement_distance' => 0,
                'average_speed' => 0,
                'idle_time' => 0
            ];
        }

        $movements = array_filter($this->mouse_data, function ($point) {
            return $point['type'] === 'move';
        });
        
        $clicks = array_filter($this->mouse_data, function ($point) {
            return $point['type'] === 'click';
        });

        $totalDistance = 0;
        $totalTime = 0;
        $idleTime = 0;
        
        for ($i = 1; $i < count($movements); $i++) {
            $prev = $movements[$i - 1];
            $curr = $movements[$i];
            
            $distance = sqrt(
                pow($curr['x'] - $prev['x'], 2) + 
                pow($curr['y'] - $prev['y'], 2)
            );
            
            $time = $curr['timestamp'] - $prev['timestamp'];
            
            $totalDistance += $distance;
            $totalTime += $time;
            
            // Consider idle if no movement for more than 2 seconds
            if ($time > 2000) {
                $idleTime += $time;
            }
        }
        
        $averageSpeed = $totalTime > 0 ? $totalDistance / $totalTime : 0;

        return [
            'total_movements' => count($movements),
            'total_clicks' => count($clicks),
            'movement_distance' => round($totalDistance, 2),
            'average_speed' => round($averageSpeed, 2),
            'idle_time' => $idleTime,
            'total_time' => $totalTime
        ];
    }

    /**
     * Scope for page URL
     */
    public function scopeForPage($query, $pageUrl)
    {
        return $query->where('page_url', $pageUrl);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('recorded_at', [$startDate, $endDate]);
    }

    /**
     * Scope for recent tracking
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('recorded_at', '>=', now()->subHours($hours));
    }

    /**
     * Convert to API array
     */
    public function toApiArray()
    {
        return [
            'id' => $this->id,
            'tracking_id' => $this->tracking_id,
            'page_url' => $this->page_url,
            'viewport_size' => $this->viewport_size,
            'duration' => $this->duration,
            'recorded_at' => $this->recorded_at->toISOString(),
            'movement_stats' => $this->getMovementStatistics(),
            'scroll_behavior' => $this->getScrollBehavior(),
            'click_density' => $this->getClickDensityMap(),
            'has_heatmap_data' => !empty($this->heatmap_data),
        ];
    }

    /**
     * Get heatmap data for visualization
     */
    public function getHeatmapForVisualization()
    {
        if (empty($this->heatmap_data)) {
            $this->generateHeatmapData();
        }

        return [
            'data' => $this->heatmap_data,
            'viewport' => $this->viewport_size,
            'max_intensity' => $this->getMaxHeatmapIntensity(),
        ];
    }

    /**
     * Get maximum intensity value from heatmap
     */
    protected function getMaxHeatmapIntensity()
    {
        if (empty($this->heatmap_data)) {
            return 0;
        }

        $max = 0;
        foreach ($this->heatmap_data as $row) {
            foreach ($row as $value) {
                $max = max($max, $value);
            }
        }

        return $max;
    }
}

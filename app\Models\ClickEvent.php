<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class ClickEvent extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'event_id',
        'site_id',
        'visitor_id',
        'visitor_tracking_id',
        'session_id',
        'page_url',
        'element_type',
        'element_id',
        'element_class',
        'element_text',
        'element_selector',
        'click_x',
        'click_y',
        'viewport_x',
        'viewport_y',
        'button',
        'modifier_keys',
        'timestamp',
        'page_load_time',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'click_x' => 'integer',
        'click_y' => 'integer',
        'viewport_x' => 'integer',
        'viewport_y' => 'integer',
        'button' => 'integer',
        'modifier_keys' => 'array',
        'timestamp' => 'integer',
        'page_load_time' => 'integer',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($event) {
            if (!$event->event_id) {
                $event->event_id = Str::uuid();
            }
        });
    }

    /**
     * Get the site that owns the click event
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Get the visitor that owns the click event
     */
    public function visitor()
    {
        return $this->belongsTo(Visitor::class);
    }

    /**
     * Get the visitor tracking record
     */
    public function visitorTracking()
    {
        return $this->belongsTo(VisitorTracking::class);
    }

    /**
     * Get button name
     */
    public function getButtonNameAttribute()
    {
        $buttons = [
            0 => 'left',
            1 => 'middle',
            2 => 'right'
        ];

        return $buttons[$this->button] ?? 'unknown';
    }

    /**
     * Get modifier keys string
     */
    public function getModifierKeysStringAttribute()
    {
        if (!$this->modifier_keys) {
            return '';
        }

        $keys = [];
        if ($this->modifier_keys['ctrl'] ?? false) $keys[] = 'Ctrl';
        if ($this->modifier_keys['alt'] ?? false) $keys[] = 'Alt';
        if ($this->modifier_keys['shift'] ?? false) $keys[] = 'Shift';
        if ($this->modifier_keys['meta'] ?? false) $keys[] = 'Meta';

        return implode('+', $keys);
    }

    /**
     * Get element display name
     */
    public function getElementDisplayNameAttribute()
    {
        if ($this->element_text) {
            return $this->element_type . ': ' . Str::limit($this->element_text, 50);
        }

        if ($this->element_id) {
            return $this->element_type . '#' . $this->element_id;
        }

        if ($this->element_class) {
            return $this->element_type . '.' . $this->element_class;
        }

        return $this->element_type ?: 'unknown';
    }

    /**
     * Get click coordinates as string
     */
    public function getCoordinatesAttribute()
    {
        return "({$this->click_x}, {$this->click_y})";
    }

    /**
     * Get viewport coordinates as string
     */
    public function getViewportCoordinatesAttribute()
    {
        return "({$this->viewport_x}, {$this->viewport_y})";
    }

    /**
     * Get time since page load in seconds
     */
    public function getTimeSincePageLoadAttribute()
    {
        if (!$this->page_load_time || !$this->timestamp) {
            return null;
        }

        return round(($this->timestamp - $this->page_load_time) / 1000, 2);
    }

    /**
     * Check if click is on interactive element
     */
    public function isInteractiveElement()
    {
        $interactiveElements = [
            'a', 'button', 'input', 'select', 'textarea', 
            'label', 'option', 'summary', 'details'
        ];

        return in_array(strtolower($this->element_type), $interactiveElements);
    }

    /**
     * Check if click is on form element
     */
    public function isFormElement()
    {
        $formElements = ['input', 'select', 'textarea', 'button', 'label'];
        return in_array(strtolower($this->element_type), $formElements);
    }

    /**
     * Check if click is on navigation element
     */
    public function isNavigationElement()
    {
        return strtolower($this->element_type) === 'a' || 
               strpos(strtolower($this->element_class ?? ''), 'nav') !== false ||
               strpos(strtolower($this->element_class ?? ''), 'menu') !== false;
    }

    /**
     * Scope for element type
     */
    public function scopeElementType($query, $elementType)
    {
        return $query->where('element_type', $elementType);
    }

    /**
     * Scope for interactive elements
     */
    public function scopeInteractive($query)
    {
        return $query->whereIn('element_type', [
            'a', 'button', 'input', 'select', 'textarea', 
            'label', 'option', 'summary', 'details'
        ]);
    }

    /**
     * Scope for form elements
     */
    public function scopeFormElements($query)
    {
        return $query->whereIn('element_type', [
            'input', 'select', 'textarea', 'button', 'label'
        ]);
    }

    /**
     * Scope for navigation elements
     */
    public function scopeNavigation($query)
    {
        return $query->where('element_type', 'a')
            ->orWhere('element_class', 'like', '%nav%')
            ->orWhere('element_class', 'like', '%menu%');
    }

    /**
     * Scope for page URL
     */
    public function scopeForPage($query, $pageUrl)
    {
        return $query->where('page_url', $pageUrl);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for recent events
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Get click heatmap data for a page
     */
    public static function getClickHeatmapForPage($siteId, $pageUrl, $startDate = null, $endDate = null)
    {
        $query = self::where('site_id', $siteId)
            ->where('page_url', $pageUrl);

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        $clicks = $query->get();
        
        $heatmapData = [];
        foreach ($clicks as $click) {
            $key = floor($click->click_x / 20) . ',' . floor($click->click_y / 20); // 20px grid
            $heatmapData[$key] = ($heatmapData[$key] ?? 0) + 1;
        }

        return $heatmapData;
    }

    /**
     * Get most clicked elements for a page
     */
    public static function getMostClickedElements($siteId, $pageUrl, $limit = 10, $startDate = null, $endDate = null)
    {
        $query = self::where('site_id', $siteId)
            ->where('page_url', $pageUrl);

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }

        return $query->selectRaw('
                element_type,
                element_id,
                element_class,
                element_text,
                element_selector,
                COUNT(*) as click_count
            ')
            ->groupBy([
                'element_type', 'element_id', 'element_class', 
                'element_text', 'element_selector'
            ])
            ->orderBy('click_count', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Convert to API array
     */
    public function toApiArray()
    {
        return [
            'id' => $this->id,
            'event_id' => $this->event_id,
            'page_url' => $this->page_url,
            'element' => [
                'type' => $this->element_type,
                'id' => $this->element_id,
                'class' => $this->element_class,
                'text' => $this->element_text,
                'selector' => $this->element_selector,
                'display_name' => $this->element_display_name,
            ],
            'coordinates' => [
                'x' => $this->click_x,
                'y' => $this->click_y,
                'viewport_x' => $this->viewport_x,
                'viewport_y' => $this->viewport_y,
            ],
            'button' => $this->button_name,
            'modifier_keys' => $this->modifier_keys_string,
            'timestamp' => $this->timestamp,
            'time_since_page_load' => $this->time_since_page_load,
            'is_interactive' => $this->isInteractiveElement(),
            'is_form_element' => $this->isFormElement(),
            'is_navigation' => $this->isNavigationElement(),
            'created_at' => $this->created_at->toISOString(),
        ];
    }
}

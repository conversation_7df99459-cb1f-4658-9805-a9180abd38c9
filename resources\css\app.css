@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom component styles */
@layer components {
  /* Button styles */
  .btn {
    @apply inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }

  .btn-primary {
    @apply text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-secondary {
    @apply text-gray-700 bg-white border-gray-300 hover:bg-gray-50 focus:ring-blue-500;
  }

  .btn-success {
    @apply text-white bg-green-600 hover:bg-green-700 focus:ring-green-500;
  }

  .btn-warning {
    @apply text-white bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500;
  }

  .btn-danger {
    @apply text-white bg-red-600 hover:bg-red-700 focus:ring-red-500;
  }

  .btn-outline {
    @apply text-gray-700 bg-white border-gray-300 hover:bg-gray-50 focus:ring-blue-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-sm {
    @apply px-2 py-0.5 text-xs;
  }

  .badge-primary {
    @apply bg-blue-100 text-blue-800;
  }

  .badge-success {
    @apply bg-green-100 text-green-800;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-danger {
    @apply bg-red-100 text-red-800;
  }

  .badge-gray {
    @apply bg-gray-100 text-gray-800;
  }

  /* Card styles */
  .card {
    @apply bg-white overflow-hidden shadow rounded-lg;
  }

  .card-header {
    @apply px-4 py-5 sm:px-6 border-b border-gray-200;
  }

  .card-body {
    @apply px-4 py-5 sm:p-6;
  }

  .card-footer {
    @apply px-4 py-4 sm:px-6 bg-gray-50 border-t border-gray-200;
  }

  /* Form styles */
  .form-input {
    @apply block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
  }

  .form-textarea {
    @apply block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
  }

  .form-select {
    @apply block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
  }

  .form-checkbox {
    @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded;
  }

  .form-radio {
    @apply h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300;
  }

  /* Alert styles */
  .alert {
    @apply rounded-md p-4;
  }

  .alert-success {
    @apply bg-green-50 border border-green-200 text-green-800;
  }

  .alert-warning {
    @apply bg-yellow-50 border border-yellow-200 text-yellow-800;
  }

  .alert-error {
    @apply bg-red-50 border border-red-200 text-red-800;
  }

  .alert-info {
    @apply bg-blue-50 border border-blue-200 text-blue-800;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
  }

  /* Chat message styles */
  .message-bubble {
    @apply max-w-xs lg:max-w-md px-4 py-2 rounded-lg text-sm;
  }

  .message-bubble-agent {
    @apply bg-blue-600 text-white;
  }

  .message-bubble-visitor {
    @apply bg-gray-200 text-gray-900;
  }

  .message-bubble-system {
    @apply bg-yellow-100 text-yellow-800 text-center italic;
  }

  /* Status indicators */
  .status-online {
    @apply bg-green-400;
  }

  .status-away {
    @apply bg-yellow-400;
  }

  .status-busy {
    @apply bg-red-400;
  }

  .status-offline {
    @apply bg-gray-400;
  }

  /* Sidebar styles */
  .sidebar-link {
    @apply flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200;
  }

  .sidebar-link-active {
    @apply bg-blue-100 text-blue-700 border-r-2 border-blue-700;
  }

  .sidebar-link-inactive {
    @apply text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }

  /* Table styles */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }

  .table-header {
    @apply bg-gray-50;
  }

  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  /* Modal styles */
  .modal-overlay {
    @apply fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity z-40;
  }

  .modal-container {
    @apply fixed inset-0 z-50 overflow-y-auto;
  }

  .modal-content {
    @apply inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full;
  }

  /* Dropdown styles */
  .dropdown-menu {
    @apply origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50;
  }

  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900;
  }

  /* Tooltip styles */
  .tooltip {
    @apply absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg;
  }

  /* Progress bar styles */
  .progress-bar {
    @apply w-full bg-gray-200 rounded-full h-2;
  }

  .progress-fill {
    @apply h-2 bg-blue-600 rounded-full transition-all duration-300;
  }

  /* Avatar styles */
  .avatar {
    @apply rounded-full object-cover;
  }

  .avatar-sm {
    @apply h-6 w-6;
  }

  .avatar-md {
    @apply h-8 w-8;
  }

  .avatar-lg {
    @apply h-10 w-10;
  }

  .avatar-xl {
    @apply h-12 w-12;
  }

  /* Divider styles */
  .divider {
    @apply border-t border-gray-200 my-4;
  }

  .divider-vertical {
    @apply border-l border-gray-200 mx-4;
  }
}

/* Custom utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

/* Global styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #374151;
  background-color: #f9fafb;
}

/* Focus styles */
*:focus {
  outline: none;
}

/* Selection styles */
::selection {
  background-color: #3b82f6;
  color: white;
}

/* Smooth transitions */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
  /* Dark mode styles can be added here */
}

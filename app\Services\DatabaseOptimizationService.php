<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class DatabaseOptimizationService
{
    /**
     * Optimize database performance
     */
    public function optimizeDatabase(): array
    {
        $results = [];

        try {
            // Add missing indexes
            $results['indexes'] = $this->addMissingIndexes();

            // Analyze slow queries
            $results['slow_queries'] = $this->analyzeSlowQueries();

            // Optimize tables
            $results['table_optimization'] = $this->optimizeTables();

            // Update table statistics
            $results['statistics'] = $this->updateTableStatistics();

            // Clean up query cache
            $results['cache_cleanup'] = $this->cleanupQueryCache();

            Log::info('Database optimization completed', $results);

        } catch (\Exception $e) {
            Log::error('Database optimization failed: ' . $e->getMessage());
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Add missing database indexes for better performance
     */
    protected function addMissingIndexes(): array
    {
        $indexes = [];

        try {
            // Chat sessions indexes
            $this->addIndexIfNotExists('chat_sessions', 'idx_sessions_status_site', ['status', 'site_id']);
            $this->addIndexIfNotExists('chat_sessions', 'idx_sessions_started_at', ['started_at']);
            $this->addIndexIfNotExists('chat_sessions', 'idx_sessions_agent_status', ['agent_id', 'status']);
            $indexes[] = 'chat_sessions indexes added';

            // Chat messages indexes
            $this->addIndexIfNotExists('chat_messages', 'idx_messages_session_sent', ['session_id', 'sent_at']);
            $this->addIndexIfNotExists('chat_messages', 'idx_messages_sender_type', ['sender_type']);
            $indexes[] = 'chat_messages indexes added';

            // Visitor behaviors indexes
            $this->addIndexIfNotExists('visitor_behaviors', 'idx_behaviors_visitor_event', ['visitor_id', 'event_type']);
            $this->addIndexIfNotExists('visitor_behaviors', 'idx_behaviors_created_at', ['created_at']);
            $this->addIndexIfNotExists('visitor_behaviors', 'idx_behaviors_page_url', ['page_url']);
            $indexes[] = 'visitor_behaviors indexes added';

            // Visitors indexes
            $this->addIndexIfNotExists('visitors', 'idx_visitors_site_ip', ['site_id', 'ip_address']);
            $this->addIndexIfNotExists('visitors', 'idx_visitors_last_visit', ['last_visit_at']);
            $indexes[] = 'visitors indexes added';

            // Contact messages indexes
            $this->addIndexIfNotExists('contact_messages', 'idx_contact_status_priority', ['status', 'priority']);
            $this->addIndexIfNotExists('contact_messages', 'idx_contact_submitted_at', ['submitted_at']);
            $indexes[] = 'contact_messages indexes added';

            // Users indexes
            $this->addIndexIfNotExists('users', 'idx_users_role_status', ['role', 'status']);
            $this->addIndexIfNotExists('users', 'idx_users_is_active', ['is_active']);
            $indexes[] = 'users indexes added';

        } catch (\Exception $e) {
            $indexes[] = 'Error adding indexes: ' . $e->getMessage();
        }

        return $indexes;
    }

    /**
     * Add index if it doesn't exist
     */
    protected function addIndexIfNotExists(string $table, string $indexName, array $columns): void
    {
        $columnList = implode(',', $columns);
        
        // Check if index exists
        $exists = DB::select("
            SELECT COUNT(*) as count 
            FROM information_schema.statistics 
            WHERE table_schema = DATABASE() 
            AND table_name = ? 
            AND index_name = ?
        ", [$table, $indexName]);

        if ($exists[0]->count == 0) {
            DB::statement("ALTER TABLE `{$table}` ADD INDEX `{$indexName}` ({$columnList})");
            Log::info("Added index {$indexName} to table {$table}");
        }
    }

    /**
     * Analyze slow queries
     */
    protected function analyzeSlowQueries(): array
    {
        $analysis = [];

        try {
            // Enable slow query log temporarily
            DB::statement("SET GLOBAL slow_query_log = 'ON'");
            DB::statement("SET GLOBAL long_query_time = 1");

            // Get slow query statistics
            $slowQueries = DB::select("SHOW GLOBAL STATUS LIKE 'Slow_queries'");
            $analysis['slow_query_count'] = $slowQueries[0]->Value ?? 0;

            // Get most expensive queries from performance schema (MySQL 5.7+)
            try {
                $expensiveQueries = DB::select("
                    SELECT 
                        DIGEST_TEXT as query,
                        COUNT_STAR as exec_count,
                        AVG_TIMER_WAIT/1000000000 as avg_time_sec,
                        SUM_TIMER_WAIT/1000000000 as total_time_sec
                    FROM performance_schema.events_statements_summary_by_digest 
                    WHERE DIGEST_TEXT IS NOT NULL
                    ORDER BY AVG_TIMER_WAIT DESC 
                    LIMIT 5
                ");
                
                $analysis['expensive_queries'] = collect($expensiveQueries)->map(function ($query) {
                    return [
                        'query' => substr($query->query, 0, 100) . '...',
                        'avg_time' => round($query->avg_time_sec, 3),
                        'exec_count' => $query->exec_count
                    ];
                })->toArray();

            } catch (\Exception $e) {
                $analysis['expensive_queries'] = 'Performance schema not available';
            }

        } catch (\Exception $e) {
            $analysis['error'] = $e->getMessage();
        }

        return $analysis;
    }

    /**
     * Optimize database tables
     */
    protected function optimizeTables(): array
    {
        $results = [];
        
        $tables = [
            'chat_sessions',
            'chat_messages', 
            'visitor_behaviors',
            'visitors',
            'contact_messages',
            'users'
        ];

        foreach ($tables as $table) {
            try {
                DB::statement("OPTIMIZE TABLE `{$table}`");
                $results[] = "Optimized table: {$table}";
            } catch (\Exception $e) {
                $results[] = "Failed to optimize {$table}: " . $e->getMessage();
            }
        }

        return $results;
    }

    /**
     * Update table statistics
     */
    protected function updateTableStatistics(): array
    {
        $results = [];

        try {
            // Update table statistics for better query planning
            $tables = DB::select("
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
            ");

            foreach ($tables as $table) {
                try {
                    DB::statement("ANALYZE TABLE `{$table->table_name}`");
                    $results[] = "Analyzed table: {$table->table_name}";
                } catch (\Exception $e) {
                    $results[] = "Failed to analyze {$table->table_name}: " . $e->getMessage();
                }
            }

        } catch (\Exception $e) {
            $results[] = 'Error updating statistics: ' . $e->getMessage();
        }

        return $results;
    }

    /**
     * Clean up query cache
     */
    protected function cleanupQueryCache(): array
    {
        $results = [];

        try {
            // Reset query cache
            DB::statement("RESET QUERY CACHE");
            $results[] = 'Query cache reset';

            // Get cache statistics
            $cacheStats = DB::select("SHOW STATUS LIKE 'Qcache%'");
            $stats = [];
            foreach ($cacheStats as $stat) {
                $stats[$stat->Variable_name] = $stat->Value;
            }
            $results['cache_stats'] = $stats;

        } catch (\Exception $e) {
            $results[] = 'Error cleaning query cache: ' . $e->getMessage();
        }

        return $results;
    }

    /**
     * Get database performance metrics
     */
    public function getPerformanceMetrics(): array
    {
        $metrics = [];

        try {
            // Connection statistics
            $connections = DB::select("SHOW STATUS WHERE Variable_name IN ('Connections', 'Threads_connected', 'Max_used_connections')");
            foreach ($connections as $conn) {
                $metrics['connections'][$conn->Variable_name] = $conn->Value;
            }

            // Query statistics
            $queries = DB::select("SHOW STATUS WHERE Variable_name IN ('Questions', 'Queries', 'Slow_queries')");
            foreach ($queries as $query) {
                $metrics['queries'][$query->Variable_name] = $query->Value;
            }

            // Table statistics
            $tableStats = DB::select("
                SELECT 
                    table_name,
                    table_rows,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                    ROUND((data_length / 1024 / 1024), 2) AS data_mb,
                    ROUND((index_length / 1024 / 1024), 2) AS index_mb
                FROM information_schema.TABLES 
                WHERE table_schema = DATABASE()
                ORDER BY (data_length + index_length) DESC
            ");

            $metrics['tables'] = collect($tableStats)->mapWithKeys(function ($table) {
                return [$table->table_name => [
                    'rows' => $table->table_rows,
                    'size_mb' => $table->size_mb,
                    'data_mb' => $table->data_mb,
                    'index_mb' => $table->index_mb
                ]];
            })->toArray();

            // Cache metrics in Redis for dashboard
            Cache::put('db_performance_metrics', $metrics, 300);

        } catch (\Exception $e) {
            $metrics['error'] = $e->getMessage();
        }

        return $metrics;
    }

    /**
     * Check for database issues
     */
    public function checkDatabaseHealth(): array
    {
        $issues = [];

        try {
            // Check for tables without primary keys
            $noPrimaryKey = DB::select("
                SELECT table_name 
                FROM information_schema.tables t
                WHERE table_schema = DATABASE()
                AND NOT EXISTS (
                    SELECT * FROM information_schema.table_constraints tc
                    WHERE tc.table_schema = t.table_schema
                    AND tc.table_name = t.table_name
                    AND tc.constraint_type = 'PRIMARY KEY'
                )
            ");

            if (!empty($noPrimaryKey)) {
                $issues[] = 'Tables without primary keys: ' . implode(', ', array_column($noPrimaryKey, 'table_name'));
            }

            // Check for large tables that might need partitioning
            $largeTables = DB::select("
                SELECT table_name, table_rows
                FROM information_schema.TABLES 
                WHERE table_schema = DATABASE()
                AND table_rows > 1000000
            ");

            if (!empty($largeTables)) {
                $tableNames = array_map(function ($table) {
                    return $table->table_name . ' (' . number_format($table->table_rows) . ' rows)';
                }, $largeTables);
                $issues[] = 'Large tables that might need optimization: ' . implode(', ', $tableNames);
            }

            // Check for unused indexes
            // This would require more complex analysis of query patterns

        } catch (\Exception $e) {
            $issues[] = 'Error checking database health: ' . $e->getMessage();
        }

        return $issues;
    }
}

<?php

/**
 * 系统测试运行脚本
 * 用于快速验证系统的基本功能是否正常
 */

require_once __DIR__ . '/../vendor/autoload.php';

class SystemTester
{
    private $baseUrl;
    private $testResults = [];

    public function __construct($baseUrl = 'http://localhost:8000')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }

    /**
     * 运行所有测试
     */
    public function runAllTests()
    {
        echo "🚀 开始系统测试...\n\n";

        $this->testDatabaseConnection();
        $this->testRedisConnection();
        $this->testWebServerResponse();
        $this->testApiEndpoints();
        $this->testFilePermissions();
        $this->testEnvironmentConfiguration();

        $this->displayResults();
    }

    /**
     * 测试数据库连接
     */
    private function testDatabaseConnection()
    {
        echo "📊 测试数据库连接...\n";
        
        try {
            $env = $this->loadEnvFile();
            $dsn = "mysql:host={$env['DB_HOST']};port={$env['DB_PORT']};dbname={$env['DB_DATABASE']}";
            $pdo = new PDO($dsn, $env['DB_USERNAME'], $env['DB_PASSWORD']);
            
            // 测试查询
            $stmt = $pdo->query("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '{$env['DB_DATABASE']}'");
            $tableCount = $stmt->fetchColumn();
            
            $this->testResults['database'] = [
                'status' => 'success',
                'message' => "数据库连接成功，共有 {$tableCount} 个表"
            ];
            echo "✅ 数据库连接正常\n";
            
        } catch (Exception $e) {
            $this->testResults['database'] = [
                'status' => 'error',
                'message' => '数据库连接失败: ' . $e->getMessage()
            ];
            echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试Redis连接
     */
    private function testRedisConnection()
    {
        echo "🔄 测试Redis连接...\n";
        
        try {
            $env = $this->loadEnvFile();
            $redis = new Redis();
            $redis->connect($env['REDIS_HOST'], $env['REDIS_PORT']);
            
            if (!empty($env['REDIS_PASSWORD'])) {
                $redis->auth($env['REDIS_PASSWORD']);
            }
            
            // 测试读写
            $redis->set('test_key', 'test_value');
            $value = $redis->get('test_key');
            $redis->del('test_key');
            
            if ($value === 'test_value') {
                $this->testResults['redis'] = [
                    'status' => 'success',
                    'message' => 'Redis连接和读写测试成功'
                ];
                echo "✅ Redis连接正常\n";
            } else {
                throw new Exception('Redis读写测试失败');
            }
            
        } catch (Exception $e) {
            $this->testResults['redis'] = [
                'status' => 'error',
                'message' => 'Redis连接失败: ' . $e->getMessage()
            ];
            echo "❌ Redis连接失败: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试Web服务器响应
     */
    private function testWebServerResponse()
    {
        echo "🌐 测试Web服务器响应...\n";
        
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->baseUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                throw new Exception("cURL错误: $error");
            }
            
            if ($httpCode >= 200 && $httpCode < 400) {
                $this->testResults['webserver'] = [
                    'status' => 'success',
                    'message' => "Web服务器响应正常 (HTTP {$httpCode})"
                ];
                echo "✅ Web服务器响应正常\n";
            } else {
                throw new Exception("HTTP错误码: $httpCode");
            }
            
        } catch (Exception $e) {
            $this->testResults['webserver'] = [
                'status' => 'error',
                'message' => 'Web服务器测试失败: ' . $e->getMessage()
            ];
            echo "❌ Web服务器测试失败: " . $e->getMessage() . "\n";
        }
    }

    /**
     * 测试API端点
     */
    private function testApiEndpoints()
    {
        echo "🔌 测试API端点...\n";
        
        $endpoints = [
            '/api/v1/auth/login' => 'POST',
            '/api/v1/chat/sessions' => 'GET',
            '/api/v1/visitors' => 'GET',
            '/api/v1/analytics/overview' => 'GET'
        ];
        
        $successCount = 0;
        $totalCount = count($endpoints);
        
        foreach ($endpoints as $endpoint => $method) {
            try {
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $this->baseUrl . $endpoint);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_TIMEOUT, 5);
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Accept: application/json',
                    'Content-Type: application/json'
                ]);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                // API端点存在且返回JSON响应即为成功（即使是401未授权也算正常）
                if ($httpCode < 500 && $this->isValidJson($response)) {
                    $successCount++;
                }
                
            } catch (Exception $e) {
                // 忽略单个端点错误，继续测试其他端点
            }
        }
        
        if ($successCount === $totalCount) {
            $this->testResults['api'] = [
                'status' => 'success',
                'message' => "所有API端点响应正常 ({$successCount}/{$totalCount})"
            ];
            echo "✅ API端点测试通过\n";
        } else {
            $this->testResults['api'] = [
                'status' => 'warning',
                'message' => "部分API端点可能有问题 ({$successCount}/{$totalCount})"
            ];
            echo "⚠️ 部分API端点可能有问题\n";
        }
    }

    /**
     * 测试文件权限
     */
    private function testFilePermissions()
    {
        echo "📁 测试文件权限...\n";
        
        $directories = [
            'storage/logs',
            'storage/app',
            'storage/framework/cache',
            'storage/framework/sessions',
            'storage/framework/views',
            'bootstrap/cache'
        ];
        
        $issues = [];
        
        foreach ($directories as $dir) {
            $fullPath = __DIR__ . '/../' . $dir;
            
            if (!is_dir($fullPath)) {
                $issues[] = "目录不存在: $dir";
                continue;
            }
            
            if (!is_writable($fullPath)) {
                $issues[] = "目录不可写: $dir";
            }
        }
        
        if (empty($issues)) {
            $this->testResults['permissions'] = [
                'status' => 'success',
                'message' => '所有必要目录权限正常'
            ];
            echo "✅ 文件权限检查通过\n";
        } else {
            $this->testResults['permissions'] = [
                'status' => 'error',
                'message' => '文件权限问题: ' . implode(', ', $issues)
            ];
            echo "❌ 文件权限问题: " . implode(', ', $issues) . "\n";
        }
    }

    /**
     * 测试环境配置
     */
    private function testEnvironmentConfiguration()
    {
        echo "⚙️ 测试环境配置...\n";
        
        $requiredExtensions = [
            'pdo_mysql', 'redis', 'gd', 'curl', 'mbstring', 
            'xml', 'zip', 'json', 'openssl', 'fileinfo', 'tokenizer'
        ];
        
        $missingExtensions = [];
        
        foreach ($requiredExtensions as $extension) {
            if (!extension_loaded($extension)) {
                $missingExtensions[] = $extension;
            }
        }
        
        $env = $this->loadEnvFile();
        $configIssues = [];
        
        if (empty($env['APP_KEY'])) {
            $configIssues[] = 'APP_KEY未设置';
        }
        
        if ($env['APP_DEBUG'] === 'true' && $env['APP_ENV'] === 'production') {
            $configIssues[] = '生产环境不应开启DEBUG模式';
        }
        
        if (empty($missingExtensions) && empty($configIssues)) {
            $this->testResults['environment'] = [
                'status' => 'success',
                'message' => '环境配置检查通过'
            ];
            echo "✅ 环境配置正常\n";
        } else {
            $issues = array_merge($missingExtensions, $configIssues);
            $this->testResults['environment'] = [
                'status' => 'error',
                'message' => '环境配置问题: ' . implode(', ', $issues)
            ];
            echo "❌ 环境配置问题: " . implode(', ', $issues) . "\n";
        }
    }

    /**
     * 显示测试结果
     */
    private function displayResults()
    {
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "📋 测试结果汇总\n";
        echo str_repeat("=", 50) . "\n";
        
        $successCount = 0;
        $totalCount = count($this->testResults);
        
        foreach ($this->testResults as $test => $result) {
            $icon = $result['status'] === 'success' ? '✅' : 
                   ($result['status'] === 'warning' ? '⚠️' : '❌');
            
            echo sprintf("%-15s %s %s\n", ucfirst($test), $icon, $result['message']);
            
            if ($result['status'] === 'success') {
                $successCount++;
            }
        }
        
        echo str_repeat("-", 50) . "\n";
        echo sprintf("总体结果: %d/%d 项测试通过\n", $successCount, $totalCount);
        
        if ($successCount === $totalCount) {
            echo "🎉 所有测试通过！系统运行正常。\n";
        } elseif ($successCount >= $totalCount * 0.8) {
            echo "⚠️ 大部分测试通过，但有一些问题需要注意。\n";
        } else {
            echo "❌ 多项测试失败，请检查系统配置。\n";
        }
    }

    /**
     * 加载环境变量文件
     */
    private function loadEnvFile()
    {
        $envFile = __DIR__ . '/../.env';
        $env = [];
        
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
                    list($key, $value) = explode('=', $line, 2);
                    $env[trim($key)] = trim($value, '"\'');
                }
            }
        }
        
        return $env;
    }

    /**
     * 检查是否为有效JSON
     */
    private function isValidJson($string)
    {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }
}

// 运行测试
if (php_sapi_name() === 'cli') {
    $baseUrl = $argv[1] ?? 'http://localhost:8000';
    $tester = new SystemTester($baseUrl);
    $tester->runAllTests();
} else {
    echo "请在命令行中运行此脚本\n";
    echo "用法: php tests/run_tests.php [base_url]\n";
}

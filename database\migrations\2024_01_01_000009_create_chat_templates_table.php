<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChatTemplatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('chat_templates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('site_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // Creator
            
            $table->string('name');
            $table->text('content');
            $table->text('description')->nullable();
            $table->enum('type', ['greeting', 'closing', 'common_response', 'escalation', 'custom'])->default('custom');
            $table->json('tags')->nullable();
            $table->boolean('is_global')->default(false); // Available to all agents
            $table->boolean('is_active')->default(true);
            $table->integer('usage_count')->default(0);
            $table->string('shortcut')->nullable(); // Keyboard shortcut
            
            // Multi-language support
            $table->string('language')->default('en');
            $table->json('translations')->nullable();
            
            $table->timestamps();
            
            $table->index(['site_id', 'is_active']);
            $table->index(['site_id', 'type']);
            $table->index(['user_id', 'is_active']);
            $table->index('shortcut');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('chat_templates');
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Translation extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'st_translations';

    protected $fillable = [
        'translation_id',
        'language_id',
        'namespace',
        'group',
        'key',
        'value',
        'is_system',
        'is_approved',
        'context',
        'metadata',
    ];

    protected $casts = [
        'is_system' => 'boolean',
        'is_approved' => 'boolean',
        'metadata' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($translation) {
            if (empty($translation->translation_id)) {
                $translation->translation_id = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the language that owns the translation
     */
    public function language()
    {
        return $this->belongsTo(Language::class);
    }

    /**
     * Scope to get system translations
     */
    public function scopeSystem($query)
    {
        return $query->where('is_system', true);
    }

    /**
     * Scope to get custom translations
     */
    public function scopeCustom($query)
    {
        return $query->where('is_system', false);
    }

    /**
     * Scope to get approved translations
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope to get translations by namespace
     */
    public function scopeByNamespace($query, $namespace)
    {
        return $query->where('namespace', $namespace);
    }

    /**
     * Scope to get translations by group
     */
    public function scopeByGroup($query, $group)
    {
        return $query->where('group', $group);
    }

    /**
     * Get default system translations
     */
    public static function getDefaultSystemTranslations()
    {
        return [
            // Chat Interface
            'chat' => [
                'welcome_message' => 'Hello! How can I help you today?',
                'agent_typing' => 'Agent is typing...',
                'visitor_typing' => 'Visitor is typing...',
                'send_message' => 'Send message',
                'type_message' => 'Type your message...',
                'file_upload' => 'Upload file',
                'emoji_picker' => 'Choose emoji',
                'chat_ended' => 'Chat session ended',
                'chat_transferred' => 'Chat transferred to another agent',
                'agent_joined' => 'Agent joined the chat',
                'agent_left' => 'Agent left the chat',
                'connection_lost' => 'Connection lost. Reconnecting...',
                'connection_restored' => 'Connection restored',
                'message_failed' => 'Message failed to send',
                'retry_send' => 'Retry',
                'download_transcript' => 'Download chat transcript',
                'print_transcript' => 'Print chat transcript',
                'minimize_chat' => 'Minimize chat',
                'maximize_chat' => 'Maximize chat',
                'close_chat' => 'Close chat',
                'new_message' => 'New message',
                'unread_messages' => 'unread messages',
                'scroll_to_bottom' => 'Scroll to bottom',
            ],

            // Pre-chat Form
            'prechat' => [
                'title' => 'Start a conversation',
                'subtitle' => 'Please provide your information to begin',
                'name_label' => 'Your Name',
                'name_placeholder' => 'Enter your name',
                'email_label' => 'Email Address',
                'email_placeholder' => 'Enter your email',
                'phone_label' => 'Phone Number',
                'phone_placeholder' => 'Enter your phone number',
                'company_label' => 'Company',
                'company_placeholder' => 'Enter your company name',
                'subject_label' => 'Subject',
                'subject_placeholder' => 'What can we help you with?',
                'message_label' => 'Message',
                'message_placeholder' => 'Describe your inquiry...',
                'department_label' => 'Department',
                'department_placeholder' => 'Select department',
                'priority_label' => 'Priority',
                'priority_placeholder' => 'Select priority',
                'start_chat' => 'Start Chat',
                'required_field' => 'This field is required',
                'invalid_email' => 'Please enter a valid email address',
                'invalid_phone' => 'Please enter a valid phone number',
            ],

            // Post-chat Survey
            'postchat' => [
                'title' => 'How was your experience?',
                'subtitle' => 'Please rate your chat experience',
                'rating_label' => 'Overall satisfaction',
                'comment_label' => 'Additional comments',
                'comment_placeholder' => 'Tell us about your experience...',
                'submit_survey' => 'Submit Feedback',
                'skip_survey' => 'Skip',
                'thank_you' => 'Thank you for your feedback!',
                'rating_excellent' => 'Excellent',
                'rating_good' => 'Good',
                'rating_average' => 'Average',
                'rating_poor' => 'Poor',
                'rating_terrible' => 'Terrible',
            ],

            // Offline Messages
            'offline' => [
                'title' => 'We\'re currently offline',
                'subtitle' => 'Leave us a message and we\'ll get back to you',
                'name_label' => 'Your Name',
                'email_label' => 'Email Address',
                'subject_label' => 'Subject',
                'message_label' => 'Message',
                'send_message' => 'Send Message',
                'message_sent' => 'Thank you! We\'ll get back to you soon.',
                'business_hours' => 'Business Hours',
                'contact_info' => 'Contact Information',
                'alternative_contact' => 'You can also reach us at:',
            ],

            // Agent Interface
            'agent' => [
                'online' => 'Online',
                'offline' => 'Offline',
                'away' => 'Away',
                'busy' => 'Busy',
                'break' => 'On Break',
                'accept_chat' => 'Accept Chat',
                'decline_chat' => 'Decline Chat',
                'transfer_chat' => 'Transfer Chat',
                'end_chat' => 'End Chat',
                'visitor_info' => 'Visitor Information',
                'chat_history' => 'Chat History',
                'internal_notes' => 'Internal Notes',
                'add_note' => 'Add Note',
                'save_note' => 'Save Note',
                'visitor_location' => 'Visitor Location',
                'visitor_browser' => 'Browser',
                'visitor_os' => 'Operating System',
                'visitor_device' => 'Device',
                'page_history' => 'Page History',
                'referrer' => 'Referrer',
                'search_keywords' => 'Search Keywords',
                'visit_duration' => 'Visit Duration',
                'page_views' => 'Page Views',
                'return_visitor' => 'Return Visitor',
                'first_visit' => 'First Visit',
            ],

            // System Messages
            'system' => [
                'loading' => 'Loading...',
                'saving' => 'Saving...',
                'saved' => 'Saved',
                'error' => 'An error occurred',
                'success' => 'Success',
                'warning' => 'Warning',
                'info' => 'Information',
                'confirm' => 'Are you sure?',
                'yes' => 'Yes',
                'no' => 'No',
                'ok' => 'OK',
                'cancel' => 'Cancel',
                'close' => 'Close',
                'back' => 'Back',
                'next' => 'Next',
                'previous' => 'Previous',
                'continue' => 'Continue',
                'finish' => 'Finish',
                'submit' => 'Submit',
                'save' => 'Save',
                'edit' => 'Edit',
                'delete' => 'Delete',
                'add' => 'Add',
                'remove' => 'Remove',
                'search' => 'Search',
                'filter' => 'Filter',
                'sort' => 'Sort',
                'refresh' => 'Refresh',
                'reload' => 'Reload',
                'print' => 'Print',
                'download' => 'Download',
                'upload' => 'Upload',
                'copy' => 'Copy',
                'paste' => 'Paste',
                'cut' => 'Cut',
                'undo' => 'Undo',
                'redo' => 'Redo',
                'select_all' => 'Select All',
                'clear_all' => 'Clear All',
            ],

            // Time and Date
            'time' => [
                'now' => 'now',
                'just_now' => 'just now',
                'minute_ago' => 'a minute ago',
                'minutes_ago' => 'minutes ago',
                'hour_ago' => 'an hour ago',
                'hours_ago' => 'hours ago',
                'day_ago' => 'a day ago',
                'days_ago' => 'days ago',
                'week_ago' => 'a week ago',
                'weeks_ago' => 'weeks ago',
                'month_ago' => 'a month ago',
                'months_ago' => 'months ago',
                'year_ago' => 'a year ago',
                'years_ago' => 'years ago',
                'today' => 'Today',
                'yesterday' => 'Yesterday',
                'tomorrow' => 'Tomorrow',
                'monday' => 'Monday',
                'tuesday' => 'Tuesday',
                'wednesday' => 'Wednesday',
                'thursday' => 'Thursday',
                'friday' => 'Friday',
                'saturday' => 'Saturday',
                'sunday' => 'Sunday',
                'january' => 'January',
                'february' => 'February',
                'march' => 'March',
                'april' => 'April',
                'may' => 'May',
                'june' => 'June',
                'july' => 'July',
                'august' => 'August',
                'september' => 'September',
                'october' => 'October',
                'november' => 'November',
                'december' => 'December',
            ],

            // File Upload
            'upload' => [
                'select_file' => 'Select file',
                'drag_drop' => 'Drag and drop files here',
                'file_too_large' => 'File is too large',
                'invalid_file_type' => 'Invalid file type',
                'upload_failed' => 'Upload failed',
                'upload_success' => 'File uploaded successfully',
                'max_files_exceeded' => 'Maximum number of files exceeded',
                'remove_file' => 'Remove file',
                'file_name' => 'File name',
                'file_size' => 'File size',
                'file_type' => 'File type',
            ],

            // Validation
            'validation' => [
                'required' => 'This field is required',
                'email' => 'Please enter a valid email address',
                'phone' => 'Please enter a valid phone number',
                'url' => 'Please enter a valid URL',
                'number' => 'Please enter a valid number',
                'min_length' => 'Minimum length is :min characters',
                'max_length' => 'Maximum length is :max characters',
                'pattern' => 'Please match the requested format',
            ],
        ];
    }

    /**
     * Get full translation key
     */
    public function getFullKey()
    {
        $parts = array_filter([$this->namespace, $this->group, $this->key]);
        return implode('.', $parts);
    }

    /**
     * Parse full key into components
     */
    public static function parseKey($fullKey)
    {
        $parts = explode('.', $fullKey);
        
        if (count($parts) === 1) {
            return [
                'namespace' => null,
                'group' => null,
                'key' => $parts[0],
            ];
        } elseif (count($parts) === 2) {
            return [
                'namespace' => null,
                'group' => $parts[0],
                'key' => $parts[1],
            ];
        } else {
            return [
                'namespace' => $parts[0],
                'group' => $parts[1],
                'key' => implode('.', array_slice($parts, 2)),
            ];
        }
    }

    /**
     * Approve translation
     */
    public function approve()
    {
        $this->update(['is_approved' => true]);
    }

    /**
     * Reject translation
     */
    public function reject()
    {
        $this->update(['is_approved' => false]);
    }
}

<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <form @submit.prevent="transferChat">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                <ArrowRightIcon class="h-6 w-6 text-blue-600" />
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                  Transfer Chat Session
                </h3>
                <div class="mt-4 space-y-4">
                  <p class="text-sm text-gray-500">
                    Transfer this chat session to another agent. The visitor will be notified of the transfer.
                  </p>

                  <!-- Session info -->
                  <div class="bg-gray-50 p-3 rounded-md">
                    <div class="flex items-center space-x-3">
                      <img :src="session?.visitor_avatar || '/images/default-avatar.png'" 
                           :alt="session?.visitor_name" 
                           class="h-8 w-8 rounded-full">
                      <div>
                        <p class="text-sm font-medium text-gray-900">
                          {{ session?.visitor_name || 'Anonymous' }}
                        </p>
                        <p class="text-xs text-gray-500">
                          {{ session?.subject || 'No subject' }}
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- Target agent selection -->
                  <div>
                    <label for="target_agent" class="block text-sm font-medium text-gray-700">
                      Transfer to Agent
                    </label>
                    <select
                      id="target_agent"
                      v-model="form.targetAgentId"
                      required
                      class="mt-1 form-select"
                      :class="{ 'border-red-300': errors.targetAgentId }"
                    >
                      <option value="">Select an agent</option>
                      <option
                        v-for="agent in availableAgents"
                        :key="agent.id"
                        :value="agent.id"
                        :disabled="agent.status === 'offline'"
                      >
                        {{ agent.name }} 
                        <span class="text-gray-500">({{ agent.status }})</span>
                        <span v-if="agent.active_sessions_count > 0" class="text-gray-500">
                          - {{ agent.active_sessions_count }} active
                        </span>
                      </option>
                    </select>
                    <p v-if="errors.targetAgentId" class="mt-1 text-sm text-red-600">{{ errors.targetAgentId }}</p>
                  </div>

                  <!-- Transfer reason -->
                  <div>
                    <label for="reason" class="block text-sm font-medium text-gray-700">
                      Reason for Transfer (optional)
                    </label>
                    <select
                      id="reason"
                      v-model="form.reason"
                      class="mt-1 form-select"
                    >
                      <option value="">Select a reason</option>
                      <option value="expertise">Requires specific expertise</option>
                      <option value="language">Language preference</option>
                      <option value="workload">Workload balancing</option>
                      <option value="escalation">Escalation required</option>
                      <option value="shift_change">Shift change</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <!-- Custom reason -->
                  <div v-if="form.reason === 'other'">
                    <label for="custom_reason" class="block text-sm font-medium text-gray-700">
                      Custom Reason
                    </label>
                    <textarea
                      id="custom_reason"
                      v-model="form.customReason"
                      rows="2"
                      class="mt-1 form-textarea"
                      placeholder="Please specify the reason for transfer..."
                    ></textarea>
                  </div>

                  <!-- Transfer message -->
                  <div>
                    <label for="transfer_message" class="block text-sm font-medium text-gray-700">
                      Message to Visitor (optional)
                    </label>
                    <textarea
                      id="transfer_message"
                      v-model="form.transferMessage"
                      rows="2"
                      class="mt-1 form-textarea"
                      placeholder="I'm transferring you to another agent who can better assist you..."
                    ></textarea>
                  </div>

                  <!-- Internal note -->
                  <div>
                    <label for="internal_note" class="block text-sm font-medium text-gray-700">
                      Internal Note (for receiving agent)
                    </label>
                    <textarea
                      id="internal_note"
                      v-model="form.internalNote"
                      rows="2"
                      class="mt-1 form-textarea"
                      placeholder="Context or notes for the receiving agent..."
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="submit"
              :disabled="isLoading || !form.targetAgentId"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="isLoading" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Transferring...
              </span>
              <span v-else>Transfer Chat</span>
            </button>
            <button
              @click="$emit('close')"
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ArrowRightIcon } from '@heroicons/vue/24/outline'
import axios from 'axios'

export default {
  name: 'TransferChatModal',
  components: {
    ArrowRightIcon
  },
  props: {
    session: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'transfer'],
  setup(props, { emit }) {
    const isLoading = ref(false)
    const availableAgents = ref([])
    const errors = reactive({})

    const form = reactive({
      targetAgentId: '',
      reason: '',
      customReason: '',
      transferMessage: '',
      internalNote: ''
    })

    const fetchAvailableAgents = async () => {
      try {
        const response = await axios.get('/admin/agents/available')
        if (response.data.success) {
          availableAgents.value = response.data.data
        }
      } catch (error) {
        console.error('Failed to fetch available agents:', error)
      }
    }

    const validateForm = () => {
      Object.keys(errors).forEach(key => delete errors[key])

      if (!form.targetAgentId) {
        errors.targetAgentId = 'Please select an agent to transfer to'
      }

      return Object.keys(errors).length === 0
    }

    const transferChat = async () => {
      if (!validateForm()) {
        return
      }

      try {
        isLoading.value = true

        const transferData = {
          sessionId: props.session.session_id,
          targetAgentId: form.targetAgentId,
          reason: form.reason === 'other' ? form.customReason : form.reason,
          transferMessage: form.transferMessage,
          internalNote: form.internalNote
        }

        emit('transfer', transferData)
      } catch (error) {
        console.error('Transfer failed:', error)
      } finally {
        isLoading.value = false
      }
    }

    onMounted(() => {
      fetchAvailableAgents()
    })

    return {
      isLoading,
      availableAgents,
      errors,
      form,
      transferChat
    }
  }
}
</script>

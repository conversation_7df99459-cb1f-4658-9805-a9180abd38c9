<?php

namespace App\Services;

use App\Models\Site;
use App\Models\User;
use App\Models\WorkingHour;
use App\Models\ContactMessage;
use App\Models\Visitor;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;

class WorkModeService
{
    protected $webSocketService;

    public function __construct(WebSocketService $webSocketService)
    {
        $this->webSocketService = $webSocketService;
    }

    /**
     * Check if site is currently in working hours
     */
    public function isSiteOnline(Site $site)
    {
        $cacheKey = "site_online_status_{$site->id}";
        
        return Cache::remember($cacheKey, 60, function () use ($site) {
            $now = now()->setTimezone($site->timezone ?: config('app.timezone'));
            $dayOfWeek = strtolower($now->format('l'));
            
            $workingHour = WorkingHour::where('site_id', $site->id)
                ->where('day_of_week', $dayOfWeek)
                ->first();
            
            if (!$workingHour) {
                return false;
            }
            
            return $workingHour->isWorkingAt($now);
        });
    }

    /**
     * Get site working status with details
     */
    public function getSiteWorkingStatus(Site $site)
    {
        $isOnline = $this->isSiteOnline($site);
        $availableAgents = $this->getAvailableAgents($site);
        $nextWorkingTime = $this->getNextWorkingTime($site);
        
        return [
            'is_online' => $isOnline,
            'available_agents' => $availableAgents->count(),
            'agents' => $availableAgents->map(function ($agent) {
                return [
                    'id' => $agent->id,
                    'name' => $agent->name,
                    'status' => $agent->status,
                    'active_chats' => $agent->getActiveChatCount(),
                ];
            }),
            'next_working_time' => $nextWorkingTime ? $nextWorkingTime->toISOString() : null,
            'working_hours' => WorkingHour::getFormattedSchedule($site->id),
        ];
    }

    /**
     * Get available agents for site
     */
    public function getAvailableAgents(Site $site)
    {
        if (!$this->isSiteOnline($site)) {
            return collect();
        }

        return $site->users()
            ->where('status', 'online')
            ->where('role', 'agent')
            ->get()
            ->filter(function ($agent) use ($site) {
                return $agent->canHandleMoreChats($site->id);
            });
    }

    /**
     * Get next working time for site
     */
    public function getNextWorkingTime(Site $site)
    {
        $now = now()->setTimezone($site->timezone ?: config('app.timezone'));
        
        // Check next 7 days
        for ($i = 0; $i < 7; $i++) {
            $checkDate = $now->copy()->addDays($i);
            $dayOfWeek = strtolower($checkDate->format('l'));
            
            $workingHour = WorkingHour::where('site_id', $site->id)
                ->where('day_of_week', $dayOfWeek)
                ->where('is_working_day', true)
                ->first();
            
            if (!$workingHour) {
                continue;
            }

            // If it's today and we're still within working hours
            if ($i === 0 && $workingHour->isWorkingAt($checkDate)) {
                return null; // Already working
            }

            // Get start time for this day
            $specialHours = $workingHour->getSpecialHoursForDate($checkDate);
            $startTime = $specialHours ? $specialHours['start'] : $workingHour->start_time;
            
            if ($workingHour->is_24_hours) {
                $startTime = '00:00:00';
            }

            $workingStart = $checkDate->copy()->setTimeFromTimeString($startTime);
            
            // If it's today and the working time hasn't started yet
            if ($i === 0 && $workingStart->gt($now)) {
                return $workingStart;
            }
            
            // If it's a future day
            if ($i > 0) {
                return $workingStart;
            }
        }

        return null;
    }

    /**
     * Auto-update agent status based on working hours
     */
    public function updateAgentStatusByWorkingHours()
    {
        $sites = Site::with('users')->get();
        
        foreach ($sites as $site) {
            $isWorkingTime = $this->isSiteOnline($site);
            
            foreach ($site->users as $user) {
                if ($user->role !== 'agent') {
                    continue;
                }
                
                $autoMode = $user->getSetting('auto_status_mode', false);
                if (!$autoMode) {
                    continue;
                }
                
                $newStatus = $isWorkingTime ? 'online' : 'offline';
                
                if ($user->status !== $newStatus) {
                    $user->update(['status' => $newStatus]);
                    
                    // Notify about status change
                    $this->webSocketService->notifyAgentStatusChange($user, $newStatus);
                    
                    Log::info('Agent status auto-updated', [
                        'user_id' => $user->id,
                        'site_id' => $site->id,
                        'old_status' => $user->status,
                        'new_status' => $newStatus,
                        'is_working_time' => $isWorkingTime,
                    ]);
                }
            }
        }
    }

    /**
     * Create contact message when site is offline
     */
    public function createContactMessage(Site $site, Visitor $visitor, array $data)
    {
        try {
            $contactMessage = ContactMessage::create([
                'site_id' => $site->id,
                'visitor_id' => $visitor->id,
                'name' => $data['name'] ?? null,
                'email' => $data['email'] ?? null,
                'phone' => $data['phone'] ?? null,
                'subject' => $data['subject'] ?? 'Contact Request',
                'message' => $data['message'],
                'category' => $data['category'] ?? 'general',
                'priority' => $this->determinePriority($data),
                'source' => 'offline_form',
                'ip_address' => $data['ip_address'] ?? null,
                'user_agent' => $data['user_agent'] ?? null,
                'page_url' => $data['page_url'] ?? null,
                'referrer' => $data['referrer'] ?? null,
                'metadata' => $data['metadata'] ?? null,
            ]);

            // Auto-assign if enabled
            $this->autoAssignContactMessage($contactMessage);

            // Send notifications
            $this->sendContactMessageNotifications($contactMessage);

            Log::info('Contact message created', [
                'message_id' => $contactMessage->message_id,
                'site_id' => $site->id,
                'visitor_id' => $visitor->visitor_id,
            ]);

            return $contactMessage;

        } catch (\Exception $e) {
            Log::error('Failed to create contact message', [
                'site_id' => $site->id,
                'visitor_id' => $visitor->visitor_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Auto-assign contact message to available agent
     */
    public function autoAssignContactMessage(ContactMessage $contactMessage)
    {
        $site = $contactMessage->site;
        
        if (!$site->getSetting('auto_assign_contact_messages', true)) {
            return;
        }

        // Get available agents
        $availableAgents = $site->users()
            ->where('role', 'agent')
            ->where('status', 'online')
            ->get();

        if ($availableAgents->isEmpty()) {
            return;
        }

        // Simple round-robin assignment
        $agent = $availableAgents->first();
        
        $contactMessage->assignTo($agent);

        // Notify agent
        $this->webSocketService->notifyNewContactMessage($contactMessage, $agent);
    }

    /**
     * Send contact message notifications
     */
    public function sendContactMessageNotifications(ContactMessage $contactMessage)
    {
        $site = $contactMessage->site;
        
        // Email notification to site administrators
        $adminEmails = $site->getSetting('admin_emails', []);
        
        if (!empty($adminEmails)) {
            try {
                // Send email notification (implement mail class)
                Log::info('Contact message email notification sent', [
                    'message_id' => $contactMessage->message_id,
                    'recipients' => $adminEmails,
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to send contact message email', [
                    'message_id' => $contactMessage->message_id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // WebSocket notification to online agents
        $onlineAgents = $site->users()
            ->where('role', 'agent')
            ->where('status', 'online')
            ->get();

        foreach ($onlineAgents as $agent) {
            $this->webSocketService->notifyNewContactMessage($contactMessage, $agent);
        }
    }

    /**
     * Determine message priority based on content and keywords
     */
    protected function determinePriority(array $data)
    {
        $message = strtolower($data['message'] ?? '');
        $subject = strtolower($data['subject'] ?? '');
        
        $urgentKeywords = ['urgent', 'emergency', 'asap', 'immediately', 'critical', 'help'];
        $highKeywords = ['important', 'priority', 'soon', 'problem', 'issue', 'bug'];
        
        foreach ($urgentKeywords as $keyword) {
            if (strpos($message, $keyword) !== false || strpos($subject, $keyword) !== false) {
                return ContactMessage::PRIORITY_URGENT;
            }
        }
        
        foreach ($highKeywords as $keyword) {
            if (strpos($message, $keyword) !== false || strpos($subject, $keyword) !== false) {
                return ContactMessage::PRIORITY_HIGH;
            }
        }
        
        return ContactMessage::PRIORITY_NORMAL;
    }

    /**
     * Get offline message template
     */
    public function getOfflineMessageTemplate(Site $site)
    {
        $nextWorkingTime = $this->getNextWorkingTime($site);
        $workingHours = WorkingHour::getFormattedSchedule($site->id);
        
        $template = $site->getSetting('offline_message_template', 
            'We are currently offline. Please leave your message and we will get back to you as soon as possible.'
        );
        
        return [
            'message' => $template,
            'next_working_time' => $nextWorkingTime ? $nextWorkingTime->toISOString() : null,
            'working_hours' => $workingHours,
            'show_contact_form' => $site->getSetting('show_offline_contact_form', true),
            'required_fields' => $site->getSetting('offline_form_required_fields', ['name', 'email', 'message']),
        ];
    }

    /**
     * Clear site online status cache
     */
    public function clearSiteStatusCache(Site $site)
    {
        Cache::forget("site_online_status_{$site->id}");
    }

    /**
     * Get work mode statistics
     */
    public function getWorkModeStatistics(Site $site, $period = '7d')
    {
        $startDate = match($period) {
            '24h' => now()->subDay(),
            '7d' => now()->subWeek(),
            '30d' => now()->subMonth(),
            default => now()->subWeek(),
        };

        return [
            'online_time_percentage' => $this->calculateOnlineTimePercentage($site, $startDate),
            'total_contact_messages' => ContactMessage::where('site_id', $site->id)
                ->where('created_at', '>=', $startDate)
                ->count(),
            'responded_messages' => ContactMessage::where('site_id', $site->id)
                ->where('created_at', '>=', $startDate)
                ->whereNotNull('responded_at')
                ->count(),
            'average_response_time' => $this->calculateAverageResponseTime($site, $startDate),
            'missed_chats' => $this->calculateMissedChats($site, $startDate),
        ];
    }

    /**
     * Calculate online time percentage
     */
    protected function calculateOnlineTimePercentage(Site $site, Carbon $startDate)
    {
        // This would require tracking online/offline events
        // For now, return estimated based on working hours
        $totalHours = now()->diffInHours($startDate);
        $workingHours = $this->calculateWorkingHours($site, $startDate);
        
        return $totalHours > 0 ? round(($workingHours / $totalHours) * 100, 2) : 0;
    }

    /**
     * Calculate working hours in period
     */
    protected function calculateWorkingHours(Site $site, Carbon $startDate)
    {
        $workingHours = 0;
        $current = $startDate->copy();
        
        while ($current->lt(now())) {
            $dayOfWeek = strtolower($current->format('l'));
            $workingHour = WorkingHour::where('site_id', $site->id)
                ->where('day_of_week', $dayOfWeek)
                ->where('is_working_day', true)
                ->first();
            
            if ($workingHour && !$workingHour->is_24_hours) {
                $start = $current->copy()->setTimeFromTimeString($workingHour->start_time);
                $end = $current->copy()->setTimeFromTimeString($workingHour->end_time);
                
                if ($end->lt($start)) {
                    $end->addDay();
                }
                
                $workingHours += $end->diffInHours($start);
            } elseif ($workingHour && $workingHour->is_24_hours) {
                $workingHours += 24;
            }
            
            $current->addDay();
        }
        
        return $workingHours;
    }

    /**
     * Calculate average response time for contact messages
     */
    protected function calculateAverageResponseTime(Site $site, Carbon $startDate)
    {
        $avgSeconds = ContactMessage::where('site_id', $site->id)
            ->where('created_at', '>=', $startDate)
            ->whereNotNull('response_time')
            ->avg('response_time');
        
        if (!$avgSeconds) {
            return null;
        }
        
        $hours = floor($avgSeconds / 3600);
        $minutes = floor(($avgSeconds % 3600) / 60);
        
        return $hours > 0 ? "{$hours}h {$minutes}m" : "{$minutes}m";
    }

    /**
     * Calculate missed chats (sessions that ended without agent assignment)
     */
    protected function calculateMissedChats(Site $site, Carbon $startDate)
    {
        return $site->chatSessions()
            ->where('created_at', '>=', $startDate)
            ->where('status', 'timeout')
            ->whereNull('user_id')
            ->count();
    }
}

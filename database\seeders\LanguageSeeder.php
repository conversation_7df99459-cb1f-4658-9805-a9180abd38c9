<?php

namespace Database\Seeders;

use App\Models\Language;
use App\Models\Translation;
use App\Models\Site;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class LanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all sites
        $sites = Site::all();

        foreach ($sites as $site) {
            // Create default English language for each site
            $this->createDefaultLanguage($site->id);
            
            // Optionally create additional common languages
            $this->createCommonLanguages($site->id);
        }
    }

    /**
     * Create default English language
     */
    protected function createDefaultLanguage($siteId)
    {
        $language = Language::create([
            'language_id' => (string) Str::uuid(),
            'site_id' => $siteId,
            'code' => 'en',
            'name' => 'English',
            'native_name' => 'English',
            'flag_icon' => '🇺🇸',
            'is_active' => true,
            'is_default' => true,
            'is_rtl' => false,
            'date_format' => 'Y-m-d',
            'time_format' => 'H:i:s',
            'currency_code' => 'USD',
            'currency_symbol' => '$',
            'number_format' => '1,234.56',
            'timezone' => 'UTC',
        ]);

        // Create default system translations
        $this->createSystemTranslations($language);
    }

    /**
     * Create common languages (optional)
     */
    protected function createCommonLanguages($siteId)
    {
        $commonLanguages = [
            'zh' => [
                'name' => 'Chinese (Simplified)',
                'native_name' => '简体中文',
                'flag_icon' => '🇨🇳',
                'is_rtl' => false,
                'date_format' => 'Y年m月d日',
                'time_format' => 'H:i:s',
                'currency_code' => 'CNY',
                'currency_symbol' => '¥',
                'number_format' => '1,234.56',
                'timezone' => 'Asia/Shanghai',
            ],
            'es' => [
                'name' => 'Spanish',
                'native_name' => 'Español',
                'flag_icon' => '🇪🇸',
                'is_rtl' => false,
                'date_format' => 'd/m/Y',
                'time_format' => 'H:i:s',
                'currency_code' => 'EUR',
                'currency_symbol' => '€',
                'number_format' => '1.234,56',
                'timezone' => 'Europe/Madrid',
            ],
            'fr' => [
                'name' => 'French',
                'native_name' => 'Français',
                'flag_icon' => '🇫🇷',
                'is_rtl' => false,
                'date_format' => 'd/m/Y',
                'time_format' => 'H:i:s',
                'currency_code' => 'EUR',
                'currency_symbol' => '€',
                'number_format' => '1 234,56',
                'timezone' => 'Europe/Paris',
            ],
        ];

        foreach ($commonLanguages as $code => $data) {
            $language = Language::create([
                'language_id' => (string) Str::uuid(),
                'site_id' => $siteId,
                'code' => $code,
                'name' => $data['name'],
                'native_name' => $data['native_name'],
                'flag_icon' => $data['flag_icon'],
                'is_active' => false, // Inactive by default
                'is_default' => false,
                'is_rtl' => $data['is_rtl'],
                'date_format' => $data['date_format'],
                'time_format' => $data['time_format'],
                'currency_code' => $data['currency_code'],
                'currency_symbol' => $data['currency_symbol'],
                'number_format' => $data['number_format'],
                'timezone' => $data['timezone'],
            ]);

            // Create basic system translations (you might want to translate these)
            $this->createSystemTranslations($language);
        }
    }

    /**
     * Create system translations for a language
     */
    protected function createSystemTranslations(Language $language)
    {
        $defaultTranslations = Translation::getDefaultSystemTranslations();
        
        foreach ($defaultTranslations as $group => $translations) {
            foreach ($translations as $key => $value) {
                Translation::create([
                    'translation_id' => (string) Str::uuid(),
                    'language_id' => $language->id,
                    'namespace' => null,
                    'group' => $group,
                    'key' => $key,
                    'value' => $this->translateValue($value, $language->code),
                    'is_system' => true,
                    'is_approved' => true,
                ]);
            }
        }
    }

    /**
     * Basic translation for common languages (placeholder)
     */
    protected function translateValue($value, $languageCode)
    {
        // This is a very basic translation mapping
        // In a real application, you would use a proper translation service
        
        if ($languageCode === 'en') {
            return $value;
        }

        $basicTranslations = [
            'zh' => [
                'Hello! How can I help you today?' => '您好！今天我能为您做些什么？',
                'Send message' => '发送消息',
                'Type your message...' => '输入您的消息...',
                'Chat session ended' => '聊天会话已结束',
                'Loading...' => '加载中...',
                'Save' => '保存',
                'Cancel' => '取消',
                'Yes' => '是',
                'No' => '否',
                'OK' => '确定',
                'Close' => '关闭',
                'Back' => '返回',
                'Next' => '下一步',
                'Submit' => '提交',
                'Search' => '搜索',
                'Today' => '今天',
                'Yesterday' => '昨天',
            ],
            'es' => [
                'Hello! How can I help you today?' => '¡Hola! ¿Cómo puedo ayudarte hoy?',
                'Send message' => 'Enviar mensaje',
                'Type your message...' => 'Escribe tu mensaje...',
                'Chat session ended' => 'Sesión de chat terminada',
                'Loading...' => 'Cargando...',
                'Save' => 'Guardar',
                'Cancel' => 'Cancelar',
                'Yes' => 'Sí',
                'No' => 'No',
                'OK' => 'OK',
                'Close' => 'Cerrar',
                'Back' => 'Atrás',
                'Next' => 'Siguiente',
                'Submit' => 'Enviar',
                'Search' => 'Buscar',
                'Today' => 'Hoy',
                'Yesterday' => 'Ayer',
            ],
            'fr' => [
                'Hello! How can I help you today?' => 'Bonjour ! Comment puis-je vous aider aujourd\'hui ?',
                'Send message' => 'Envoyer le message',
                'Type your message...' => 'Tapez votre message...',
                'Chat session ended' => 'Session de chat terminée',
                'Loading...' => 'Chargement...',
                'Save' => 'Enregistrer',
                'Cancel' => 'Annuler',
                'Yes' => 'Oui',
                'No' => 'Non',
                'OK' => 'OK',
                'Close' => 'Fermer',
                'Back' => 'Retour',
                'Next' => 'Suivant',
                'Submit' => 'Soumettre',
                'Search' => 'Rechercher',
                'Today' => 'Aujourd\'hui',
                'Yesterday' => 'Hier',
            ],
        ];

        if (isset($basicTranslations[$languageCode][$value])) {
            return $basicTranslations[$languageCode][$value];
        }

        return $value; // Return original if no translation found
    }
}

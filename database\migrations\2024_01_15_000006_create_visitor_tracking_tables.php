<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Visitor tracking table
        Schema::create('st_visitor_trackings', function (Blueprint $table) {
            $table->id();
            $table->uuid('tracking_id')->unique();
            $table->unsignedBigInteger('site_id');
            $table->unsignedBigInteger('visitor_id');
            $table->string('session_id');
            $table->text('page_url');
            $table->string('page_title')->nullable();
            $table->text('referrer')->nullable();
            $table->string('utm_source')->nullable();
            $table->string('utm_medium')->nullable();
            $table->string('utm_campaign')->nullable();
            $table->string('utm_term')->nullable();
            $table->text('utm_content')->nullable();
            $table->integer('visit_duration')->default(0); // in seconds
            $table->integer('page_views')->default(0);
            $table->integer('scroll_depth')->default(0); // percentage 0-100
            $table->integer('clicks')->default(0);
            $table->integer('form_interactions')->default(0);
            $table->text('exit_page')->nullable();
            $table->boolean('bounce')->default(false);
            $table->json('device_info')->nullable();
            $table->json('browser_info')->nullable();
            $table->json('screen_resolution')->nullable();
            $table->json('viewport_size')->nullable();
            $table->text('user_agent')->nullable();
            $table->string('ip_address', 45)->nullable();
            $table->string('country')->nullable();
            $table->string('city')->nullable();
            $table->string('timezone')->nullable();
            $table->string('language', 10)->nullable();
            $table->timestamp('visited_at')->nullable();
            $table->timestamp('left_at')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('site_id')->references('id')->on('st_sites')->onDelete('cascade');
            $table->foreign('visitor_id')->references('id')->on('st_visitors')->onDelete('cascade');
            
            $table->index(['site_id', 'visitor_id']);
            $table->index(['site_id', 'session_id']);
            $table->index(['site_id', 'visited_at']);
            $table->index(['page_url']);
            $table->index(['country']);
            $table->index(['bounce']);
        });

        // Mouse tracking table
        Schema::create('st_mouse_trackings', function (Blueprint $table) {
            $table->id();
            $table->uuid('tracking_id')->unique();
            $table->unsignedBigInteger('site_id');
            $table->unsignedBigInteger('visitor_id');
            $table->unsignedBigInteger('visitor_tracking_id')->nullable();
            $table->string('session_id');
            $table->text('page_url');
            $table->longText('mouse_data')->nullable(); // JSON array of mouse movements
            $table->longText('heatmap_data')->nullable(); // JSON grid data for heatmap
            $table->json('scroll_data')->nullable(); // JSON array of scroll events
            $table->json('viewport_size')->nullable();
            $table->timestamp('recorded_at')->nullable();
            $table->integer('duration')->default(0); // in milliseconds
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->foreign('site_id')->references('id')->on('st_sites')->onDelete('cascade');
            $table->foreign('visitor_id')->references('id')->on('st_visitors')->onDelete('cascade');
            $table->foreign('visitor_tracking_id')->references('id')->on('st_visitor_trackings')->onDelete('cascade');
            
            $table->index(['site_id', 'visitor_id']);
            $table->index(['site_id', 'session_id']);
            $table->index(['site_id', 'page_url']);
            $table->index(['recorded_at']);
        });

        // Click events table
        Schema::create('st_click_events', function (Blueprint $table) {
            $table->id();
            $table->uuid('event_id')->unique();
            $table->unsignedBigInteger('site_id');
            $table->unsignedBigInteger('visitor_id');
            $table->unsignedBigInteger('visitor_tracking_id')->nullable();
            $table->string('session_id');
            $table->text('page_url');
            $table->string('element_type')->nullable();
            $table->string('element_id')->nullable();
            $table->text('element_class')->nullable();
            $table->text('element_text')->nullable();
            $table->text('element_selector')->nullable();
            $table->integer('click_x');
            $table->integer('click_y');
            $table->integer('viewport_x')->nullable();
            $table->integer('viewport_y')->nullable();
            $table->tinyInteger('button')->default(0); // 0=left, 1=middle, 2=right
            $table->json('modifier_keys')->nullable(); // ctrl, alt, shift, meta
            $table->bigInteger('timestamp'); // JavaScript timestamp
            $table->bigInteger('page_load_time')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->foreign('site_id')->references('id')->on('st_sites')->onDelete('cascade');
            $table->foreign('visitor_id')->references('id')->on('st_visitors')->onDelete('cascade');
            $table->foreign('visitor_tracking_id')->references('id')->on('st_visitor_trackings')->onDelete('cascade');
            
            $table->index(['site_id', 'visitor_id']);
            $table->index(['site_id', 'session_id']);
            $table->index(['site_id', 'page_url']);
            $table->index(['element_type']);
            $table->index(['created_at']);
        });

        // Form events table
        Schema::create('st_form_events', function (Blueprint $table) {
            $table->id();
            $table->uuid('event_id')->unique();
            $table->unsignedBigInteger('site_id');
            $table->unsignedBigInteger('visitor_id');
            $table->unsignedBigInteger('visitor_tracking_id')->nullable();
            $table->string('session_id');
            $table->text('page_url');
            $table->string('form_id')->nullable();
            $table->string('form_name')->nullable();
            $table->text('form_action')->nullable();
            $table->string('form_method')->nullable();
            $table->string('field_name');
            $table->string('field_type');
            $table->string('field_label')->nullable();
            $table->enum('event_type', ['focus', 'blur', 'change', 'submit']);
            $table->text('field_value')->nullable();
            $table->text('previous_value')->nullable();
            $table->json('validation_errors')->nullable();
            $table->bigInteger('timestamp'); // JavaScript timestamp
            $table->integer('time_spent')->default(0); // in milliseconds
            $table->integer('keystroke_count')->default(0);
            $table->integer('focus_count')->default(0);
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->foreign('site_id')->references('id')->on('st_sites')->onDelete('cascade');
            $table->foreign('visitor_id')->references('id')->on('st_visitors')->onDelete('cascade');
            $table->foreign('visitor_tracking_id')->references('id')->on('st_visitor_trackings')->onDelete('cascade');
            
            $table->index(['site_id', 'visitor_id']);
            $table->index(['site_id', 'session_id']);
            $table->index(['site_id', 'page_url']);
            $table->index(['form_id']);
            $table->index(['field_name']);
            $table->index(['event_type']);
            $table->index(['created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('st_form_events');
        Schema::dropIfExists('st_click_events');
        Schema::dropIfExists('st_mouse_trackings');
        Schema::dropIfExists('st_visitor_trackings');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChatSessionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('chat_sessions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('site_id')->constrained()->onDelete('cascade');
            $table->foreignId('visitor_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // Assigned agent
            $table->string('session_id')->unique(); // UUID for session
            
            // Session Status
            $table->enum('status', ['waiting', 'active', 'transferred', 'closed', 'abandoned'])->default('waiting');
            $table->enum('end_reason', ['visitor_left', 'agent_closed', 'timeout', 'transferred'])->nullable();
            
            // Timing
            $table->timestamp('started_at');
            $table->timestamp('first_response_at')->nullable(); // When agent first responded
            $table->timestamp('ended_at')->nullable();
            $table->integer('wait_time')->nullable(); // Time before agent responded (seconds)
            $table->integer('duration')->nullable(); // Total session duration (seconds)
            
            // Session Info
            $table->text('initial_message')->nullable();
            $table->string('initial_page_url')->nullable();
            $table->string('initial_page_title')->nullable();
            $table->integer('message_count')->default(0);
            $table->integer('visitor_message_count')->default(0);
            $table->integer('agent_message_count')->default(0);
            
            // Quality & Feedback
            $table->integer('satisfaction_rating')->nullable(); // 1-5 scale
            $table->text('satisfaction_comment')->nullable();
            $table->json('tags')->nullable(); // For categorization
            
            // Transfer Info
            $table->foreignId('transferred_from_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('transferred_to_user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->text('transfer_reason')->nullable();
            
            $table->timestamps();
            
            $table->index(['site_id', 'status']);
            $table->index(['site_id', 'started_at']);
            $table->index(['user_id', 'status']);
            $table->index(['visitor_id', 'started_at']);
            $table->index('session_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('chat_sessions');
    }
}

<?php

namespace Tests\Unit\Services;

use Tests\TestCase;
use App\Services\ChatService;
use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\User;
use App\Models\Visitor;
use App\Models\Site;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use App\Events\MessageSent;

class ChatServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $chatService;
    protected $user;
    protected $visitor;
    protected $site;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->chatService = new ChatService();
        
        // Create test data
        $this->site = Site::factory()->create();
        $this->user = User::factory()->create(['site_id' => $this->site->id]);
        $this->visitor = Visitor::factory()->create(['site_id' => $this->site->id]);
    }

    public function test_can_start_chat_session()
    {
        $sessionData = [
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'visitor_name' => 'Test Visitor',
            'visitor_email' => '<EMAIL>'
        ];

        $session = $this->chatService->startSession($sessionData);

        $this->assertInstanceOf(ChatSession::class, $session);
        $this->assertEquals('waiting', $session->status);
        $this->assertEquals($this->visitor->id, $session->visitor_id);
        $this->assertEquals($this->site->id, $session->site_id);
    }

    public function test_can_assign_agent_to_session()
    {
        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'status' => 'waiting'
        ]);

        $result = $this->chatService->assignAgent($session->id, $this->user->id);

        $this->assertTrue($result);
        $session->refresh();
        $this->assertEquals('active', $session->status);
        $this->assertEquals($this->user->id, $session->agent_id);
    }

    public function test_can_send_message()
    {
        Event::fake();

        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'agent_id' => $this->user->id,
            'status' => 'active'
        ]);

        $messageData = [
            'session_id' => $session->id,
            'sender_type' => 'agent',
            'sender_id' => $this->user->id,
            'message' => 'Hello, how can I help you?',
            'message_type' => 'text'
        ];

        $message = $this->chatService->sendMessage($messageData);

        $this->assertInstanceOf(ChatMessage::class, $message);
        $this->assertEquals('Hello, how can I help you?', $message->message);
        $this->assertEquals('agent', $message->sender_type);

        Event::assertDispatched(MessageSent::class);
    }

    public function test_can_end_session()
    {
        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'agent_id' => $this->user->id,
            'status' => 'active'
        ]);

        $result = $this->chatService->endSession($session->id, $this->user->id);

        $this->assertTrue($result);
        $session->refresh();
        $this->assertEquals('ended', $session->status);
        $this->assertNotNull($session->ended_at);
    }

    public function test_can_get_session_messages()
    {
        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id
        ]);

        // Create some messages
        ChatMessage::factory()->count(5)->create([
            'session_id' => $session->id
        ]);

        $messages = $this->chatService->getSessionMessages($session->id);

        $this->assertCount(5, $messages);
    }

    public function test_can_get_active_sessions_for_agent()
    {
        // Create active sessions for the agent
        ChatSession::factory()->count(3)->create([
            'agent_id' => $this->user->id,
            'status' => 'active',
            'site_id' => $this->site->id
        ]);

        // Create inactive session
        ChatSession::factory()->create([
            'agent_id' => $this->user->id,
            'status' => 'ended',
            'site_id' => $this->site->id
        ]);

        $activeSessions = $this->chatService->getActiveSessionsForAgent($this->user->id);

        $this->assertCount(3, $activeSessions);
    }

    public function test_can_transfer_session()
    {
        $newAgent = User::factory()->create(['site_id' => $this->site->id]);
        
        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'agent_id' => $this->user->id,
            'status' => 'active'
        ]);

        $result = $this->chatService->transferSession($session->id, $newAgent->id, 'Transfer reason');

        $this->assertTrue($result);
        $session->refresh();
        $this->assertEquals($newAgent->id, $session->agent_id);
    }

    public function test_can_calculate_queue_position()
    {
        // Create waiting sessions
        ChatSession::factory()->count(3)->create([
            'site_id' => $this->site->id,
            'status' => 'waiting',
            'started_at' => now()->subMinutes(10)
        ]);

        $newSession = ChatSession::factory()->create([
            'site_id' => $this->site->id,
            'status' => 'waiting',
            'started_at' => now()
        ]);

        $position = $this->chatService->getQueuePosition($newSession->id);

        $this->assertEquals(4, $position);
    }

    public function test_can_estimate_wait_time()
    {
        // Create some historical data
        ChatSession::factory()->count(5)->create([
            'site_id' => $this->site->id,
            'status' => 'ended',
            'started_at' => now()->subHour(),
            'ended_at' => now()->subMinutes(30)
        ]);

        $waitTime = $this->chatService->getEstimatedWaitTime($this->site->id);

        $this->assertIsInt($waitTime);
        $this->assertGreaterThanOrEqual(0, $waitTime);
    }

    public function test_can_cleanup_old_sessions()
    {
        // Create old sessions
        ChatSession::factory()->count(3)->create([
            'site_id' => $this->site->id,
            'status' => 'ended',
            'ended_at' => now()->subDays(40)
        ]);

        // Create recent sessions
        ChatSession::factory()->count(2)->create([
            'site_id' => $this->site->id,
            'status' => 'ended',
            'ended_at' => now()->subDays(10)
        ]);

        $cleaned = $this->chatService->cleanupOldSessions(30);

        $this->assertEquals(3, $cleaned);
        $this->assertEquals(2, ChatSession::count());
    }

    public function test_can_get_session_statistics()
    {
        // Create test sessions with different statuses
        ChatSession::factory()->create([
            'site_id' => $this->site->id,
            'status' => 'active'
        ]);

        ChatSession::factory()->count(2)->create([
            'site_id' => $this->site->id,
            'status' => 'waiting'
        ]);

        ChatSession::factory()->count(3)->create([
            'site_id' => $this->site->id,
            'status' => 'ended'
        ]);

        $stats = $this->chatService->getSessionStatistics($this->site->id);

        $this->assertEquals(1, $stats['active']);
        $this->assertEquals(2, $stats['waiting']);
        $this->assertEquals(3, $stats['ended']);
        $this->assertEquals(6, $stats['total']);
    }

    public function test_cannot_assign_agent_to_nonexistent_session()
    {
        $result = $this->chatService->assignAgent(999, $this->user->id);

        $this->assertFalse($result);
    }

    public function test_cannot_send_message_to_ended_session()
    {
        $session = ChatSession::factory()->create([
            'visitor_id' => $this->visitor->id,
            'site_id' => $this->site->id,
            'status' => 'ended'
        ]);

        $messageData = [
            'session_id' => $session->id,
            'sender_type' => 'agent',
            'sender_id' => $this->user->id,
            'message' => 'This should fail',
            'message_type' => 'text'
        ];

        $message = $this->chatService->sendMessage($messageData);

        $this->assertNull($message);
    }
}

<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class SecurityScanService
{
    /**
     * Perform comprehensive security scan
     */
    public function performSecurityScan(): array
    {
        $results = [];

        try {
            $results['user_security'] = $this->scanUserSecurity();
            $results['file_permissions'] = $this->scanFilePermissions();
            $results['database_security'] = $this->scanDatabaseSecurity();
            $results['configuration_security'] = $this->scanConfigurationSecurity();
            $results['vulnerability_scan'] = $this->scanVulnerabilities();
            $results['access_logs'] = $this->analyzeAccessLogs();

            // Calculate overall security score
            $results['security_score'] = $this->calculateSecurityScore($results);

            Log::info('Security scan completed', [
                'security_score' => $results['security_score'],
                'issues_found' => $this->countIssues($results)
            ]);

        } catch (\Exception $e) {
            Log::error('Security scan failed: ' . $e->getMessage());
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Scan user security issues
     */
    protected function scanUserSecurity(): array
    {
        $issues = [];
        $recommendations = [];

        try {
            // Check for weak passwords
            $weakPasswords = DB::table('users')
                ->where('password_changed_at', '<', Carbon::now()->subMonths(6))
                ->orWhereNull('password_changed_at')
                ->count();

            if ($weakPasswords > 0) {
                $issues[] = "{$weakPasswords} users have not changed passwords in 6+ months";
                $recommendations[] = "Implement password expiration policy";
            }

            // Check for inactive admin accounts
            $inactiveAdmins = DB::table('users')
                ->where('role', 'admin')
                ->where('last_login_at', '<', Carbon::now()->subMonths(3))
                ->count();

            if ($inactiveAdmins > 0) {
                $issues[] = "{$inactiveAdmins} admin accounts inactive for 3+ months";
                $recommendations[] = "Review and disable inactive admin accounts";
            }

            // Check for users with multiple failed login attempts
            $suspiciousUsers = DB::table('users')
                ->where('failed_login_attempts', '>', 5)
                ->where('locked_until', '>', Carbon::now())
                ->count();

            if ($suspiciousUsers > 0) {
                $issues[] = "{$suspiciousUsers} users currently locked due to failed attempts";
            }

            // Check for default passwords (if any test accounts exist)
            $defaultPasswords = DB::table('users')
                ->where('email', 'like', '%test%')
                ->orWhere('email', 'like', '%demo%')
                ->orWhere('email', 'like', '%admin@%')
                ->count();

            if ($defaultPasswords > 0) {
                $issues[] = "{$defaultPasswords} potential test/demo accounts found";
                $recommendations[] = "Remove or secure test accounts";
            }

        } catch (\Exception $e) {
            $issues[] = 'Error scanning user security: ' . $e->getMessage();
        }

        return [
            'issues' => $issues,
            'recommendations' => $recommendations,
            'severity' => count($issues) > 3 ? 'high' : (count($issues) > 1 ? 'medium' : 'low')
        ];
    }

    /**
     * Scan file permissions and security
     */
    protected function scanFilePermissions(): array
    {
        $issues = [];
        $recommendations = [];

        try {
            // Check storage directory permissions
            $storagePath = storage_path();
            $permissions = substr(sprintf('%o', fileperms($storagePath)), -4);
            
            if ($permissions !== '0755' && $permissions !== '0750') {
                $issues[] = "Storage directory has permissions {$permissions} (should be 755 or 750)";
                $recommendations[] = "Set proper storage directory permissions";
            }

            // Check for publicly accessible sensitive files
            $sensitiveFiles = ['.env', 'composer.json', 'artisan'];
            foreach ($sensitiveFiles as $file) {
                if (file_exists(public_path($file))) {
                    $issues[] = "Sensitive file {$file} is publicly accessible";
                    $recommendations[] = "Move sensitive files outside public directory";
                }
            }

            // Check upload directory security
            $uploadDirs = ['uploads', 'chat_files', 'contact_attachments'];
            foreach ($uploadDirs as $dir) {
                $dirPath = storage_path("app/public/{$dir}");
                if (is_dir($dirPath)) {
                    $htaccessPath = $dirPath . '/.htaccess';
                    if (!file_exists($htaccessPath)) {
                        $issues[] = "Upload directory {$dir} missing .htaccess protection";
                        $recommendations[] = "Add .htaccess files to upload directories";
                    }
                }
            }

            // Check for executable files in upload directories
            $executableExtensions = ['php', 'php3', 'php4', 'php5', 'phtml', 'pl', 'py', 'jsp', 'asp', 'sh'];
            $executableFiles = [];
            
            foreach ($uploadDirs as $dir) {
                $dirPath = storage_path("app/public/{$dir}");
                if (is_dir($dirPath)) {
                    $files = new \RecursiveIteratorIterator(
                        new \RecursiveDirectoryIterator($dirPath)
                    );
                    
                    foreach ($files as $file) {
                        if ($file->isFile()) {
                            $extension = strtolower($file->getExtension());
                            if (in_array($extension, $executableExtensions)) {
                                $executableFiles[] = $file->getPathname();
                            }
                        }
                    }
                }
            }

            if (!empty($executableFiles)) {
                $issues[] = count($executableFiles) . " executable files found in upload directories";
                $recommendations[] = "Remove or quarantine executable files from uploads";
            }

        } catch (\Exception $e) {
            $issues[] = 'Error scanning file permissions: ' . $e->getMessage();
        }

        return [
            'issues' => $issues,
            'recommendations' => $recommendations,
            'executable_files' => $executableFiles ?? [],
            'severity' => count($issues) > 2 ? 'high' : (count($issues) > 0 ? 'medium' : 'low')
        ];
    }

    /**
     * Scan database security
     */
    protected function scanDatabaseSecurity(): array
    {
        $issues = [];
        $recommendations = [];

        try {
            // Check for SQL injection vulnerabilities in stored data
            $suspiciousPatterns = [
                'union select',
                'drop table',
                'delete from',
                'update.*set',
                'insert into',
                '<script',
                'javascript:',
                'onload=',
                'onerror='
            ];

            $suspiciousData = [];
            $tables = ['chat_messages', 'contact_messages', 'visitor_behaviors'];
            
            foreach ($tables as $table) {
                foreach ($suspiciousPatterns as $pattern) {
                    $count = DB::table($table)
                        ->where('message', 'like', "%{$pattern}%")
                        ->orWhere('content', 'like', "%{$pattern}%")
                        ->count();
                    
                    if ($count > 0) {
                        $suspiciousData[] = "{$count} suspicious entries in {$table}";
                    }
                }
            }

            if (!empty($suspiciousData)) {
                $issues = array_merge($issues, $suspiciousData);
                $recommendations[] = "Review and sanitize suspicious database entries";
            }

            // Check for unencrypted sensitive data
            $unencryptedEmails = DB::table('contact_messages')
                ->where('email', 'not like', '%@%')
                ->where('email', 'regexp', '^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+$')
                ->count();

            // Check database user privileges
            try {
                $privileges = DB::select("SHOW GRANTS FOR CURRENT_USER()");
                $hasAllPrivileges = false;
                
                foreach ($privileges as $privilege) {
                    if (stripos($privilege->{'Grants for ' . DB::connection()->getConfig('username') . '@%'}, 'ALL PRIVILEGES') !== false) {
                        $hasAllPrivileges = true;
                        break;
                    }
                }

                if ($hasAllPrivileges) {
                    $issues[] = "Database user has ALL PRIVILEGES (overprivileged)";
                    $recommendations[] = "Use principle of least privilege for database access";
                }

            } catch (\Exception $e) {
                // Ignore privilege check errors
            }

        } catch (\Exception $e) {
            $issues[] = 'Error scanning database security: ' . $e->getMessage();
        }

        return [
            'issues' => $issues,
            'recommendations' => $recommendations,
            'severity' => count($issues) > 3 ? 'high' : (count($issues) > 1 ? 'medium' : 'low')
        ];
    }

    /**
     * Scan configuration security
     */
    protected function scanConfigurationSecurity(): array
    {
        $issues = [];
        $recommendations = [];

        try {
            // Check environment configuration
            if (config('app.debug') === true && config('app.env') === 'production') {
                $issues[] = "Debug mode enabled in production environment";
                $recommendations[] = "Disable debug mode in production";
            }

            // Check session security
            if (!config('session.secure') && config('app.env') === 'production') {
                $issues[] = "Session cookies not marked as secure";
                $recommendations[] = "Enable secure session cookies for HTTPS";
            }

            if (!config('session.http_only')) {
                $issues[] = "Session cookies accessible via JavaScript";
                $recommendations[] = "Enable HTTP-only session cookies";
            }

            // Check CSRF protection
            if (!in_array(\App\Http\Middleware\VerifyCsrfToken::class, config('app.middleware', []))) {
                $issues[] = "CSRF protection may not be properly configured";
                $recommendations[] = "Ensure CSRF middleware is enabled";
            }

            // Check encryption key
            if (empty(config('app.key'))) {
                $issues[] = "Application encryption key not set";
                $recommendations[] = "Generate and set application encryption key";
            }

            // Check database connection security
            if (config('database.default') === 'mysql') {
                $sslMode = config('database.connections.mysql.options.' . PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT);
                if ($sslMode !== true) {
                    $issues[] = "Database SSL verification not enabled";
                    $recommendations[] = "Enable SSL verification for database connections";
                }
            }

            // Check mail security
            if (config('mail.default') === 'smtp' && !config('mail.mailers.smtp.encryption')) {
                $issues[] = "SMTP mail not encrypted";
                $recommendations[] = "Enable SMTP encryption (TLS/SSL)";
            }

        } catch (\Exception $e) {
            $issues[] = 'Error scanning configuration: ' . $e->getMessage();
        }

        return [
            'issues' => $issues,
            'recommendations' => $recommendations,
            'severity' => count($issues) > 2 ? 'high' : (count($issues) > 0 ? 'medium' : 'low')
        ];
    }

    /**
     * Scan for common vulnerabilities
     */
    protected function scanVulnerabilities(): array
    {
        $vulnerabilities = [];
        $recommendations = [];

        try {
            // Check for outdated dependencies (simplified check)
            $composerLock = base_path('composer.lock');
            if (file_exists($composerLock)) {
                $lockData = json_decode(file_get_contents($composerLock), true);
                $oldPackages = [];
                
                foreach ($lockData['packages'] as $package) {
                    // This is a simplified check - in practice you'd use security advisories
                    if (isset($package['time'])) {
                        $packageTime = Carbon::parse($package['time']);
                        if ($packageTime->lt(Carbon::now()->subYear())) {
                            $oldPackages[] = $package['name'] . ' (' . $package['version'] . ')';
                        }
                    }
                }

                if (!empty($oldPackages)) {
                    $vulnerabilities[] = count($oldPackages) . " packages older than 1 year";
                    $recommendations[] = "Update outdated packages";
                }
            }

            // Check for common security headers
            $missingHeaders = [];
            $requiredHeaders = [
                'X-Content-Type-Options',
                'X-Frame-Options',
                'X-XSS-Protection',
                'Strict-Transport-Security'
            ];

            // This would need to be checked via actual HTTP requests
            // For now, we'll assume they're missing if not configured
            $vulnerabilities[] = "Security headers check requires HTTP testing";
            $recommendations[] = "Implement security headers middleware";

        } catch (\Exception $e) {
            $vulnerabilities[] = 'Error scanning vulnerabilities: ' . $e->getMessage();
        }

        return [
            'vulnerabilities' => $vulnerabilities,
            'recommendations' => $recommendations,
            'severity' => count($vulnerabilities) > 2 ? 'high' : (count($vulnerabilities) > 0 ? 'medium' : 'low')
        ];
    }

    /**
     * Analyze access logs for suspicious activity
     */
    protected function analyzeAccessLogs(): array
    {
        $analysis = [];
        $suspiciousActivity = [];

        try {
            // Check for suspicious login attempts
            $recentFailedLogins = DB::table('users')
                ->where('failed_login_attempts', '>', 3)
                ->where('updated_at', '>', Carbon::now()->subHours(24))
                ->count();

            if ($recentFailedLogins > 0) {
                $suspiciousActivity[] = "{$recentFailedLogins} users with multiple failed logins in 24h";
            }

            // Check for unusual access patterns
            $nightTimeLogins = DB::table('users')
                ->where('last_login_at', '>', Carbon::now()->subDays(7))
                ->whereRaw('HOUR(last_login_at) BETWEEN 0 AND 5')
                ->count();

            if ($nightTimeLogins > 10) {
                $suspiciousActivity[] = "{$nightTimeLogins} logins during night hours (00:00-05:00)";
            }

            // Check for rapid session creation
            $rapidSessions = DB::table('chat_sessions')
                ->where('started_at', '>', Carbon::now()->subHours(1))
                ->groupBy('visitor_ip')
                ->havingRaw('COUNT(*) > 10')
                ->count();

            if ($rapidSessions > 0) {
                $suspiciousActivity[] = "{$rapidSessions} IPs creating excessive sessions";
            }

            $analysis = [
                'suspicious_activity' => $suspiciousActivity,
                'severity' => count($suspiciousActivity) > 2 ? 'high' : (count($suspiciousActivity) > 0 ? 'medium' : 'low')
            ];

        } catch (\Exception $e) {
            $analysis['error'] = 'Error analyzing access logs: ' . $e->getMessage();
        }

        return $analysis;
    }

    /**
     * Calculate overall security score
     */
    protected function calculateSecurityScore(array $results): int
    {
        $totalIssues = $this->countIssues($results);
        $maxScore = 100;
        $deduction = min($totalIssues * 5, 80); // Max 80 point deduction

        return max($maxScore - $deduction, 20); // Minimum score of 20
    }

    /**
     * Count total issues across all scans
     */
    protected function countIssues(array $results): int
    {
        $count = 0;
        
        foreach ($results as $category => $data) {
            if (is_array($data) && isset($data['issues'])) {
                $count += count($data['issues']);
            }
            if (is_array($data) && isset($data['vulnerabilities'])) {
                $count += count($data['vulnerabilities']);
            }
            if (is_array($data) && isset($data['suspicious_activity'])) {
                $count += count($data['suspicious_activity']);
            }
        }

        return $count;
    }

    /**
     * Generate security report
     */
    public function generateSecurityReport(): array
    {
        $scanResults = $this->performSecurityScan();
        
        $report = [
            'scan_date' => now()->toISOString(),
            'security_score' => $scanResults['security_score'] ?? 0,
            'total_issues' => $this->countIssues($scanResults),
            'categories' => $scanResults,
            'recommendations' => $this->consolidateRecommendations($scanResults),
            'next_scan_recommended' => now()->addWeek()->toDateString()
        ];

        // Store report for dashboard
        Storage::disk('private')->put(
            'security_reports/scan_' . now()->format('Y-m-d_H-i-s') . '.json',
            json_encode($report, JSON_PRETTY_PRINT)
        );

        return $report;
    }

    /**
     * Consolidate all recommendations
     */
    protected function consolidateRecommendations(array $results): array
    {
        $allRecommendations = [];
        
        foreach ($results as $category => $data) {
            if (is_array($data) && isset($data['recommendations'])) {
                $allRecommendations = array_merge($allRecommendations, $data['recommendations']);
            }
        }

        return array_unique($allRecommendations);
    }
}

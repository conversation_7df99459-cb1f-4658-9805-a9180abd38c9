<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class GdprCompliance
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Log data processing activities for GDPR compliance
        if ($this->shouldLogActivity($request)) {
            $this->logDataProcessingActivity($request);
        }

        $response = $next($request);

        // Add privacy-related headers
        $response->headers->set('X-Privacy-Policy', config('app.url') . '/privacy-policy');
        $response->headers->set('X-Data-Retention', '90-days');

        return $response;
    }

    /**
     * Determine if we should log this activity
     */
    protected function shouldLogActivity(Request $request): bool
    {
        // Log activities that involve personal data processing
        $sensitiveRoutes = [
            'api/v1/visitors/init-session',
            'api/v1/visitors/track-behavior',
            'api/v1/contact/store',
            'api/v1/chat/start-session'
        ];

        foreach ($sensitiveRoutes as $route) {
            if ($request->is($route)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Log data processing activity
     */
    protected function logDataProcessingActivity(Request $request): void
    {
        $activity = [
            'timestamp' => now()->toISOString(),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'route' => $request->route()?->getName() ?? $request->path(),
            'method' => $request->method(),
            'data_types' => $this->identifyDataTypes($request),
            'legal_basis' => $this->determineLegalBasis($request),
            'purpose' => $this->determinePurpose($request)
        ];

        Log::channel('gdpr')->info('Data processing activity', $activity);
    }

    /**
     * Identify types of personal data being processed
     */
    protected function identifyDataTypes(Request $request): array
    {
        $dataTypes = [];

        if ($request->has(['name', 'visitor_name'])) {
            $dataTypes[] = 'name';
        }

        if ($request->has(['email', 'visitor_email'])) {
            $dataTypes[] = 'email';
        }

        if ($request->has(['phone', 'visitor_phone'])) {
            $dataTypes[] = 'phone';
        }

        if ($request->ip()) {
            $dataTypes[] = 'ip_address';
        }

        if ($request->userAgent()) {
            $dataTypes[] = 'user_agent';
        }

        if ($request->has(['page_url', 'coordinates', 'scroll_depth'])) {
            $dataTypes[] = 'behavioral_data';
        }

        return $dataTypes;
    }

    /**
     * Determine legal basis for processing
     */
    protected function determineLegalBasis(Request $request): string
    {
        if ($request->is('api/v1/contact/*')) {
            return 'consent'; // Contact forms require explicit consent
        }

        if ($request->is('api/v1/chat/*')) {
            return 'legitimate_interest'; // Customer service is legitimate interest
        }

        if ($request->is('api/v1/visitors/*')) {
            return 'legitimate_interest'; // Website analytics for service improvement
        }

        return 'not_specified';
    }

    /**
     * Determine purpose of processing
     */
    protected function determinePurpose(Request $request): string
    {
        if ($request->is('api/v1/contact/*')) {
            return 'customer_support';
        }

        if ($request->is('api/v1/chat/*')) {
            return 'live_chat_service';
        }

        if ($request->is('api/v1/visitors/*')) {
            return 'website_analytics';
        }

        return 'general_service_provision';
    }
}

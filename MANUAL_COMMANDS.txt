========================================
Manual Deployment Commands
========================================

If batch files don't work due to encoding issues,
please run these commands manually in Command Prompt:

Step 1: Open Command Prompt as Administrator
- Press Win+R, type "cmd", press Ctrl+Shift+Enter

Step 2: Navigate to project directory
cd /d D:\wwwroot\aichat.jagship.com

Step 3: Check if PHP exists
dir D:\BtSoft\php\72\php.exe
dir D:\BtSoft\php\73\php.exe
dir D:\BtSoft\php\74\php.exe

Step 4: Set PHP path (use the one that exists)
set PHP_PATH=D:\BtSoft\php\72\php.exe

Step 5: Check Composer
composer --version

Step 6: If Composer not found, download it
powershell -Command "Invoke-WebRequest -Uri 'https://getcomposer.org/composer.phar' -OutFile 'composer.phar'"

Step 7: Configure Composer (use system composer or downloaded one)
composer config -g secure-http false
composer config -g repo.packagist composer http://packagist.phpcomposer.com

OR if using downloaded composer.phar:
%PHP_PATH% composer.phar config -g secure-http false
%PHP_PATH% composer.phar config -g repo.packagist composer http://packagist.phpcomposer.com

Step 8: Install dependencies
composer install --no-dev --optimize-autoloader

OR if using composer.phar:
%PHP_PATH% composer.phar install --no-dev --optimize-autoloader

Step 9: Create .env file
copy .env.example .env

Step 10: Generate app key
%PHP_PATH% artisan key:generate

Step 11: Test Laravel
%PHP_PATH% artisan --version

========================================
BT Panel Configuration
========================================

1. Login to BT Panel
2. Go to "Website" -> "Add Site"
3. Domain: aichat.jagship.com
4. Root Directory: D:\wwwroot\aichat.jagship.com\public
5. PHP Version: 7.2
6. Create Database: Yes

PHP Extensions needed:
- redis
- opcache  
- fileinfo
- zip
- curl
- gd
- mbstring

Disabled Functions to remove:
- putenv
- getenv

========================================
Database Configuration
========================================

Edit .env file and set:

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_database_user
DB_PASSWORD=your_database_password

Then run:
%PHP_PATH% artisan migrate

========================================
Common Issues
========================================

1. If you see Chinese characters as commands:
   - Your system has encoding issues
   - Use these manual commands instead

2. If "composer" command not found:
   - Download composer.phar manually
   - Use: php composer.phar instead of composer

3. If PHP not found:
   - Check BT Panel installation
   - Verify PHP installation path

4. If dependencies fail to install:
   - Add --ignore-platform-reqs flag
   - Check network connection

========================================

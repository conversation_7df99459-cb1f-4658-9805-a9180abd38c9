<?php

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LanguagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $languages = [
            ['code' => 'en', 'name' => 'English', 'native_name' => 'English', 'flag' => '🇺🇸', 'is_active' => true, 'sort_order' => 1],
            ['code' => 'es', 'name' => 'Spanish', 'native_name' => 'Español', 'flag' => '🇪🇸', 'is_active' => true, 'sort_order' => 2],
            ['code' => 'fr', 'name' => 'French', 'native_name' => 'Français', 'flag' => '🇫🇷', 'is_active' => true, 'sort_order' => 3],
            ['code' => 'de', 'name' => 'German', 'native_name' => 'Deutsch', 'flag' => '🇩🇪', 'is_active' => true, 'sort_order' => 4],
            ['code' => 'it', 'name' => 'Italian', 'native_name' => 'Italiano', 'flag' => '🇮🇹', 'is_active' => true, 'sort_order' => 5],
            ['code' => 'pt', 'name' => 'Portuguese', 'native_name' => 'Português', 'flag' => '🇵🇹', 'is_active' => true, 'sort_order' => 6],
            ['code' => 'ru', 'name' => 'Russian', 'native_name' => 'Русский', 'flag' => '🇷🇺', 'is_active' => true, 'sort_order' => 7],
            ['code' => 'zh', 'name' => 'Chinese', 'native_name' => '中文', 'flag' => '🇨🇳', 'is_active' => true, 'sort_order' => 8],
            ['code' => 'ja', 'name' => 'Japanese', 'native_name' => '日本語', 'flag' => '🇯🇵', 'is_active' => true, 'sort_order' => 9],
            ['code' => 'ko', 'name' => 'Korean', 'native_name' => '한국어', 'flag' => '🇰🇷', 'is_active' => true, 'sort_order' => 10],
            ['code' => 'ar', 'name' => 'Arabic', 'native_name' => 'العربية', 'flag' => '🇸🇦', 'is_active' => true, 'is_rtl' => true, 'sort_order' => 11],
            ['code' => 'hi', 'name' => 'Hindi', 'native_name' => 'हिन्दी', 'flag' => '🇮🇳', 'is_active' => true, 'sort_order' => 12],
            ['code' => 'th', 'name' => 'Thai', 'native_name' => 'ไทย', 'flag' => '🇹🇭', 'is_active' => true, 'sort_order' => 13],
            ['code' => 'vi', 'name' => 'Vietnamese', 'native_name' => 'Tiếng Việt', 'flag' => '🇻🇳', 'is_active' => true, 'sort_order' => 14],
            ['code' => 'id', 'name' => 'Indonesian', 'native_name' => 'Bahasa Indonesia', 'flag' => '🇮🇩', 'is_active' => true, 'sort_order' => 15],
            ['code' => 'ms', 'name' => 'Malay', 'native_name' => 'Bahasa Melayu', 'flag' => '🇲🇾', 'is_active' => true, 'sort_order' => 16],
            ['code' => 'tl', 'name' => 'Filipino', 'native_name' => 'Filipino', 'flag' => '🇵🇭', 'is_active' => true, 'sort_order' => 17],
        ];

        foreach ($languages as $language) {
            DB::table('languages')->insert(array_merge($language, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }
}

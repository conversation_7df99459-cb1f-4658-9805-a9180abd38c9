<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\WebSocketServer::class,
        Commands\UpdateAgentStatus::class,
        Commands\CleanupTrackingData::class,
        Commands\CleanupExportFiles::class,
        Commands\CleanupOldData::class,
        Commands\MonitorPerformance::class,
        Commands\OptimizeSystem::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // Update agent status based on working hours every minute
        $schedule->command('agents:update-status')->everyMinute();

        // Clean up old visitor tracking data
        $schedule->command('cleanup:visitor-tracking')->daily();

        // Clean up old chat sessions
        $schedule->command('cleanup:chat-sessions')->daily();

        // Generate analytics reports
        $schedule->command('analytics:generate-reports')->hourly();

        // Send webhook notifications
        $schedule->command('webhooks:process')->everyMinute();

        // Update visitor location data
        $schedule->command('visitors:update-locations')->everyTenMinutes();

        // Clean up old chat sessions with ChatService
        $schedule->call(function () {
            app(\App\Services\ChatService::class)->cleanupOldSessions(30);
        })->dailyAt('02:00');

        // Clean up old tracking data (keep 90 days)
        $schedule->command('cleanup:tracking-data --days=90')->dailyAt('03:00');

        // Clean up old export files (keep 7 days)
        $schedule->command('cleanup:export-files --days=7')->dailyAt('04:00');

        // Performance and security monitoring
        $schedule->command('monitor:performance --log-results')->hourly();

        // System optimization (weekly)
        $schedule->command('system:optimize --all')->weeklyOn(1, '01:00');

        // Data cleanup (daily)
        $schedule->command('cleanup:old-data --days=90')->dailyAt('05:00');

        // Security scan (weekly)
        $schedule->command('system:optimize --security --report')->weeklyOn(7, '02:00');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}

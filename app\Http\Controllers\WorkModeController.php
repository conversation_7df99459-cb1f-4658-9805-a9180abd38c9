<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Site;
use App\Models\WorkingHour;
use App\Models\ContactMessage;
use App\Services\WorkModeService;
use App\Services\PermissionService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class WorkModeController extends Controller
{
    protected $workModeService;
    protected $permissionService;

    public function __construct(WorkModeService $workModeService, PermissionService $permissionService)
    {
        $this->workModeService = $workModeService;
        $this->permissionService = $permissionService;
        $this->middleware('auth');
    }

    /**
     * Get site working status
     */
    public function getSiteStatus(Request $request, $siteId)
    {
        $user = Auth::user();
        
        if (!$user->canAccessSite($siteId)) {
            return response()->json(['error' => 'Access denied to this site'], 403);
        }

        $site = Site::findOrFail($siteId);
        $status = $this->workModeService->getSiteWorkingStatus($site);

        return response()->json([
            'success' => true,
            'data' => $status
        ]);
    }

    /**
     * Get working hours for site
     */
    public function getWorkingHours(Request $request, $siteId)
    {
        $user = Auth::user();
        
        if (!$user->canAccessSite($siteId)) {
            return response()->json(['error' => 'Access denied to this site'], 403);
        }

        $workingHours = WorkingHour::getFormattedSchedule($siteId);

        return response()->json([
            'success' => true,
            'data' => $workingHours
        ]);
    }

    /**
     * Update working hours for site
     */
    public function updateWorkingHours(Request $request, $siteId)
    {
        $user = Auth::user();
        
        if (!$user->canAccessSite($siteId)) {
            return response()->json(['error' => 'Access denied to this site'], 403);
        }

        if (!$this->permissionService->hasPermission($user, 'manage_working_hours', $siteId)) {
            return response()->json(['error' => 'No permission to manage working hours'], 403);
        }

        $validator = Validator::make($request->all(), [
            'working_hours' => 'required|array',
            'working_hours.*.day' => 'required|string|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'working_hours.*.is_working' => 'required|boolean',
            'working_hours.*.start_time' => 'required_if:working_hours.*.is_working,true|date_format:H:i:s',
            'working_hours.*.end_time' => 'required_if:working_hours.*.is_working,true|date_format:H:i:s',
            'working_hours.*.is_24_hours' => 'sometimes|boolean',
            'working_hours.*.break_start_time' => 'sometimes|nullable|date_format:H:i:s',
            'working_hours.*.break_end_time' => 'sometimes|nullable|date_format:H:i:s',
            'timezone' => 'sometimes|string|timezone',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $site = Site::findOrFail($siteId);
            $timezone = $request->input('timezone', $site->timezone ?: config('app.timezone'));

            foreach ($request->input('working_hours') as $hourData) {
                WorkingHour::updateOrCreate(
                    [
                        'site_id' => $siteId,
                        'day_of_week' => $hourData['day'],
                    ],
                    [
                        'is_working_day' => $hourData['is_working'],
                        'start_time' => $hourData['is_working'] ? $hourData['start_time'] : null,
                        'end_time' => $hourData['is_working'] ? $hourData['end_time'] : null,
                        'is_24_hours' => $hourData['is_24_hours'] ?? false,
                        'break_start_time' => $hourData['break_start_time'] ?? null,
                        'break_end_time' => $hourData['break_end_time'] ?? null,
                        'timezone' => $timezone,
                    ]
                );
            }

            // Clear cache
            $this->workModeService->clearSiteStatusCache($site);

            return response()->json([
                'success' => true,
                'message' => 'Working hours updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to update working hours'
            ], 500);
        }
    }

    /**
     * Get contact messages
     */
    public function getContactMessages(Request $request, $siteId = null)
    {
        $user = Auth::user();
        
        $query = ContactMessage::with(['site', 'visitor', 'assignedUser']);

        if ($siteId) {
            if (!$user->canAccessSite($siteId)) {
                return response()->json(['error' => 'Access denied to this site'], 403);
            }
            $query->where('site_id', $siteId);
        } else {
            // Filter by user's accessible sites
            $siteIds = $user->sites()->pluck('site_id');
            $query->whereIn('site_id', $siteIds);
        }

        // Filter based on user role
        if ($user->role === 'agent') {
            $query->where(function ($q) use ($user) {
                $q->where('assigned_user_id', $user->id)
                  ->orWhereNull('assigned_user_id');
            });
        }

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->input('status'));
        }

        if ($request->has('priority')) {
            $query->where('priority', $request->input('priority'));
        }

        if ($request->has('category')) {
            $query->where('category', $request->input('category'));
        }

        if ($request->has('assigned_to')) {
            if ($request->input('assigned_to') === 'unassigned') {
                $query->whereNull('assigned_user_id');
            } else {
                $query->where('assigned_user_id', $request->input('assigned_to'));
            }
        }

        $messages = $query->orderBy('created_at', 'desc')
            ->paginate($request->input('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => $messages->items(),
            'pagination' => [
                'current_page' => $messages->currentPage(),
                'last_page' => $messages->lastPage(),
                'per_page' => $messages->perPage(),
                'total' => $messages->total(),
            ]
        ]);
    }

    /**
     * Get specific contact message
     */
    public function getContactMessage(Request $request, $messageId)
    {
        $user = Auth::user();
        
        $message = ContactMessage::with(['site', 'visitor', 'assignedUser'])
            ->where('message_id', $messageId)
            ->firstOrFail();

        if (!$user->canAccessSite($message->site_id)) {
            return response()->json(['error' => 'Access denied to this message'], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $message->toApiArray()
        ]);
    }

    /**
     * Assign contact message to agent
     */
    public function assignContactMessage(Request $request, $messageId)
    {
        $validator = Validator::make($request->all(), [
            'agent_id' => 'required|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        try {
            $message = ContactMessage::where('message_id', $messageId)->firstOrFail();
            
            if (!$user->canAccessSite($message->site_id)) {
                return response()->json(['error' => 'Access denied to this message'], 403);
            }

            if (!$this->permissionService->hasPermission($user, 'assign_contact_messages', $message->site_id)) {
                return response()->json(['error' => 'No permission to assign messages'], 403);
            }

            $agent = User::findOrFail($request->input('agent_id'));
            
            if (!$agent->canAccessSite($message->site_id)) {
                return response()->json(['error' => 'Agent does not have access to this site'], 403);
            }

            $message->assignTo($agent);

            return response()->json([
                'success' => true,
                'message' => 'Contact message assigned successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to assign contact message'
            ], 500);
        }
    }

    /**
     * Respond to contact message
     */
    public function respondToContactMessage(Request $request, $messageId)
    {
        $validator = Validator::make($request->all(), [
            'response' => 'required|string|max:5000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        try {
            $message = ContactMessage::where('message_id', $messageId)->firstOrFail();
            
            if (!$user->canAccessSite($message->site_id)) {
                return response()->json(['error' => 'Access denied to this message'], 403);
            }

            if (!$this->permissionService->hasPermission($user, 'respond_to_messages', $message->site_id)) {
                return response()->json(['error' => 'No permission to respond to messages'], 403);
            }

            $message->respond($request->input('response'), $user);

            // Send email response if email is provided
            if ($message->email) {
                // TODO: Implement email sending
            }

            return response()->json([
                'success' => true,
                'message' => 'Response sent successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to send response'
            ], 500);
        }
    }

    /**
     * Update contact message status
     */
    public function updateContactMessageStatus(Request $request, $messageId)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|string|in:new,assigned,in_progress,responded,closed,spam',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();
        
        try {
            $message = ContactMessage::where('message_id', $messageId)->firstOrFail();
            
            if (!$user->canAccessSite($message->site_id)) {
                return response()->json(['error' => 'Access denied to this message'], 403);
            }

            if (!$this->permissionService->hasPermission($user, 'manage_contact_messages', $message->site_id)) {
                return response()->json(['error' => 'No permission to update message status'], 403);
            }

            $status = $request->input('status');
            
            switch ($status) {
                case 'in_progress':
                    $message->markInProgress();
                    break;
                case 'closed':
                    $message->close();
                    break;
                case 'spam':
                    $message->markAsSpam();
                    break;
                default:
                    $message->update(['status' => $status]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Status updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'Failed to update status'
            ], 500);
        }
    }

    /**
     * Get work mode statistics
     */
    public function getStatistics(Request $request, $siteId)
    {
        $user = Auth::user();
        
        if (!$user->canAccessSite($siteId)) {
            return response()->json(['error' => 'Access denied to this site'], 403);
        }

        $site = Site::findOrFail($siteId);
        $period = $request->input('period', '7d');
        
        $statistics = $this->workModeService->getWorkModeStatistics($site, $period);

        return response()->json([
            'success' => true,
            'data' => $statistics
        ]);
    }

    /**
     * Get offline message template
     */
    public function getOfflineTemplate(Request $request, $siteId)
    {
        $user = Auth::user();
        
        if (!$user->canAccessSite($siteId)) {
            return response()->json(['error' => 'Access denied to this site'], 403);
        }

        $site = Site::findOrFail($siteId);
        $template = $this->workModeService->getOfflineMessageTemplate($site);

        return response()->json([
            'success' => true,
            'data' => $template
        ]);
    }
}

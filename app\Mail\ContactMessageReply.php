<?php

namespace App\Mail;

use App\Models\ContactMessage;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ContactMessageReply extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $contactMessage;
    public $repliedBy;

    /**
     * Create a new message instance.
     */
    public function __construct(ContactMessage $contactMessage, User $repliedBy)
    {
        $this->contactMessage = $contactMessage;
        $this->repliedBy = $repliedBy;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject('Re: ' . $this->contactMessage->subject)
                    ->view('emails.contact-message-reply')
                    ->with([
                        'message' => $this->contactMessage,
                        'repliedBy' => $this->repliedBy,
                        'referenceNumber' => $this->contactMessage->reference_number
                    ]);
    }
}

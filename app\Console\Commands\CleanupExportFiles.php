<?php

namespace App\Console\Commands;

use App\Services\ExportService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CleanupExportFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:export-files 
                            {--days=7 : Number of days to keep export files}
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old export files to free up storage space';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $dryRun = $this->option('dry-run');
        
        $this->info("Cleaning up export files older than {$days} days");
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No files will be actually deleted');
        }
        
        try {
            $exportService = app(ExportService::class);
            
            if ($dryRun) {
                $this->showFilesToDelete($days);
            } else {
                $deletedCount = $exportService->cleanupOldExports($days);
                $this->info("Successfully deleted {$deletedCount} old export files");
            }
            
            $this->showStorageStats();
            
        } catch (\Exception $e) {
            $this->error('Failed to cleanup export files: ' . $e->getMessage());
            return 1;
        }
        
        return 0;
    }

    /**
     * Show files that would be deleted in dry run mode
     */
    protected function showFilesToDelete($days)
    {
        $files = Storage::files('exports');
        $cutoffTime = now()->subDays($days);
        
        $filesToDelete = [];
        $totalSize = 0;
        
        foreach ($files as $file) {
            $lastModified = \Carbon\Carbon::createFromTimestamp(Storage::lastModified($file));
            
            if ($lastModified->lt($cutoffTime)) {
                $fileSize = Storage::size($file);
                $filesToDelete[] = [
                    'file' => basename($file),
                    'size' => $this->formatBytes($fileSize),
                    'modified' => $lastModified->toDateTimeString(),
                ];
                $totalSize += $fileSize;
            }
        }
        
        if (empty($filesToDelete)) {
            $this->info('No files found to delete.');
            return;
        }
        
        $this->info("Files that would be deleted:");
        $this->table(
            ['File', 'Size', 'Last Modified'],
            $filesToDelete
        );
        
        $this->info("Total files to delete: " . count($filesToDelete));
        $this->info("Total space to free: " . $this->formatBytes($totalSize));
    }

    /**
     * Show current storage statistics
     */
    protected function showStorageStats()
    {
        try {
            $files = Storage::files('exports');
            
            if (empty($files)) {
                $this->info('No export files found.');
                return;
            }
            
            $totalFiles = count($files);
            $totalSize = 0;
            $typeStats = [];
            
            foreach ($files as $file) {
                $fileSize = Storage::size($file);
                $totalSize += $fileSize;
                
                // Get file type from filename
                $fileName = basename($file);
                $parts = explode('_', $fileName);
                $type = $parts[0] ?? 'unknown';
                
                if (!isset($typeStats[$type])) {
                    $typeStats[$type] = ['count' => 0, 'size' => 0];
                }
                
                $typeStats[$type]['count']++;
                $typeStats[$type]['size'] += $fileSize;
            }
            
            $this->info("\nCurrent export storage statistics:");
            $this->info("Total files: {$totalFiles}");
            $this->info("Total size: " . $this->formatBytes($totalSize));
            
            if (!empty($typeStats)) {
                $this->info("\nBreakdown by export type:");
                
                $tableData = [];
                foreach ($typeStats as $type => $stats) {
                    $tableData[] = [
                        'type' => ucfirst($type),
                        'count' => $stats['count'],
                        'size' => $this->formatBytes($stats['size']),
                    ];
                }
                
                $this->table(
                    ['Export Type', 'Files', 'Total Size'],
                    $tableData
                );
            }
            
        } catch (\Exception $e) {
            $this->warn('Could not retrieve storage statistics: ' . $e->getMessage());
        }
    }

    /**
     * Format bytes to human readable format
     */
    protected function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

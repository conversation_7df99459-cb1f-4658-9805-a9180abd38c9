<?php

namespace App\Http\Controllers\Admin;

use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\VisitorSession;
use App\Models\PageView;
use App\Models\User;
use App\Services\AnalyticsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class AnalyticsController extends AdminController
{
    protected $analyticsService;

    public function __construct(AnalyticsService $analyticsService)
    {
        parent::__construct();
        $this->analyticsService = $analyticsService;
    }

    /**
     * Get comprehensive analytics dashboard
     */
    public function getDashboardAnalytics(Request $request): JsonResponse
    {
        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'view');
            if ($error) return $error;

            $startDate = $request->get('start_date', now()->subDays(30)->toDateString());
            $endDate = $request->get('end_date', now()->toDateString());
            $compareWith = $request->get('compare_with', 'previous_period');

            $analytics = [
                'overview' => $this->getOverviewMetrics($siteId, $startDate, $endDate),
                'chat_metrics' => $this->getChatMetrics($siteId, $startDate, $endDate),
                'visitor_metrics' => $this->getVisitorMetrics($siteId, $startDate, $endDate),
                'agent_performance' => $this->getAgentPerformanceMetrics($siteId, $startDate, $endDate),
                'trends' => $this->getTrendAnalysis($siteId, $startDate, $endDate),
                'comparisons' => $this->getComparisonData($siteId, $startDate, $endDate, $compareWith),
            ];

            return $this->successResponse($analytics);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get analytics: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get overview metrics
     */
    private function getOverviewMetrics($siteId, $startDate, $endDate)
    {
        return [
            'total_visitors' => VisitorSession::where('site_id', $siteId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->distinct('visitor_id')
                ->count(),
            
            'total_sessions' => ChatSession::where('site_id', $siteId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            
            'total_messages' => ChatMessage::whereHas('session', function($query) use ($siteId) {
                    $query->where('site_id', $siteId);
                })
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            
            'conversion_rate' => $this->calculateConversionRate($siteId, $startDate, $endDate),
            
            'avg_session_duration' => ChatSession::where('site_id', $siteId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->whereNotNull('ended_at')
                ->avg(DB::raw('TIMESTAMPDIFF(SECOND, created_at, ended_at)')) ?? 0,
            
            'satisfaction_score' => ChatSession::where('site_id', $siteId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->whereNotNull('satisfaction_rating')
                ->avg('satisfaction_rating') ?? 0,
        ];
    }

    /**
     * Get chat-specific metrics
     */
    private function getChatMetrics($siteId, $startDate, $endDate)
    {
        $sessions = ChatSession::where('site_id', $siteId)
            ->whereBetween('created_at', [$startDate, $endDate]);

        return [
            'sessions_by_status' => [
                'completed' => (clone $sessions)->where('status', 'ended')->count(),
                'active' => (clone $sessions)->where('status', 'active')->count(),
                'waiting' => (clone $sessions)->where('status', 'waiting')->count(),
                'abandoned' => (clone $sessions)->where('status', 'abandoned')->count(),
            ],
            
            'avg_response_time' => ChatMessage::whereHas('session', function($query) use ($siteId) {
                    $query->where('site_id', $siteId);
                })
                ->where('sender_type', 'agent')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->avg('response_time') ?? 0,
            
            'messages_per_session' => ChatMessage::whereHas('session', function($query) use ($siteId) {
                    $query->where('site_id', $siteId);
                })
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count() / max(1, (clone $sessions)->count()),
            
            'peak_hours' => $this->getPeakHours($siteId, $startDate, $endDate),
            
            'satisfaction_distribution' => ChatSession::where('site_id', $siteId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->whereNotNull('satisfaction_rating')
                ->selectRaw('satisfaction_rating, COUNT(*) as count')
                ->groupBy('satisfaction_rating')
                ->orderBy('satisfaction_rating')
                ->get()
                ->pluck('count', 'satisfaction_rating'),
        ];
    }

    /**
     * Get visitor metrics
     */
    private function getVisitorMetrics($siteId, $startDate, $endDate)
    {
        return [
            'unique_visitors' => VisitorSession::where('site_id', $siteId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->distinct('visitor_id')
                ->count(),
            
            'returning_visitors' => VisitorSession::where('site_id', $siteId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->whereHas('visitor', function($query) {
                    $query->where('visit_count', '>', 1);
                })
                ->distinct('visitor_id')
                ->count(),
            
            'avg_pages_per_session' => PageView::whereHas('visitorSession', function($query) use ($siteId) {
                    $query->where('site_id', $siteId);
                })
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count() / max(1, VisitorSession::where('site_id', $siteId)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count()),
            
            'top_pages' => PageView::whereHas('visitorSession', function($query) use ($siteId) {
                    $query->where('site_id', $siteId);
                })
                ->whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('url, COUNT(*) as views')
                ->groupBy('url')
                ->orderBy('views', 'desc')
                ->limit(10)
                ->get(),
            
            'traffic_sources' => VisitorSession::where('site_id', $siteId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('referrer_domain, COUNT(*) as count')
                ->groupBy('referrer_domain')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get(),
            
            'device_types' => VisitorSession::where('site_id', $siteId)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->selectRaw('device_type, COUNT(*) as count')
                ->groupBy('device_type')
                ->get()
                ->pluck('count', 'device_type'),
        ];
    }

    /**
     * Get agent performance metrics
     */
    private function getAgentPerformanceMetrics($siteId, $startDate, $endDate)
    {
        $agents = User::where('site_id', $siteId)
            ->where('role', 'agent')
            ->get();

        $performance = [];
        foreach ($agents as $agent) {
            $sessions = ChatSession::where('site_id', $siteId)
                ->where('agent_id', $agent->id)
                ->whereBetween('created_at', [$startDate, $endDate]);

            $performance[] = [
                'agent_id' => $agent->id,
                'agent_name' => $agent->name,
                'sessions_handled' => (clone $sessions)->count(),
                'avg_response_time' => ChatMessage::whereHas('session', function($query) use ($siteId, $agent) {
                        $query->where('site_id', $siteId)->where('agent_id', $agent->id);
                    })
                    ->where('sender_type', 'agent')
                    ->where('sender_id', $agent->id)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->avg('response_time') ?? 0,
                'satisfaction_rating' => (clone $sessions)
                    ->whereNotNull('satisfaction_rating')
                    ->avg('satisfaction_rating') ?? 0,
                'messages_sent' => ChatMessage::whereHas('session', function($query) use ($siteId, $agent) {
                        $query->where('site_id', $siteId)->where('agent_id', $agent->id);
                    })
                    ->where('sender_type', 'agent')
                    ->where('sender_id', $agent->id)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count(),
            ];
        }

        return collect($performance)->sortByDesc('sessions_handled')->values();
    }

    /**
     * Get trend analysis
     */
    private function getTrendAnalysis($siteId, $startDate, $endDate)
    {
        $dailyStats = ChatSession::where('site_id', $siteId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as sessions, AVG(satisfaction_rating) as avg_rating')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $hourlyStats = ChatSession::where('site_id', $siteId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as sessions')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();

        return [
            'daily_sessions' => $dailyStats->pluck('sessions', 'date'),
            'daily_satisfaction' => $dailyStats->pluck('avg_rating', 'date'),
            'hourly_distribution' => $hourlyStats->pluck('sessions', 'hour'),
        ];
    }

    /**
     * Get comparison data
     */
    private function getComparisonData($siteId, $startDate, $endDate, $compareWith)
    {
        $currentPeriod = [
            'start' => $startDate,
            'end' => $endDate,
        ];

        // Calculate comparison period
        $start = \Carbon\Carbon::parse($startDate);
        $end = \Carbon\Carbon::parse($endDate);
        $daysDiff = $start->diffInDays($end);

        switch ($compareWith) {
            case 'previous_period':
                $comparisonStart = $start->copy()->subDays($daysDiff + 1);
                $comparisonEnd = $start->copy()->subDay();
                break;
            case 'previous_month':
                $comparisonStart = $start->copy()->subMonth();
                $comparisonEnd = $end->copy()->subMonth();
                break;
            case 'previous_year':
                $comparisonStart = $start->copy()->subYear();
                $comparisonEnd = $end->copy()->subYear();
                break;
            default:
                return null;
        }

        $currentMetrics = $this->getOverviewMetrics($siteId, $currentPeriod['start'], $currentPeriod['end']);
        $comparisonMetrics = $this->getOverviewMetrics($siteId, $comparisonStart->toDateString(), $comparisonEnd->toDateString());

        $comparison = [];
        foreach ($currentMetrics as $key => $currentValue) {
            $comparisonValue = $comparisonMetrics[$key] ?? 0;
            $change = $comparisonValue > 0 ? (($currentValue - $comparisonValue) / $comparisonValue) * 100 : 0;
            
            $comparison[$key] = [
                'current' => $currentValue,
                'previous' => $comparisonValue,
                'change_percent' => round($change, 2),
                'trend' => $change > 0 ? 'up' : ($change < 0 ? 'down' : 'stable'),
            ];
        }

        return $comparison;
    }

    /**
     * Get real-time analytics
     */
    public function getRealTimeAnalytics(Request $request): JsonResponse
    {
        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'view');
            if ($error) return $error;

            $realTime = [
                'active_visitors' => VisitorSession::where('site_id', $siteId)
                    ->where('last_activity_at', '>=', now()->subMinutes(5))
                    ->count(),
                
                'active_chats' => ChatSession::where('site_id', $siteId)
                    ->where('status', 'active')
                    ->count(),
                
                'waiting_chats' => ChatSession::where('site_id', $siteId)
                    ->where('status', 'waiting')
                    ->count(),
                
                'online_agents' => User::where('site_id', $siteId)
                    ->where('role', 'agent')
                    ->where('status', 'online')
                    ->count(),
                
                'recent_sessions' => ChatSession::where('site_id', $siteId)
                    ->with(['visitor', 'agent'])
                    ->orderBy('created_at', 'desc')
                    ->limit(10)
                    ->get(),
                
                'current_page_views' => PageView::whereHas('visitorSession', function($query) use ($siteId) {
                        $query->where('site_id', $siteId);
                    })
                    ->where('created_at', '>=', now()->subMinutes(5))
                    ->selectRaw('url, COUNT(*) as views')
                    ->groupBy('url')
                    ->orderBy('views', 'desc')
                    ->limit(5)
                    ->get(),
            ];

            return $this->successResponse($realTime);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get real-time analytics: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Export analytics data
     */
    public function exportAnalytics(Request $request): JsonResponse
    {
        $request->validate([
            'format' => 'required|string|in:csv,xlsx,json',
            'data_type' => 'required|string|in:sessions,messages,visitors,agents',
        ]);

        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'view');
            if ($error) return $error;

            $startDate = $request->get('start_date', now()->subDays(30)->toDateString());
            $endDate = $request->get('end_date', now()->toDateString());

            $exportData = $this->analyticsService->exportData(
                $siteId,
                $request->data_type,
                $startDate,
                $endDate,
                $request->format
            );

            return response()->json([
                'success' => true,
                'download_url' => $exportData['url'],
                'filename' => $exportData['filename'],
                'size' => $exportData['size'],
            ]);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to export analytics: ' . $e->getMessage(), 500);
        }
    }
}

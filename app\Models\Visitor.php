<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Visitor extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'visitor_id',
        'site_id',
        'name',
        'email',
        'phone',
        'ip_address',
        'user_agent',
        'country',
        'city',
        'timezone',
        'language',
        'first_visit_at',
        'last_visit_at',
        'total_visits',
        'total_page_views',
        'referrer',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'device_type',
        'browser',
        'os',
        'screen_resolution',
        'is_returning',
        'gdpr_consent',
        'gdpr_consent_at',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'first_visit_at' => 'datetime',
        'last_visit_at' => 'datetime',
        'total_visits' => 'integer',
        'total_page_views' => 'integer',
        'is_returning' => 'boolean',
        'gdpr_consent' => 'boolean',
        'gdpr_consent_at' => 'datetime',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($visitor) {
            if (!$visitor->visitor_id) {
                $visitor->visitor_id = Str::uuid();
            }
            if (!$visitor->first_visit_at) {
                $visitor->first_visit_at = now();
            }
            if (!$visitor->last_visit_at) {
                $visitor->last_visit_at = now();
            }
            if (!$visitor->total_visits) {
                $visitor->total_visits = 1;
            }
        });
    }

    /**
     * Get the site that owns the visitor
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Get the chat sessions for the visitor
     */
    public function chatSessions()
    {
        return $this->hasMany(ChatSession::class);
    }

    /**
     * Get the contact messages for the visitor
     */
    public function contactMessages()
    {
        return $this->hasMany(ContactMessage::class);
    }

    /**
     * Get the visitor tracking data
     */
    public function visitorTracking()
    {
        return $this->hasMany(VisitorTracking::class);
    }

    /**
     * Get active chat session
     */
    public function activeChatSession()
    {
        return $this->hasOne(ChatSession::class)
            ->whereIn('status', ['waiting', 'active'])
            ->latest();
    }

    /**
     * Check if visitor has active chat session
     */
    public function hasActiveChatSession()
    {
        return $this->activeChatSession()->exists();
    }

    /**
     * Get visitor's location string
     */
    public function getLocationAttribute()
    {
        $location = [];
        
        if ($this->city) {
            $location[] = $this->city;
        }
        
        if ($this->country) {
            $location[] = $this->country;
        }
        
        return implode(', ', $location) ?: 'Unknown';
    }

    /**
     * Get visitor's display name
     */
    public function getDisplayNameAttribute()
    {
        return $this->name ?: 'Anonymous Visitor';
    }

    /**
     * Get avatar URL
     */
    public function getAvatarUrlAttribute()
    {
        if ($this->name) {
            $initials = collect(explode(' ', $this->name))->map(function ($name) {
                return strtoupper(substr($name, 0, 1));
            })->join('');
            
            return "https://ui-avatars.com/api/?name={$initials}&background=random&color=fff&size=128";
        }
        
        return "https://ui-avatars.com/api/?name=V&background=6c757d&color=fff&size=128";
    }

    /**
     * Get device info
     */
    public function getDeviceInfoAttribute()
    {
        $info = [];
        
        if ($this->device_type) {
            $info[] = ucfirst($this->device_type);
        }
        
        if ($this->browser) {
            $info[] = $this->browser;
        }
        
        if ($this->os) {
            $info[] = $this->os;
        }
        
        return implode(' • ', $info) ?: 'Unknown';
    }

    /**
     * Update visit information
     */
    public function updateVisit($pageUrl = null, $referrer = null)
    {
        $this->increment('total_visits');
        
        if ($pageUrl) {
            $this->increment('total_page_views');
        }
        
        $this->update([
            'last_visit_at' => now(),
            'is_returning' => $this->total_visits > 1,
        ]);

        // Track page view
        if ($pageUrl) {
            $this->visitorTracking()->create([
                'tracking_id' => Str::uuid(),
                'page_url' => $pageUrl,
                'referrer' => $referrer,
                'visit_duration' => 0,
                'page_views' => 1,
                'visited_at' => now(),
            ]);
        }

        return $this;
    }

    /**
     * Give GDPR consent
     */
    public function giveGdprConsent()
    {
        $this->update([
            'gdpr_consent' => true,
            'gdpr_consent_at' => now(),
        ]);

        return $this;
    }

    /**
     * Revoke GDPR consent
     */
    public function revokeGdprConsent()
    {
        $this->update([
            'gdpr_consent' => false,
            'gdpr_consent_at' => null,
        ]);

        return $this;
    }

    /**
     * Get visitor statistics
     */
    public function getStatistics()
    {
        return [
            'total_visits' => $this->total_visits,
            'total_page_views' => $this->total_page_views,
            'total_chat_sessions' => $this->chatSessions()->count(),
            'active_chat_sessions' => $this->chatSessions()->whereIn('status', ['waiting', 'active'])->count(),
            'completed_chat_sessions' => $this->chatSessions()->where('status', 'closed')->count(),
            'total_contact_messages' => $this->contactMessages()->count(),
            'first_visit' => $this->first_visit_at,
            'last_visit' => $this->last_visit_at,
            'is_returning' => $this->is_returning,
            'has_gdpr_consent' => $this->gdpr_consent,
        ];
    }

    /**
     * Get recent activity
     */
    public function getRecentActivity($limit = 10)
    {
        $activities = collect();

        // Add chat sessions
        $chatSessions = $this->chatSessions()
            ->with('user')
            ->latest()
            ->limit($limit)
            ->get()
            ->map(function ($session) {
                return [
                    'type' => 'chat_session',
                    'title' => 'Chat Session',
                    'description' => $session->user ? "with {$session->user->name}" : 'waiting for agent',
                    'status' => $session->status,
                    'created_at' => $session->created_at,
                ];
            });

        $activities = $activities->merge($chatSessions);

        // Add contact messages
        $contactMessages = $this->contactMessages()
            ->latest()
            ->limit($limit)
            ->get()
            ->map(function ($message) {
                return [
                    'type' => 'contact_message',
                    'title' => 'Contact Message',
                    'description' => Str::limit($message->message, 100),
                    'status' => $message->status,
                    'created_at' => $message->created_at,
                ];
            });

        $activities = $activities->merge($contactMessages);

        return $activities->sortByDesc('created_at')->take($limit)->values();
    }

    /**
     * Scope for visitors with GDPR consent
     */
    public function scopeWithGdprConsent($query)
    {
        return $query->where('gdpr_consent', true);
    }

    /**
     * Scope for returning visitors
     */
    public function scopeReturning($query)
    {
        return $query->where('is_returning', true);
    }

    /**
     * Scope for visitors by country
     */
    public function scopeByCountry($query, $country)
    {
        return $query->where('country', $country);
    }

    /**
     * Scope for visitors by device type
     */
    public function scopeByDeviceType($query, $deviceType)
    {
        return $query->where('device_type', $deviceType);
    }

    /**
     * Scope for recent visitors
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('last_visit_at', '>=', now()->subDays($days));
    }

    /**
     * Convert visitor to array for API response
     */
    public function toApiArray()
    {
        return [
            'id' => $this->id,
            'visitor_id' => $this->visitor_id,
            'name' => $this->display_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'location' => $this->location,
            'device_info' => $this->device_info,
            'language' => $this->language,
            'timezone' => $this->timezone,
            'is_returning' => $this->is_returning,
            'total_visits' => $this->total_visits,
            'total_page_views' => $this->total_page_views,
            'first_visit_at' => $this->first_visit_at->toISOString(),
            'last_visit_at' => $this->last_visit_at->toISOString(),
            'has_active_chat' => $this->hasActiveChatSession(),
            'gdpr_consent' => $this->gdpr_consent,
            'avatar_url' => $this->avatar_url,
        ];
    }
}

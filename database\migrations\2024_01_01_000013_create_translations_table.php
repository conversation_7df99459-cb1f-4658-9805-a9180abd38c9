<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTranslationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('site_id')->nullable()->constrained()->onDelete('cascade'); // null for global translations
            $table->string('language_code', 10);
            $table->string('key'); // Translation key
            $table->text('value'); // Translation value
            $table->string('group')->nullable(); // For organizing translations
            $table->boolean('is_system')->default(false); // System vs custom translations
            $table->timestamps();
            
            $table->foreign('language_code')->references('code')->on('languages')->onDelete('cascade');
            $table->unique(['site_id', 'language_code', 'key']);
            $table->index(['language_code', 'group']);
            $table->index(['key', 'is_system']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('translations');
    }
}

<?php

namespace Database\Factories;

use App\Models\Site;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class SiteFactory extends Factory
{
    protected $model = Site::class;

    public function definition()
    {
        $name = $this->faker->company();
        
        return [
            'name' => $name,
            'domain' => $this->faker->domainName(),
            'description' => $this->faker->paragraph(),
            'logo' => null,
            'favicon' => null,
            'primary_color' => $this->faker->hexColor(),
            'secondary_color' => $this->faker->hexColor(),
            'accent_color' => $this->faker->hexColor(),
            'font_family' => $this->faker->randomElement(['Arial', 'Helvetica', 'Times New Roman', 'Georgia']),
            'language' => $this->faker->randomElement(['en', 'zh', 'es', 'fr', 'de']),
            'timezone' => $this->faker->timezone(),
            'currency' => $this->faker->currencyCode(),
            'date_format' => $this->faker->randomElement(['Y-m-d', 'd/m/Y', 'm/d/Y']),
            'time_format' => $this->faker->randomElement(['H:i', 'h:i A']),
            'working_hours' => json_encode([
                'monday' => ['start' => '09:00', 'end' => '17:00'],
                'tuesday' => ['start' => '09:00', 'end' => '17:00'],
                'wednesday' => ['start' => '09:00', 'end' => '17:00'],
                'thursday' => ['start' => '09:00', 'end' => '17:00'],
                'friday' => ['start' => '09:00', 'end' => '17:00'],
                'saturday' => null,
                'sunday' => null,
            ]),
            'offline_message' => $this->faker->sentence(),
            'welcome_message' => $this->faker->sentence(),
            'chat_widget_settings' => json_encode([
                'position' => 'bottom-right',
                'theme' => 'light',
                'show_agent_avatar' => true,
                'show_typing_indicator' => true,
                'enable_file_upload' => true,
                'enable_emoji' => true,
                'max_file_size' => 10,
                'allowed_file_types' => ['jpg', 'png', 'pdf', 'doc'],
            ]),
            'notification_settings' => json_encode([
                'email_notifications' => true,
                'sound_notifications' => true,
                'desktop_notifications' => true,
                'webhook_url' => null,
            ]),
            'gdpr_settings' => json_encode([
                'enabled' => true,
                'cookie_consent' => true,
                'data_retention_days' => 365,
                'privacy_policy_url' => $this->faker->url(),
                'terms_of_service_url' => $this->faker->url(),
            ]),
            'analytics_settings' => json_encode([
                'track_visitors' => true,
                'track_page_views' => true,
                'track_events' => true,
                'retention_days' => 90,
            ]),
            'is_active' => true,
            'api_key' => Str::random(32),
            'webhook_secret' => Str::random(32),
        ];
    }

    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => false,
            ];
        });
    }

    public function withCustomColors()
    {
        return $this->state(function (array $attributes) {
            return [
                'primary_color' => '#007bff',
                'secondary_color' => '#6c757d',
                'accent_color' => '#28a745',
            ];
        });
    }
}

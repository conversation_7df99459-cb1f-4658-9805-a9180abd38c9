<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\ChatController;
use App\Http\Controllers\Api\VisitorController;
use App\Http\Controllers\Api\ContactController;
use App\Http\Controllers\Api\WebhookController;
use App\Http\Controllers\Api\AnalyticsController;
use App\Http\Controllers\Api\ConfigurationController;
use App\Http\Controllers\Api\FileUploadController;
use App\Http\Controllers\Api\NotificationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Public routes (no authentication required)
Route::prefix('v1')->group(function () {
    // Authentication routes
    Route::post('/auth/login', [AuthController::class, 'login']);
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::post('/auth/refresh', [AuthController::class, 'refresh']);

    // Visitor routes (public)
    Route::prefix('visitor')->group(function () {
        Route::post('/init', [VisitorController::class, 'initSession']);
        Route::post('/contact', [ContactController::class, 'store']);
        Route::get('/config/{site_id}', [ConfigurationController::class, 'getPublicConfig']);
        Route::post('/tracking', [VisitorController::class, 'trackBehavior']);
    });

    // Chat routes for visitors (no auth required)
    Route::prefix('chat')->group(function () {
        Route::post('/start', [ChatController::class, 'startSession']);
        Route::get('/{session_id}/messages', [ChatController::class, 'getMessages']);
        Route::post('/{session_id}/messages', [ChatController::class, 'sendMessage']);
        Route::post('/{session_id}/typing', [ChatController::class, 'updateTyping']);
        Route::post('/{session_id}/end', [ChatController::class, 'endSession']);
        Route::post('/{session_id}/rating', [ChatController::class, 'submitRating']);
        Route::post('/{session_id}/feedback', [ChatController::class, 'submitFeedback']);
    });

    // Webhook routes
    Route::prefix('webhooks')->group(function () {
        Route::post('/chat/message', [WebhookController::class, 'handleChatMessage']);
        Route::post('/chat/session', [WebhookController::class, 'handleChatSession']);
        Route::post('/visitor/behavior', [WebhookController::class, 'handleVisitorBehavior']);
        Route::post('/system/notification', [WebhookController::class, 'handleSystemNotification']);
    });

    // File upload (with rate limiting)
    Route::post('/upload', [FileUploadController::class, 'upload'])->middleware('throttle:10,1');
});

// Protected routes (authentication required)
Route::prefix('v1')->middleware(['auth:sanctum'])->group(function () {
    // User profile routes
    Route::get('/user', [AuthController::class, 'user']);
    Route::put('/user/profile', [AuthController::class, 'updateProfile']);
    Route::put('/user/password', [AuthController::class, 'updatePassword']);
    Route::put('/user/status', [AuthController::class, 'updateStatus']);
    Route::get('/user/permissions', [AuthController::class, 'getPermissions']);

    // Admin routes (role-based access)
    Route::prefix('admin')->middleware(['role:admin,supervisor,agent'])->group(function () {
        // Dashboard and overview
        Route::get('/dashboard/overview', [AnalyticsController::class, 'getDashboardOverview']);
        Route::get('/dashboard/stats', [AnalyticsController::class, 'getDashboardStats']);
        Route::get('/system/status', [AnalyticsController::class, 'getSystemStatus']);

        // Agent workspace
        Route::prefix('workspace')->group(function () {
            Route::get('/overview', [ChatController::class, 'getWorkspaceOverview']);
            Route::get('/sessions', [ChatController::class, 'getAgentSessions']);
            Route::post('/sessions/{session_id}/accept', [ChatController::class, 'acceptSession']);
            Route::post('/sessions/{session_id}/transfer', [ChatController::class, 'transferSession']);
            Route::post('/sessions/{session_id}/end', [ChatController::class, 'endSessionByAgent']);
            Route::post('/sessions/{session_id}/notes', [ChatController::class, 'addInternalNote']);
            Route::get('/sessions/{session_id}', [ChatController::class, 'getSessionDetails']);
        });

        // Chat management
        Route::prefix('chats')->group(function () {
            Route::get('/', [ChatController::class, 'index']);
            Route::get('/{session_id}', [ChatController::class, 'show']);
            Route::get('/{session_id}/messages', [ChatController::class, 'getSessionMessages']);
            Route::get('/{session_id}/export', [ChatController::class, 'exportSession']);
            Route::delete('/{session_id}', [ChatController::class, 'deleteSession'])->middleware('role:admin,supervisor');
        });

        // Analytics and reporting
        Route::prefix('analytics')->group(function () {
            Route::get('/overview', [AnalyticsController::class, 'getOverview']);
            Route::get('/chat-metrics', [AnalyticsController::class, 'getChatMetrics']);
            Route::get('/visitor-metrics', [AnalyticsController::class, 'getVisitorMetrics']);
            Route::get('/agent-performance', [AnalyticsController::class, 'getAgentPerformance']);
            Route::get('/satisfaction-ratings', [AnalyticsController::class, 'getSatisfactionRatings']);
            Route::get('/response-times', [AnalyticsController::class, 'getResponseTimes']);
            Route::get('/export', [AnalyticsController::class, 'exportAnalytics']);
        });

        // Visitor tracking and behavior
        Route::prefix('visitors')->group(function () {
            Route::get('/', [VisitorController::class, 'index']);
            Route::get('/{visitor_id}', [VisitorController::class, 'show']);
            Route::get('/{visitor_id}/sessions', [VisitorController::class, 'getSessions']);
            Route::get('/{visitor_id}/behavior', [VisitorController::class, 'getBehavior']);
            Route::delete('/{visitor_id}', [VisitorController::class, 'deleteVisitor'])->middleware('role:admin');
        });

        // Contact messages
        Route::prefix('contacts')->group(function () {
            Route::get('/', [ContactController::class, 'index']);
            Route::get('/{contact_id}', [ContactController::class, 'show']);
            Route::put('/{contact_id}/status', [ContactController::class, 'updateStatus']);
            Route::post('/{contact_id}/reply', [ContactController::class, 'reply']);
            Route::delete('/{contact_id}', [ContactController::class, 'destroy'])->middleware('role:admin');
        });

        // Notifications
        Route::prefix('notifications')->group(function () {
            Route::get('/', [NotificationController::class, 'index']);
            Route::post('/mark-read', [NotificationController::class, 'markAsRead']);
            Route::post('/mark-all-read', [NotificationController::class, 'markAllAsRead']);
            Route::delete('/{notification_id}', [NotificationController::class, 'destroy']);
        });

        // Available agents (for transfers)
        Route::get('/agents/available', [AuthController::class, 'getAvailableAgents']);
    });

    // Admin-only routes
    Route::prefix('admin')->middleware(['role:admin'])->group(function () {
        // User management
        Route::prefix('users')->group(function () {
            Route::get('/', [AuthController::class, 'getUsers']);
            Route::post('/', [AuthController::class, 'createUser']);
            Route::get('/{user_id}', [AuthController::class, 'getUser']);
            Route::put('/{user_id}', [AuthController::class, 'updateUser']);
            Route::delete('/{user_id}', [AuthController::class, 'deleteUser']);
            Route::put('/{user_id}/status', [AuthController::class, 'updateUserStatus']);
            Route::put('/{user_id}/permissions', [AuthController::class, 'updateUserPermissions']);
        });

        // Site management
        Route::prefix('sites')->group(function () {
            Route::get('/', [ConfigurationController::class, 'getSites']);
            Route::post('/', [ConfigurationController::class, 'createSite']);
            Route::get('/{site_id}', [ConfigurationController::class, 'getSite']);
            Route::put('/{site_id}', [ConfigurationController::class, 'updateSite']);
            Route::delete('/{site_id}', [ConfigurationController::class, 'deleteSite']);
            Route::get('/{site_id}/widget-code', [ConfigurationController::class, 'getWidgetCode']);
        });

        // System configuration
        Route::prefix('config')->group(function () {
            Route::get('/', [ConfigurationController::class, 'getSystemConfig']);
            Route::put('/', [ConfigurationController::class, 'updateSystemConfig']);
            Route::get('/themes', [ConfigurationController::class, 'getThemes']);
            Route::post('/themes', [ConfigurationController::class, 'createTheme']);
            Route::put('/themes/{theme_id}', [ConfigurationController::class, 'updateTheme']);
            Route::delete('/themes/{theme_id}', [ConfigurationController::class, 'deleteTheme']);
            Route::get('/languages', [ConfigurationController::class, 'getLanguages']);
            Route::put('/languages/{language_code}', [ConfigurationController::class, 'updateLanguage']);
        });

        // System maintenance
        Route::prefix('system')->group(function () {
            Route::post('/cleanup', [AnalyticsController::class, 'cleanupOldData']);
            Route::post('/backup', [AnalyticsController::class, 'createBackup']);
            Route::get('/logs', [AnalyticsController::class, 'getSystemLogs']);
            Route::post('/cache/clear', [AnalyticsController::class, 'clearCache']);
        });
    });
});

// Supervisor and Admin routes
Route::prefix('v1/admin')->middleware(['auth:sanctum', 'role:admin,supervisor'])->group(function () {
    // Advanced analytics
    Route::prefix('reports')->group(function () {
        Route::get('/detailed-analytics', [AnalyticsController::class, 'getDetailedAnalytics']);
        Route::get('/agent-reports', [AnalyticsController::class, 'getAgentReports']);
        Route::get('/visitor-reports', [AnalyticsController::class, 'getVisitorReports']);
        Route::get('/satisfaction-reports', [AnalyticsController::class, 'getSatisfactionReports']);
        Route::post('/custom-report', [AnalyticsController::class, 'generateCustomReport']);
    });

    // Bulk operations
    Route::prefix('bulk')->group(function () {
        Route::post('/sessions/export', [ChatController::class, 'bulkExportSessions']);
        Route::post('/visitors/cleanup', [VisitorController::class, 'bulkCleanupVisitors']);
        Route::post('/contacts/update-status', [ContactController::class, 'bulkUpdateStatus']);
    });
});

// Health check and status endpoints
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'version' => config('app.version', '1.0.0')
    ]);
});

Route::get('/status', function () {
    return response()->json([
        'database' => 'connected',
        'redis' => 'connected',
        'websocket' => 'running',
        'storage' => 'available'
    ]);
});

// Public API Routes (for widget integration)
Route::prefix('v1')->group(function () {
    
    // Chat API
    Route::prefix('chat')->group(function () {
        Route::post('/{site}/start', 'Api\ChatController@startSession');
        Route::post('/{site}/message', 'Api\ChatController@sendMessage');
        Route::get('/{site}/messages', 'Api\ChatController@getMessages');
        Route::post('/{site}/upload', 'Api\ChatController@uploadFile');
        Route::get('/{site}/status', 'Api\ChatController@getStatus');
        Route::post('/{site}/end', 'Api\ChatController@endSession');
    });
    
    // Contact Form API
    Route::prefix('contact')->group(function () {
        Route::post('/{site}', 'Api\ContactController@store');
    });
    
    // Visitor Tracking API
    Route::prefix('tracking')->group(function () {
        Route::post('/{site}/page-view', 'TrackingController@trackPageView');
        Route::post('/{site}/mouse-movement', 'TrackingController@trackMouseMovement');
        Route::post('/{site}/click', 'TrackingController@trackClick');
        Route::post('/{site}/form-event', 'TrackingController@trackFormEvent');
        Route::post('/{site}/scroll', 'TrackingController@trackScroll');
        Route::post('/{site}/end-session', 'TrackingController@endSession');
    });
    
    // Site Configuration API
    Route::get('/{site}/config', 'Api\SiteController@getConfig');
});

// Authenticated API Routes
Route::middleware(['auth:sanctum'])->prefix('v1')->group(function () {
    
    // Dashboard API
    Route::get('/dashboard/stats', 'Api\DashboardController@stats');
    Route::get('/dashboard/recent-sessions', 'Api\DashboardController@recentSessions');
    
    // Chat Management API
    Route::prefix('chat')->group(function () {
        Route::get('sessions', 'ChatController@index');
        Route::get('sessions/{sessionId}', 'ChatController@show');
        Route::post('sessions/{sessionId}/messages', 'ChatController@sendMessage');
        Route::post('sessions/{sessionId}/files', 'ChatController@uploadFile');
        Route::post('sessions/{sessionId}/assign', 'ChatController@assignAgent');
        Route::post('sessions/{sessionId}/transfer', 'ChatController@transferSession');
        Route::post('sessions/{sessionId}/close', 'ChatController@closeSession');
        Route::post('sessions/{sessionId}/typing', 'ChatController@sendTyping');
    });

    // Work Mode Management API
    Route::prefix('work-mode')->group(function () {
        Route::get('sites/{siteId}/status', 'WorkModeController@getSiteStatus');
        Route::get('sites/{siteId}/working-hours', 'WorkModeController@getWorkingHours');
        Route::put('sites/{siteId}/working-hours', 'WorkModeController@updateWorkingHours');
        Route::get('sites/{siteId}/statistics', 'WorkModeController@getStatistics');
        Route::get('sites/{siteId}/offline-template', 'WorkModeController@getOfflineTemplate');

        // Contact messages
        Route::get('contact-messages', 'WorkModeController@getContactMessages');
        Route::get('sites/{siteId}/contact-messages', 'WorkModeController@getContactMessages');
        Route::get('contact-messages/{messageId}', 'WorkModeController@getContactMessage');
        Route::post('contact-messages/{messageId}/assign', 'WorkModeController@assignContactMessage');
        Route::post('contact-messages/{messageId}/respond', 'WorkModeController@respondToContactMessage');
        Route::put('contact-messages/{messageId}/status', 'WorkModeController@updateContactMessageStatus');
    });

    // Legacy Admin Chat API (for backward compatibility)
    Route::prefix('admin/chat')->group(function () {
        Route::get('/sessions', 'Api\Admin\ChatController@sessions');
        Route::get('/sessions/{session}', 'Api\Admin\ChatController@getSession');
        Route::post('/sessions/{session}/message', 'Api\Admin\ChatController@sendMessage');
        Route::post('/sessions/{session}/transfer', 'Api\Admin\ChatController@transferSession');
        Route::post('/sessions/{session}/close', 'Api\Admin\ChatController@closeSession');
        Route::get('/templates', 'Api\Admin\ChatController@getTemplates');
        Route::post('/templates', 'Api\Admin\ChatController@storeTemplate');
    });
    
    // Analytics API
    Route::prefix('analytics')->group(function () {
        Route::get('sites/{siteId}/overview', 'AnalyticsController@getOverview');
        Route::get('sites/{siteId}/device-stats', 'AnalyticsController@getDeviceStats');
        Route::get('sites/{siteId}/geographic-stats', 'AnalyticsController@getGeographicStats');
        Route::get('sites/{siteId}/page-stats', 'AnalyticsController@getPageStats');
        Route::get('sites/{siteId}/click-heatmap', 'AnalyticsController@getClickHeatmap');
        Route::get('sites/{siteId}/mouse-heatmap', 'AnalyticsController@getMouseHeatmap');
        Route::get('sites/{siteId}/forms/{formId}/analytics', 'AnalyticsController@getFormAnalytics');
        Route::get('sites/{siteId}/real-time', 'AnalyticsController@getRealTimeActivity');
        Route::get('sites/{siteId}/engagement', 'AnalyticsController@getEngagementMetrics');
        Route::get('sites/{siteId}/visitors/{visitorId}/journey', 'AnalyticsController@getVisitorJourney');
        Route::post('sites/{siteId}/conversion-funnel', 'AnalyticsController@getConversionFunnel');
        Route::get('sites/{siteId}/most-clicked-elements', 'AnalyticsController@getMostClickedElements');
        Route::post('sites/{siteId}/clear-cache', 'AnalyticsController@clearCache');

        // Legacy routes for backward compatibility
        Route::get('/overview', 'Api\AnalyticsController@overview');
        Route::get('/sessions', 'Api\AnalyticsController@sessions');
        Route::get('/visitors', 'Api\AnalyticsController@visitors');
        Route::get('/response-times', 'Api\AnalyticsController@responseTimes');
        Route::get('/satisfaction', 'Api\AnalyticsController@satisfaction');
        Route::get('/export', 'Api\AnalyticsController@export');
    });

    // Statistics API
    Route::prefix('statistics')->group(function () {
        Route::get('sites/{siteId}/dashboard', 'StatisticsController@getDashboardOverview');
        Route::get('sites/{siteId}/sessions', 'StatisticsController@getSessionStatistics');
        Route::get('sites/{siteId}/agents', 'StatisticsController@getAgentPerformance');
        Route::get('sites/{siteId}/response-times', 'StatisticsController@getResponseTimeDistribution');
        Route::get('sites/{siteId}/satisfaction', 'StatisticsController@getSatisfactionDistribution');
        Route::get('sites/{siteId}/messages', 'StatisticsController@getMessageVolumeStats');
        Route::get('sites/{siteId}/report', 'StatisticsController@getAnalyticsReport');
        Route::get('sites/{siteId}/conversion-funnel', 'StatisticsController@getConversionFunnel');
        Route::get('sites/{siteId}/peak-hours', 'StatisticsController@getPeakHours');
        Route::get('sites/{siteId}/real-time', 'StatisticsController@getRealTimeStats');
        Route::post('sites/{siteId}/clear-cache', 'StatisticsController@clearCache');

        // Global statistics (all sites)
        Route::get('dashboard', 'StatisticsController@getDashboardOverview');
        Route::get('sessions', 'StatisticsController@getSessionStatistics');
        Route::get('agents', 'StatisticsController@getAgentPerformance');
        Route::get('response-times', 'StatisticsController@getResponseTimeDistribution');
        Route::get('satisfaction', 'StatisticsController@getSatisfactionDistribution');
        Route::get('messages', 'StatisticsController@getMessageVolumeStats');
        Route::get('report', 'StatisticsController@getAnalyticsReport');
        Route::get('conversion-funnel', 'StatisticsController@getConversionFunnel');
        Route::get('peak-hours', 'StatisticsController@getPeakHours');
        Route::get('real-time', 'StatisticsController@getRealTimeStats');
        Route::post('clear-cache', 'StatisticsController@clearCache');
    });

    // Export API
    Route::prefix('export')->group(function () {
        Route::post('sites/{siteId}/chat-sessions', 'ExportController@exportChatSessions');
        Route::post('sites/{siteId}/chat-messages', 'ExportController@exportChatMessages');
        Route::post('sites/{siteId}/visitor-analytics', 'ExportController@exportVisitorAnalytics');
        Route::post('sites/{siteId}/comprehensive-report', 'ExportController@exportComprehensiveReport');

        // Global exports (all sites)
        Route::post('chat-sessions', 'ExportController@exportChatSessions');
        Route::post('chat-messages', 'ExportController@exportChatMessages');
        Route::post('visitor-analytics', 'ExportController@exportVisitorAnalytics');
        Route::post('comprehensive-report', 'ExportController@exportComprehensiveReport');

        // File management
        Route::post('download', 'ExportController@downloadFile');
        Route::get('history', 'ExportController@getExportHistory');
        Route::delete('files', 'ExportController@deleteExportFile');
        Route::post('cleanup', 'ExportController@cleanupOldExports');
        Route::get('stats', 'ExportController@getExportStats');
    });

    // Theme API
    Route::prefix('themes')->group(function () {
        Route::get('sites/{siteId}', 'ThemeController@index');
        Route::get('sites/{siteId}/active', 'ThemeController@getActiveTheme');
        Route::post('sites/{siteId}', 'ThemeController@store');
        Route::get('{themeId}', 'ThemeController@show');
        Route::put('{themeId}', 'ThemeController@update');
        Route::post('{themeId}/activate', 'ThemeController@activate');
        Route::post('{themeId}/clone', 'ThemeController@clone');
        Route::delete('{themeId}', 'ThemeController@destroy');
        Route::post('{themeId}/logo', 'ThemeController@uploadLogo');
        Route::post('{themeId}/favicon', 'ThemeController@uploadFavicon');
        Route::get('{themeId}/css', 'ThemeController@generateCss');
        Route::get('{themeId}/preview', 'ThemeController@preview');
        Route::get('{themeId}/export', 'ThemeController@export');

        // Predefined themes
        Route::get('predefined/list', 'ThemeController@getPredefinedThemes');
        Route::post('sites/{siteId}/predefined', 'ThemeController@applyPredefinedTheme');
        Route::post('sites/{siteId}/import', 'ThemeController@import');

        // Global themes (all sites)
        Route::get('/', 'ThemeController@index');
    });

    // Custom Components API
    Route::prefix('components')->group(function () {
        Route::get('sites/{siteId}', 'CustomComponentController@index');
        Route::get('sites/{siteId}/position/{position}', 'CustomComponentController@getByPosition');
        Route::post('sites/{siteId}', 'CustomComponentController@store');
        Route::get('{componentId}', 'CustomComponentController@show');
        Route::put('{componentId}', 'CustomComponentController@update');
        Route::post('{componentId}/toggle', 'CustomComponentController@toggle');
        Route::post('{componentId}/clone', 'CustomComponentController@clone');
        Route::delete('{componentId}', 'CustomComponentController@destroy');
        Route::post('sites/{siteId}/position/{position}/reorder', 'CustomComponentController@reorder');
        Route::get('sites/{siteId}/position/{position}/render', 'CustomComponentController@render');
        Route::get('sites/{siteId}/css', 'CustomComponentController@generateCss');
        Route::get('sites/{siteId}/export', 'CustomComponentController@export');
        Route::post('sites/{siteId}/import', 'CustomComponentController@import');

        // Component metadata
        Route::get('types/available', 'CustomComponentController@getAvailableTypes');
        Route::get('positions/available', 'CustomComponentController@getAvailablePositions');
        Route::get('templates/list', 'CustomComponentController@getTemplates');
        Route::post('sites/{siteId}/from-template', 'CustomComponentController@createFromTemplate');

        // Global components (all sites)
        Route::get('/', 'CustomComponentController@index');
    });

    // Internationalization API
    Route::prefix('i18n')->group(function () {
        // Languages management
        Route::get('sites/{siteId}/languages', 'InternationalizationController@getLanguages');
        Route::get('sites/{siteId}/languages/default', 'InternationalizationController@getDefaultLanguage');
        Route::post('sites/{siteId}/languages', 'InternationalizationController@createLanguage');
        Route::put('languages/{languageId}', 'InternationalizationController@updateLanguage');
        Route::delete('languages/{languageId}', 'InternationalizationController@deleteLanguage');
        Route::get('languages/available', 'InternationalizationController@getAvailableLanguages');

        // Translations management
        Route::get('sites/{siteId}/languages/{languageCode}/translations', 'InternationalizationController@getAllTranslations');
        Route::get('sites/{siteId}/languages/{languageCode}/translations/{key}', 'InternationalizationController@getTranslation');
        Route::post('sites/{siteId}/languages/{languageCode}/translations', 'InternationalizationController@setTranslation');
        Route::post('sites/{siteId}/languages/{languageCode}/translations/bulk', 'InternationalizationController@bulkSetTranslations');

        // Import/Export
        Route::get('sites/{siteId}/languages/{languageCode}/export', 'InternationalizationController@exportTranslations');
        Route::post('sites/{siteId}/languages/{languageCode}/import', 'InternationalizationController@importTranslations');

        // Statistics
        Route::get('sites/{siteId}/translations/stats', 'InternationalizationController@getTranslationStats');
        Route::get('sites/{siteId}/languages/{languageCode}/stats', 'InternationalizationController@getTranslationStats');
    });

    // Admin Management API
    Route::prefix('admin')->middleware(['admin'])->group(function () {
        // Dashboard
        Route::get('dashboard/overview', 'Admin\AdminController@getDashboardOverview');
        Route::get('system/status', 'Admin\AdminController@getSystemStatus');

        // Agent Workspace
        Route::prefix('workspace')->group(function () {
            Route::get('overview', 'Admin\AgentWorkspaceController@getWorkspaceOverview');
            Route::put('status', 'Admin\AgentWorkspaceController@updateAgentStatus');
            Route::post('chats/{sessionId}/accept', 'Admin\AgentWorkspaceController@acceptChat');
            Route::post('chats/{sessionId}/transfer', 'Admin\AgentWorkspaceController@transferChat');
            Route::post('chats/{sessionId}/end', 'Admin\AgentWorkspaceController@endChat');
            Route::get('chats/{sessionId}', 'Admin\AgentWorkspaceController@getChatSession');
            Route::get('visitors/{visitorId}', 'Admin\AgentWorkspaceController@getVisitorInfo');
            Route::post('chats/{sessionId}/notes', 'Admin\AgentWorkspaceController@addInternalNote');
            Route::get('performance', 'Admin\AgentWorkspaceController@getAgentPerformance');
        });

        // Analytics
        Route::prefix('analytics')->group(function () {
            Route::get('dashboard', 'Admin\AnalyticsController@getDashboardAnalytics');
            Route::get('realtime', 'Admin\AnalyticsController@getRealTimeAnalytics');
            Route::post('export', 'Admin\AnalyticsController@exportAnalytics');
        });

        // System Configuration
        Route::prefix('config')->group(function () {
            // Site Configuration
            Route::get('site', 'Admin\SystemConfigController@getSiteConfig');
            Route::put('site', 'Admin\SystemConfigController@updateSiteConfig');

            // Chat Widget Configuration
            Route::get('widget', 'Admin\SystemConfigController@getChatWidgetConfig');
            Route::put('widget', 'Admin\SystemConfigController@updateChatWidgetConfig');

            // Working Hours
            Route::get('working-hours', 'Admin\SystemConfigController@getWorkingHours');
            Route::put('working-hours', 'Admin\SystemConfigController@updateWorkingHours');

            // User Management
            Route::get('users', 'Admin\SystemConfigController@getUsers');
            Route::post('users', 'Admin\SystemConfigController@createUser');
            Route::put('users/{userId}', 'Admin\SystemConfigController@updateUser');
            Route::delete('users/{userId}', 'Admin\SystemConfigController@deleteUser');

            // File Upload
            Route::post('upload/logo', 'Admin\SystemConfigController@uploadLogo');

            // System Settings
            Route::get('system', 'Admin\SystemConfigController@getSystemSettings');
        });
    });
    
    // Site Management API
    Route::prefix('sites')->group(function () {
        Route::get('/', 'Api\SiteController@index');
        Route::post('/', 'Api\SiteController@store');
        Route::get('/{site}', 'Api\SiteController@show');
        Route::put('/{site}', 'Api\SiteController@update');
        Route::delete('/{site}', 'Api\SiteController@destroy');
        Route::post('/{site}/widget-code', 'Api\SiteController@generateWidgetCode');
    });
    
    // Settings API
    Route::prefix('settings')->group(function () {
        Route::get('/', 'Api\SettingsController@index');
        Route::post('/working-hours', 'Api\SettingsController@updateWorkingHours');
        Route::post('/appearance', 'Api\SettingsController@updateAppearance');
        Route::post('/notifications', 'Api\SettingsController@updateNotifications');
        Route::post('/languages', 'Api\SettingsController@updateLanguages');
    });
    
    // User Management API
    Route::prefix('users')->group(function () {
        Route::get('/', 'Api\UserController@index');
        Route::post('/', 'Api\UserController@store');
        Route::get('/{user}', 'Api\UserController@show');
        Route::put('/{user}', 'Api\UserController@update');
        Route::delete('/{user}', 'Api\UserController@destroy');
        Route::post('/{user}/status', 'Api\UserController@updateStatus');
    });
});

// Webhook API Routes
Route::prefix('webhooks')->group(function () {
    Route::post('/chat/message', 'Api\WebhookController@chatMessage');
    Route::post('/visitor/action', 'Api\WebhookController@visitorAction');
    Route::post('/session/status', 'Api\WebhookController@sessionStatus');
});

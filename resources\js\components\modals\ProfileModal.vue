<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <form @submit.prevent="updateProfile">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                <UserCircleIcon class="h-6 w-6 text-blue-600" />
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                  Profile Settings
                </h3>
                <div class="mt-4 space-y-4">
                  <!-- Avatar -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700">Profile Picture</label>
                    <div class="mt-1 flex items-center space-x-4">
                      <img :src="form.avatar || '/images/default-avatar.png'" 
                           :alt="form.name" 
                           class="h-16 w-16 rounded-full object-cover">
                      <div>
                        <input
                          ref="avatarInput"
                          type="file"
                          accept="image/*"
                          @change="handleAvatarChange"
                          class="sr-only"
                        />
                        <button
                          type="button"
                          @click="$refs.avatarInput.click()"
                          class="btn btn-outline btn-sm"
                        >
                          Change Photo
                        </button>
                      </div>
                    </div>
                  </div>

                  <!-- Name -->
                  <div>
                    <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
                    <input
                      id="name"
                      v-model="form.name"
                      type="text"
                      required
                      class="mt-1 form-input"
                      :class="{ 'border-red-300': errors.name }"
                    />
                    <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
                  </div>

                  <!-- Email -->
                  <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                    <input
                      id="email"
                      v-model="form.email"
                      type="email"
                      required
                      class="mt-1 form-input"
                      :class="{ 'border-red-300': errors.email }"
                    />
                    <p v-if="errors.email" class="mt-1 text-sm text-red-600">{{ errors.email }}</p>
                  </div>

                  <!-- Phone -->
                  <div>
                    <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
                    <input
                      id="phone"
                      v-model="form.phone"
                      type="tel"
                      class="mt-1 form-input"
                      :class="{ 'border-red-300': errors.phone }"
                    />
                    <p v-if="errors.phone" class="mt-1 text-sm text-red-600">{{ errors.phone }}</p>
                  </div>

                  <!-- Timezone -->
                  <div>
                    <label for="timezone" class="block text-sm font-medium text-gray-700">Timezone</label>
                    <select
                      id="timezone"
                      v-model="form.timezone"
                      class="mt-1 form-select"
                    >
                      <option v-for="tz in timezones" :key="tz.value" :value="tz.value">
                        {{ tz.label }}
                      </option>
                    </select>
                  </div>

                  <!-- Language -->
                  <div>
                    <label for="language" class="block text-sm font-medium text-gray-700">Preferred Language</label>
                    <select
                      id="language"
                      v-model="form.language"
                      class="mt-1 form-select"
                    >
                      <option v-for="lang in languages" :key="lang.code" :value="lang.code">
                        {{ lang.name }}
                      </option>
                    </select>
                  </div>

                  <!-- Notification preferences -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Notification Preferences</label>
                    <div class="space-y-2">
                      <label class="flex items-center">
                        <input
                          v-model="form.notifications.email"
                          type="checkbox"
                          class="form-checkbox h-4 w-4 text-blue-600"
                        />
                        <span class="ml-2 text-sm text-gray-700">Email notifications</span>
                      </label>
                      <label class="flex items-center">
                        <input
                          v-model="form.notifications.browser"
                          type="checkbox"
                          class="form-checkbox h-4 w-4 text-blue-600"
                        />
                        <span class="ml-2 text-sm text-gray-700">Browser notifications</span>
                      </label>
                      <label class="flex items-center">
                        <input
                          v-model="form.notifications.sound"
                          type="checkbox"
                          class="form-checkbox h-4 w-4 text-blue-600"
                        />
                        <span class="ml-2 text-sm text-gray-700">Sound notifications</span>
                      </label>
                    </div>
                  </div>

                  <!-- Change password section -->
                  <div class="border-t pt-4">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">Change Password</h4>
                    <div class="space-y-3">
                      <div>
                        <label for="current_password" class="block text-sm font-medium text-gray-700">Current Password</label>
                        <input
                          id="current_password"
                          v-model="form.current_password"
                          type="password"
                          class="mt-1 form-input"
                          :class="{ 'border-red-300': errors.current_password }"
                        />
                        <p v-if="errors.current_password" class="mt-1 text-sm text-red-600">{{ errors.current_password }}</p>
                      </div>
                      <div>
                        <label for="new_password" class="block text-sm font-medium text-gray-700">New Password</label>
                        <input
                          id="new_password"
                          v-model="form.new_password"
                          type="password"
                          class="mt-1 form-input"
                          :class="{ 'border-red-300': errors.new_password }"
                        />
                        <p v-if="errors.new_password" class="mt-1 text-sm text-red-600">{{ errors.new_password }}</p>
                      </div>
                      <div>
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700">Confirm New Password</label>
                        <input
                          id="confirm_password"
                          v-model="form.confirm_password"
                          type="password"
                          class="mt-1 form-input"
                          :class="{ 'border-red-300': errors.confirm_password }"
                        />
                        <p v-if="errors.confirm_password" class="mt-1 text-sm text-red-600">{{ errors.confirm_password }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="submit"
              :disabled="isLoading"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
            >
              <span v-if="isLoading" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Updating...
              </span>
              <span v-else>Update Profile</span>
            </button>
            <button
              @click="$emit('close')"
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useAuthStore } from '../../stores/auth.js'
import { useNotificationStore } from '../../stores/notifications.js'
import { UserCircleIcon } from '@heroicons/vue/24/outline'

export default {
  name: 'ProfileModal',
  components: {
    UserCircleIcon
  },
  emits: ['close'],
  setup(props, { emit }) {
    const authStore = useAuthStore()
    const notificationStore = useNotificationStore()

    const isLoading = ref(false)
    const errors = reactive({})

    const form = reactive({
      name: '',
      email: '',
      phone: '',
      avatar: '',
      timezone: 'UTC',
      language: 'en',
      notifications: {
        email: true,
        browser: true,
        sound: true
      },
      current_password: '',
      new_password: '',
      confirm_password: ''
    })

    const timezones = [
      { value: 'UTC', label: 'UTC' },
      { value: 'America/New_York', label: 'Eastern Time (ET)' },
      { value: 'America/Chicago', label: 'Central Time (CT)' },
      { value: 'America/Denver', label: 'Mountain Time (MT)' },
      { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
      { value: 'Europe/London', label: 'London (GMT)' },
      { value: 'Europe/Paris', label: 'Paris (CET)' },
      { value: 'Asia/Tokyo', label: 'Tokyo (JST)' },
      { value: 'Asia/Shanghai', label: 'Shanghai (CST)' },
      { value: 'Australia/Sydney', label: 'Sydney (AEST)' }
    ]

    const languages = [
      { code: 'en', name: 'English' },
      { code: 'es', name: 'Español' },
      { code: 'fr', name: 'Français' },
      { code: 'de', name: 'Deutsch' },
      { code: 'it', name: 'Italiano' },
      { code: 'pt', name: 'Português' },
      { code: 'ru', name: 'Русский' },
      { code: 'ja', name: '日本語' },
      { code: 'ko', name: '한국어' },
      { code: 'zh', name: '中文' }
    ]

    const validateForm = () => {
      Object.keys(errors).forEach(key => delete errors[key])

      if (!form.name.trim()) {
        errors.name = 'Name is required'
      }

      if (!form.email.trim()) {
        errors.email = 'Email is required'
      } else if (!/\S+@\S+\.\S+/.test(form.email)) {
        errors.email = 'Email is invalid'
      }

      // Password validation (only if changing password)
      if (form.current_password || form.new_password || form.confirm_password) {
        if (!form.current_password) {
          errors.current_password = 'Current password is required'
        }
        if (!form.new_password) {
          errors.new_password = 'New password is required'
        } else if (form.new_password.length < 6) {
          errors.new_password = 'Password must be at least 6 characters'
        }
        if (form.new_password !== form.confirm_password) {
          errors.confirm_password = 'Passwords do not match'
        }
      }

      return Object.keys(errors).length === 0
    }

    const handleAvatarChange = (event) => {
      const file = event.target.files[0]
      if (file) {
        // Create preview URL
        const reader = new FileReader()
        reader.onload = (e) => {
          form.avatar = e.target.result
        }
        reader.readAsDataURL(file)
      }
    }

    const updateProfile = async () => {
      if (!validateForm()) {
        return
      }

      try {
        isLoading.value = true

        const result = await authStore.updateProfile(form)

        if (result.success) {
          notificationStore.success('Profile Updated', 'Your profile has been updated successfully')
          emit('close')
        } else {
          if (result.errors) {
            Object.assign(errors, result.errors)
          } else {
            notificationStore.error('Error', result.message)
          }
        }
      } catch (error) {
        console.error('Failed to update profile:', error)
        notificationStore.error('Error', 'Failed to update profile')
      } finally {
        isLoading.value = false
      }
    }

    onMounted(() => {
      // Load current user data
      const user = authStore.user
      if (user) {
        form.name = user.name || ''
        form.email = user.email || ''
        form.phone = user.phone || ''
        form.avatar = user.avatar || ''
        form.timezone = user.timezone || 'UTC'
        form.language = user.language || 'en'
        
        if (user.notification_preferences) {
          Object.assign(form.notifications, user.notification_preferences)
        }
      }
    })

    return {
      isLoading,
      errors,
      form,
      timezones,
      languages,
      handleAvatarChange,
      updateProfile
    }
  }
}
</script>

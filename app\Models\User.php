<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'avatar',
        'role',
        'status',
        'is_active',
        'permissions',
        'timezone',
        'language',
        'max_concurrent_chats',
        'last_activity_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'permissions' => 'array',
        'is_active' => 'boolean',
        'last_activity_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the sites that the user has access to
     */
    public function sites()
    {
        return $this->belongsToMany(Site::class, 'site_users')
                    ->withPivot('role', 'is_active', 'permissions')
                    ->withTimestamps();
    }

    /**
     * Get the chat sessions assigned to this user
     */
    public function chatSessions()
    {
        return $this->hasMany(ChatSession::class);
    }

    /**
     * Get the chat messages sent by this user
     */
    public function chatMessages()
    {
        return $this->hasMany(ChatMessage::class);
    }

    /**
     * Get the contact messages assigned to this user
     */
    public function contactMessages()
    {
        return $this->hasMany(ContactMessage::class, 'assigned_user_id');
    }

    /**
     * Get the chat templates created by this user
     */
    public function chatTemplates()
    {
        return $this->hasMany(ChatTemplate::class);
    }

    /**
     * Check if user has permission
     */
    public function hasPermission($permission, $siteId = null)
    {
        // Super admin has all permissions
        if ($this->role === 'admin' && !$siteId) {
            return true;
        }

        // Check global permissions
        if (is_array($this->permissions) && in_array($permission, $this->permissions)) {
            return true;
        }

        // Check site-specific permissions
        if ($siteId) {
            $siteUser = $this->sites()->where('site_id', $siteId)->first();
            if ($siteUser && $siteUser->pivot->is_active) {
                $sitePermissions = $siteUser->pivot->permissions ?? [];
                if (is_array($sitePermissions) && in_array($permission, $sitePermissions)) {
                    return true;
                }
                
                // Check role-based permissions
                return $this->hasRolePermission($siteUser->pivot->role, $permission);
            }
        }

        return false;
    }

    /**
     * Check if user has role-based permission
     */
    public function hasRolePermission($role, $permission)
    {
        $rolePermissions = [
            'admin' => [
                'manage_sites',
                'manage_users',
                'manage_settings',
                'view_analytics',
                'manage_chat_sessions',
                'manage_contact_messages',
                'manage_templates',
                'manage_webhooks',
            ],
            'supervisor' => [
                'view_analytics',
                'manage_chat_sessions',
                'manage_contact_messages',
                'manage_templates',
                'assign_sessions',
                'transfer_sessions',
            ],
            'agent' => [
                'handle_chat_sessions',
                'send_messages',
                'upload_files',
                'use_templates',
                'view_visitor_info',
            ]
        ];

        return isset($rolePermissions[$role]) && in_array($permission, $rolePermissions[$role]);
    }

    /**
     * Check if user can access site
     */
    public function canAccessSite($siteId)
    {
        return $this->sites()->where('site_id', $siteId)->where('is_active', true)->exists();
    }

    /**
     * Get user's role for a specific site
     */
    public function getSiteRole($siteId)
    {
        $siteUser = $this->sites()->where('site_id', $siteId)->first();
        return $siteUser ? $siteUser->pivot->role : null;
    }

    /**
     * Check if user is online
     */
    public function isOnline()
    {
        return $this->status === 'online' && 
               $this->last_activity_at && 
               $this->last_activity_at->gt(now()->subMinutes(5));
    }

    /**
     * Update last activity
     */
    public function updateLastActivity()
    {
        $this->update(['last_activity_at' => now()]);
    }

    /**
     * Get active chat sessions count
     */
    public function getActiveChatSessionsCount($siteId = null)
    {
        $query = $this->chatSessions()->where('status', 'active');
        
        if ($siteId) {
            $query->where('site_id', $siteId);
        }
        
        return $query->count();
    }

    /**
     * Check if user can handle more chat sessions
     */
    public function canHandleMoreChats($siteId = null)
    {
        return $this->getActiveChatSessionsCount($siteId) < $this->max_concurrent_chats;
    }

    /**
     * Scope for active users
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for online users
     */
    public function scopeOnline($query)
    {
        return $query->where('status', 'online')
                    ->where('last_activity_at', '>', now()->subMinutes(5));
    }

    /**
     * Scope for users with specific role
     */
    public function scopeWithRole($query, $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Get avatar URL
     */
    public function getAvatarUrlAttribute()
    {
        if ($this->avatar) {
            return asset('storage/' . $this->avatar);
        }
        
        // Generate default avatar using initials
        $initials = collect(explode(' ', $this->name))->map(function ($name) {
            return strtoupper(substr($name, 0, 1));
        })->join('');
        
        return "https://ui-avatars.com/api/?name={$initials}&background=random&color=fff&size=128";
    }

    /**
     * Get full name with role
     */
    public function getDisplayNameAttribute()
    {
        return $this->name . ' (' . ucfirst($this->role) . ')';
    }
}

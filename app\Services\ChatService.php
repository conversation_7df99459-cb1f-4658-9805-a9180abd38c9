<?php

namespace App\Services;

use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\Visitor;
use App\Models\Site;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Str;

class ChatService
{
    protected $webSocketService;

    public function __construct(WebSocketService $webSocketService)
    {
        $this->webSocketService = $webSocketService;
    }

    /**
     * Start a new chat session
     */
    public function startChatSession(Site $site, Visitor $visitor, string $initialMessage = null, array $metadata = [])
    {
        try {
            DB::beginTransaction();

            // Check if visitor already has an active session
            $existingSession = $visitor->activeChatSession();
            if ($existingSession) {
                DB::rollBack();
                return $existingSession;
            }

            // Create new chat session
            $session = ChatSession::create([
                'site_id' => $site->id,
                'visitor_id' => $visitor->id,
                'status' => 'waiting',
                'initial_message' => $initialMessage,
                'initial_page_url' => $metadata['page_url'] ?? null,
                'initial_referrer' => $metadata['referrer'] ?? null,
                'metadata' => $metadata,
            ]);

            // Add initial message if provided
            if ($initialMessage) {
                $this->addMessage($session, $initialMessage, 'visitor');
            }

            // Try to auto-assign an available agent
            $this->autoAssignAgent($session);

            // Notify agents about new session
            $this->webSocketService->notifyNewChatSession($session);

            DB::commit();

            Log::info('Chat session started', [
                'session_id' => $session->session_id,
                'site_id' => $site->id,
                'visitor_id' => $visitor->visitor_id,
            ]);

            return $session;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to start chat session', [
                'site_id' => $site->id,
                'visitor_id' => $visitor->visitor_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Add message to chat session
     */
    public function addMessage(ChatSession $session, string $content, string $senderType, User $user = null, array $options = [])
    {
        try {
            $message = ChatMessage::create([
                'chat_session_id' => $session->id,
                'user_id' => $user ? $user->id : null,
                'content' => $content,
                'sender_type' => $senderType,
                'type' => $options['type'] ?? 'text',
                'parent_message_id' => $options['parent_message_id'] ?? null,
                'file_path' => $options['file_path'] ?? null,
                'file_name' => $options['file_name'] ?? null,
                'file_size' => $options['file_size'] ?? null,
                'file_type' => $options['file_type'] ?? null,
                'metadata' => $options['metadata'] ?? null,
            ]);

            // Broadcast message to session participants
            $this->webSocketService->broadcastToSession($session->session_id, [
                'type' => 'new_message',
                'message' => $message->toApiArray(),
            ]);

            // Update session last activity
            $session->touch();

            Log::info('Message added to chat session', [
                'session_id' => $session->session_id,
                'message_id' => $message->message_id,
                'sender_type' => $senderType,
            ]);

            return $message;

        } catch (\Exception $e) {
            Log::error('Failed to add message to chat session', [
                'session_id' => $session->session_id,
                'sender_type' => $senderType,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Upload file and add as message
     */
    public function uploadFileMessage(ChatSession $session, UploadedFile $file, string $senderType, User $user = null, string $content = null)
    {
        try {
            // Validate file
            $this->validateFile($file, $session->site);

            // Store file
            $filePath = $file->store('chat_files/' . date('Y/m'), 'public');
            
            $options = [
                'type' => 'file',
                'file_path' => $filePath,
                'file_name' => $file->getClientOriginalName(),
                'file_size' => $file->getSize(),
                'file_type' => $file->getMimeType(),
            ];

            return $this->addMessage($session, $content ?: '', $senderType, $user, $options);

        } catch (\Exception $e) {
            Log::error('Failed to upload file message', [
                'session_id' => $session->session_id,
                'file_name' => $file->getClientOriginalName(),
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Assign agent to chat session
     */
    public function assignAgent(ChatSession $session, User $agent)
    {
        try {
            DB::beginTransaction();

            // Check if agent can handle more chats
            if (!$agent->canHandleMoreChats($session->site_id)) {
                throw new \Exception('Agent has reached maximum concurrent chats');
            }

            // Assign agent
            $session->assignAgent($agent);

            // Add system message
            $this->addMessage($session, "Agent {$agent->name} joined the conversation", 'system');

            // Notify about assignment
            $this->webSocketService->notifySessionAssignment($session, $agent);

            DB::commit();

            Log::info('Agent assigned to chat session', [
                'session_id' => $session->session_id,
                'agent_id' => $agent->id,
                'agent_name' => $agent->name,
            ]);

            return $session;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to assign agent to chat session', [
                'session_id' => $session->session_id,
                'agent_id' => $agent->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Transfer chat session to another agent
     */
    public function transferSession(ChatSession $session, User $newAgent, string $reason = null)
    {
        try {
            DB::beginTransaction();

            $oldAgent = $session->user;

            // Check if new agent can handle more chats
            if (!$newAgent->canHandleMoreChats($session->site_id)) {
                throw new \Exception('Target agent has reached maximum concurrent chats');
            }

            // Transfer session
            $session->transferTo($newAgent, $reason);

            // Notify about transfer
            $this->webSocketService->broadcastToSession($session->session_id, [
                'type' => 'session_transferred',
                'old_agent' => $oldAgent ? $oldAgent->only(['id', 'name']) : null,
                'new_agent' => $newAgent->only(['id', 'name']),
                'reason' => $reason,
            ]);

            DB::commit();

            Log::info('Chat session transferred', [
                'session_id' => $session->session_id,
                'old_agent_id' => $oldAgent ? $oldAgent->id : null,
                'new_agent_id' => $newAgent->id,
                'reason' => $reason,
            ]);

            return $session;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to transfer chat session', [
                'session_id' => $session->session_id,
                'new_agent_id' => $newAgent->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Close chat session
     */
    public function closeSession(ChatSession $session, string $reason = null, int $rating = null, string $feedback = null)
    {
        try {
            DB::beginTransaction();

            // Close session
            $session->close($reason, $rating, $feedback);

            // Calculate final statistics
            $session->calculateAverageResponseTime();

            // Notify about closure
            $this->webSocketService->broadcastToSession($session->session_id, [
                'type' => 'session_closed',
                'reason' => $reason,
                'rating' => $rating,
                'feedback' => $feedback,
            ]);

            DB::commit();

            Log::info('Chat session closed', [
                'session_id' => $session->session_id,
                'reason' => $reason,
                'rating' => $rating,
            ]);

            return $session;

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Failed to close chat session', [
                'session_id' => $session->session_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Auto-assign available agent to session
     */
    public function autoAssignAgent(ChatSession $session)
    {
        if (!$session->site->getWidgetSetting('auto_assign_agents', true)) {
            return null;
        }

        $availableAgents = $session->site->getAvailableAgents();
        
        if ($availableAgents->isEmpty()) {
            return null;
        }

        // Simple round-robin assignment (can be enhanced with load balancing)
        $agent = $availableAgents->first();
        
        try {
            $this->assignAgent($session, $agent);
            return $agent;
        } catch (\Exception $e) {
            Log::warning('Auto-assignment failed', [
                'session_id' => $session->session_id,
                'agent_id' => $agent->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Mark messages as read
     */
    public function markMessagesAsRead(ChatSession $session, User $user = null, string $senderType = null)
    {
        try {
            $query = $session->messages()->where('is_read', false);
            
            if ($senderType) {
                $query->where('sender_type', '!=', $senderType);
            }
            
            $query->update([
                'is_read' => true,
                'read_at' => now(),
            ]);

            Log::info('Messages marked as read', [
                'session_id' => $session->session_id,
                'user_id' => $user ? $user->id : null,
                'sender_type' => $senderType,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to mark messages as read', [
                'session_id' => $session->session_id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get chat session with messages
     */
    public function getChatSessionWithMessages(string $sessionId, int $limit = 50, int $offset = 0)
    {
        $session = ChatSession::where('session_id', $sessionId)
            ->with(['site', 'visitor', 'user'])
            ->firstOrFail();

        $messages = $session->messages()
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->offset($offset)
            ->get()
            ->reverse()
            ->values();

        return [
            'session' => $session,
            'messages' => $messages,
        ];
    }

    /**
     * Validate uploaded file
     */
    protected function validateFile(UploadedFile $file, Site $site)
    {
        $settings = $site->file_upload_settings ?? [];
        
        $maxSize = $settings['max_size'] ?? 10 * 1024 * 1024; // 10MB default
        $allowedTypes = $settings['allowed_types'] ?? ['image/*', 'application/pdf', 'text/*'];
        
        if ($file->getSize() > $maxSize) {
            throw new \Exception('File size exceeds maximum allowed size');
        }
        
        $mimeType = $file->getMimeType();
        $isAllowed = false;
        
        foreach ($allowedTypes as $allowedType) {
            if (fnmatch($allowedType, $mimeType)) {
                $isAllowed = true;
                break;
            }
        }
        
        if (!$isAllowed) {
            throw new \Exception('File type not allowed');
        }
    }

    /**
     * Send typing notification
     */
    public function sendTypingNotification(ChatSession $session, User $user = null, string $senderType = 'agent', bool $isTyping = true)
    {
        $this->webSocketService->notifyTypingStatus(
            $session->session_id,
            $user ? $user->id : null,
            $isTyping,
            $senderType
        );
    }

    /**
     * Clean up old sessions
     */
    public function cleanupOldSessions(int $daysOld = 30)
    {
        try {
            $cutoffDate = now()->subDays($daysOld);

            $sessions = ChatSession::where('status', 'closed')
                ->where('ended_at', '<', $cutoffDate)
                ->get();

            foreach ($sessions as $session) {
                // Delete associated files
                $messages = $session->messages()->whereNotNull('file_path')->get();
                foreach ($messages as $message) {
                    if ($message->file_path && Storage::disk('public')->exists($message->file_path)) {
                        Storage::disk('public')->delete($message->file_path);
                    }
                }

                // Soft delete session and messages
                $session->messages()->delete();
                $session->delete();
            }

            Log::info('Cleaned up old chat sessions', [
                'sessions_cleaned' => $sessions->count(),
                'cutoff_date' => $cutoffDate,
            ]);

            return $sessions->count();

        } catch (\Exception $e) {
            Log::error('Failed to cleanup old sessions', [
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }
}

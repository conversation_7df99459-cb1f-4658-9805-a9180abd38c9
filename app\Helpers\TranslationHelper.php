<?php

namespace App\Helpers;

use App\Services\InternationalizationService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Request;

class TranslationHelper
{
    protected static $i18nService;
    protected static $cache = [];

    /**
     * Get translation service instance
     */
    protected static function getI18nService()
    {
        if (!static::$i18nService) {
            static::$i18nService = app(InternationalizationService::class);
        }
        return static::$i18nService;
    }

    /**
     * Get current site ID from request
     */
    protected static function getCurrentSiteId()
    {
        $request = Request::instance();
        return $request->attributes->get('site_id') ?? $request->get('site_id');
    }

    /**
     * Get current language code
     */
    protected static function getCurrentLanguageCode()
    {
        $request = Request::instance();
        return $request->attributes->get('language_code') ?? App::getLocale();
    }

    /**
     * Translate a key
     */
    public static function translate($key, $default = null, array $replacements = [], $siteId = null, $languageCode = null)
    {
        $siteId = $siteId ?? static::getCurrentSiteId();
        $languageCode = $languageCode ?? static::getCurrentLanguageCode();
        
        if (!$siteId || !$languageCode) {
            return $default ?? $key;
        }

        // Check cache first
        $cacheKey = "{$siteId}.{$languageCode}.{$key}";
        if (isset(static::$cache[$cacheKey])) {
            $translation = static::$cache[$cacheKey];
        } else {
            $translation = static::getI18nService()->getTranslation(
                $siteId, 
                $languageCode, 
                $key, 
                $default
            );
            static::$cache[$cacheKey] = $translation;
        }

        // Apply replacements
        if (!empty($replacements)) {
            foreach ($replacements as $search => $replace) {
                $translation = str_replace(':' . $search, $replace, $translation);
            }
        }

        return $translation;
    }

    /**
     * Translate with pluralization
     */
    public static function translatePlural($key, $count, array $replacements = [], $siteId = null, $languageCode = null)
    {
        $replacements['count'] = $count;
        
        // Try to get plural form first
        $pluralKey = $count === 1 ? $key . '_singular' : $key . '_plural';
        $translation = static::translate($pluralKey, null, $replacements, $siteId, $languageCode);
        
        // If plural form not found, use base key
        if ($translation === $pluralKey) {
            $translation = static::translate($key, null, $replacements, $siteId, $languageCode);
        }
        
        return $translation;
    }

    /**
     * Get all translations for current language
     */
    public static function getAllTranslations($namespace = null, $group = null, $siteId = null, $languageCode = null)
    {
        $siteId = $siteId ?? static::getCurrentSiteId();
        $languageCode = $languageCode ?? static::getCurrentLanguageCode();
        
        if (!$siteId || !$languageCode) {
            return [];
        }

        return static::getI18nService()->getAllTranslations($siteId, $languageCode, $namespace, $group);
    }

    /**
     * Format date according to current language settings
     */
    public static function formatDate($date, $format = null, $siteId = null, $languageCode = null)
    {
        $siteId = $siteId ?? static::getCurrentSiteId();
        $languageCode = $languageCode ?? static::getCurrentLanguageCode();
        
        if (!$siteId || !$languageCode) {
            return $date->format($format ?? 'Y-m-d');
        }

        $language = static::getI18nService()->getLanguageByCode($siteId, $languageCode);
        if (!$language) {
            return $date->format($format ?? 'Y-m-d');
        }

        return $language->formatDate($date, $format);
    }

    /**
     * Format time according to current language settings
     */
    public static function formatTime($time, $format = null, $siteId = null, $languageCode = null)
    {
        $siteId = $siteId ?? static::getCurrentSiteId();
        $languageCode = $languageCode ?? static::getCurrentLanguageCode();
        
        if (!$siteId || !$languageCode) {
            return $time->format($format ?? 'H:i:s');
        }

        $language = static::getI18nService()->getLanguageByCode($siteId, $languageCode);
        if (!$language) {
            return $time->format($format ?? 'H:i:s');
        }

        return $language->formatTime($time, $format);
    }

    /**
     * Format number according to current language settings
     */
    public static function formatNumber($number, $decimals = 2, $siteId = null, $languageCode = null)
    {
        $siteId = $siteId ?? static::getCurrentSiteId();
        $languageCode = $languageCode ?? static::getCurrentLanguageCode();
        
        if (!$siteId || !$languageCode) {
            return number_format($number, $decimals);
        }

        $language = static::getI18nService()->getLanguageByCode($siteId, $languageCode);
        if (!$language) {
            return number_format($number, $decimals);
        }

        return $language->formatNumber($number, $decimals);
    }

    /**
     * Format currency according to current language settings
     */
    public static function formatCurrency($amount, $showSymbol = true, $siteId = null, $languageCode = null)
    {
        $siteId = $siteId ?? static::getCurrentSiteId();
        $languageCode = $languageCode ?? static::getCurrentLanguageCode();
        
        if (!$siteId || !$languageCode) {
            return '$' . number_format($amount, 2);
        }

        $language = static::getI18nService()->getLanguageByCode($siteId, $languageCode);
        if (!$language) {
            return '$' . number_format($amount, 2);
        }

        return $language->formatCurrency($amount, $showSymbol);
    }

    /**
     * Get text direction for current language
     */
    public static function getTextDirection($siteId = null, $languageCode = null)
    {
        $siteId = $siteId ?? static::getCurrentSiteId();
        $languageCode = $languageCode ?? static::getCurrentLanguageCode();
        
        if (!$siteId || !$languageCode) {
            return 'ltr';
        }

        $language = static::getI18nService()->getLanguageByCode($siteId, $languageCode);
        if (!$language) {
            return 'ltr';
        }

        return $language->getTextDirection();
    }

    /**
     * Check if current language is RTL
     */
    public static function isRtl($siteId = null, $languageCode = null)
    {
        return static::getTextDirection($siteId, $languageCode) === 'rtl';
    }

    /**
     * Get current language information
     */
    public static function getCurrentLanguage($siteId = null, $languageCode = null)
    {
        $siteId = $siteId ?? static::getCurrentSiteId();
        $languageCode = $languageCode ?? static::getCurrentLanguageCode();
        
        if (!$siteId || !$languageCode) {
            return null;
        }

        return static::getI18nService()->getLanguageByCode($siteId, $languageCode);
    }

    /**
     * Get available languages for current site
     */
    public static function getAvailableLanguages($activeOnly = true, $siteId = null)
    {
        $siteId = $siteId ?? static::getCurrentSiteId();
        
        if (!$siteId) {
            return [];
        }

        return static::getI18nService()->getLanguagesForSite($siteId, $activeOnly);
    }

    /**
     * Clear translation cache
     */
    public static function clearCache()
    {
        static::$cache = [];
    }

    /**
     * Generate language switcher data
     */
    public static function getLanguageSwitcherData($siteId = null)
    {
        $siteId = $siteId ?? static::getCurrentSiteId();
        $currentLanguageCode = static::getCurrentLanguageCode();
        
        if (!$siteId) {
            return [];
        }

        $languages = static::getAvailableLanguages(true, $siteId);
        $switcherData = [];

        foreach ($languages as $language) {
            $switcherData[] = [
                'code' => $language->code,
                'name' => $language->name,
                'native_name' => $language->native_name,
                'flag_icon' => $language->flag_icon,
                'is_current' => $language->code === $currentLanguageCode,
                'is_rtl' => $language->is_rtl,
            ];
        }

        return $switcherData;
    }
}

// Global helper functions
if (!function_exists('__t')) {
    /**
     * Translate a key (shorthand function)
     */
    function __t($key, $default = null, array $replacements = [])
    {
        return \App\Helpers\TranslationHelper::translate($key, $default, $replacements);
    }
}

if (!function_exists('__tp')) {
    /**
     * Translate with pluralization (shorthand function)
     */
    function __tp($key, $count, array $replacements = [])
    {
        return \App\Helpers\TranslationHelper::translatePlural($key, $count, $replacements);
    }
}

if (!function_exists('__tf')) {
    /**
     * Format number according to current language (shorthand function)
     */
    function __tf($number, $decimals = 2)
    {
        return \App\Helpers\TranslationHelper::formatNumber($number, $decimals);
    }
}

if (!function_exists('__tc')) {
    /**
     * Format currency according to current language (shorthand function)
     */
    function __tc($amount, $showSymbol = true)
    {
        return \App\Helpers\TranslationHelper::formatCurrency($amount, $showSymbol);
    }
}

if (!function_exists('__td')) {
    /**
     * Format date according to current language (shorthand function)
     */
    function __td($date, $format = null)
    {
        return \App\Helpers\TranslationHelper::formatDate($date, $format);
    }
}

if (!function_exists('__tt')) {
    /**
     * Format time according to current language (shorthand function)
     */
    function __tt($time, $format = null)
    {
        return \App\Helpers\TranslationHelper::formatTime($time, $format);
    }
}

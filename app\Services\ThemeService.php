<?php

namespace App\Services;

use App\Models\Theme;
use App\Models\Site;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\UploadedFile;
use Intervention\Image\Facades\Image;

class ThemeService
{
    /**
     * Get active theme for site
     */
    public function getActiveTheme($siteId)
    {
        $cacheKey = "active_theme_{$siteId}";
        
        return Cache::remember($cacheKey, 3600, function () use ($siteId) {
            $theme = Theme::where('site_id', $siteId)
                ->active()
                ->first();
            
            if (!$theme) {
                // Create default theme if none exists
                $theme = $this->createDefaultTheme($siteId);
            }
            
            return $theme;
        });
    }

    /**
     * Create default theme for site
     */
    public function createDefaultTheme($siteId)
    {
        $site = Site::find($siteId);
        
        $theme = new Theme([
            'site_id' => $siteId,
            'name' => 'Default Theme',
            'description' => 'Default theme for ' . ($site->name ?? 'site'),
            'is_active' => true,
            'is_default' => true,
        ]);
        
        $theme->applyDefaults();
        $theme->save();
        
        return $theme;
    }

    /**
     * Create new theme
     */
    public function createTheme($siteId, $data)
    {
        $theme = new Theme(array_merge($data, [
            'site_id' => $siteId,
            'is_active' => false,
            'is_default' => false,
        ]));
        
        $theme->applyDefaults();
        
        // Merge with provided settings
        if (isset($data['color_scheme'])) {
            $theme->color_scheme = array_merge($theme->color_scheme, $data['color_scheme']);
        }
        
        if (isset($data['typography'])) {
            $theme->typography = array_merge($theme->typography, $data['typography']);
        }
        
        if (isset($data['layout_settings'])) {
            $theme->layout_settings = array_merge($theme->layout_settings, $data['layout_settings']);
        }
        
        if (isset($data['button_styles'])) {
            $theme->button_styles = array_merge($theme->button_styles, $data['button_styles']);
        }
        
        if (isset($data['chat_bubble_styles'])) {
            $theme->chat_bubble_styles = array_merge($theme->chat_bubble_styles, $data['chat_bubble_styles']);
        }
        
        if (isset($data['animation_settings'])) {
            $theme->animation_settings = array_merge($theme->animation_settings, $data['animation_settings']);
        }
        
        if (isset($data['responsive_settings'])) {
            $theme->responsive_settings = array_merge($theme->responsive_settings, $data['responsive_settings']);
        }
        
        $theme->save();
        
        return $theme;
    }

    /**
     * Update theme
     */
    public function updateTheme($themeId, $data)
    {
        $theme = Theme::where('theme_id', $themeId)->firstOrFail();
        
        // Update basic fields
        $theme->fill(array_intersect_key($data, array_flip([
            'name', 'description', 'custom_css'
        ])));
        
        // Update complex fields by merging
        if (isset($data['color_scheme'])) {
            $theme->color_scheme = array_merge($theme->color_scheme ?? [], $data['color_scheme']);
        }
        
        if (isset($data['typography'])) {
            $theme->typography = array_merge($theme->typography ?? [], $data['typography']);
        }
        
        if (isset($data['layout_settings'])) {
            $theme->layout_settings = array_merge($theme->layout_settings ?? [], $data['layout_settings']);
        }
        
        if (isset($data['button_styles'])) {
            $theme->button_styles = array_merge($theme->button_styles ?? [], $data['button_styles']);
        }
        
        if (isset($data['chat_bubble_styles'])) {
            $theme->chat_bubble_styles = array_merge($theme->chat_bubble_styles ?? [], $data['chat_bubble_styles']);
        }
        
        if (isset($data['animation_settings'])) {
            $theme->animation_settings = array_merge($theme->animation_settings ?? [], $data['animation_settings']);
        }
        
        if (isset($data['responsive_settings'])) {
            $theme->responsive_settings = array_merge($theme->responsive_settings ?? [], $data['responsive_settings']);
        }
        
        if (isset($data['background_settings'])) {
            $theme->background_settings = array_merge($theme->background_settings ?? [], $data['background_settings']);
        }
        
        $theme->save();
        
        // Clear cache
        $this->clearThemeCache($theme->site_id);
        
        return $theme;
    }

    /**
     * Activate theme
     */
    public function activateTheme($themeId)
    {
        $theme = Theme::where('theme_id', $themeId)->firstOrFail();
        $theme->setAsActive();
        
        // Clear cache
        $this->clearThemeCache($theme->site_id);
        
        return $theme;
    }

    /**
     * Clone theme
     */
    public function cloneTheme($themeId, $newName = null)
    {
        $theme = Theme::where('theme_id', $themeId)->firstOrFail();
        $clone = $theme->cloneTheme($newName);
        
        return $clone;
    }

    /**
     * Upload logo
     */
    public function uploadLogo($themeId, UploadedFile $file)
    {
        $theme = Theme::where('theme_id', $themeId)->firstOrFail();
        
        // Validate file
        $this->validateImageFile($file);
        
        // Delete old logo if exists
        if ($theme->logo_url) {
            $this->deleteFile($theme->logo_url);
        }
        
        // Generate filename
        $filename = 'logos/' . $theme->theme_id . '_logo_' . time() . '.' . $file->getClientOriginalExtension();
        
        // Resize and optimize image
        $image = Image::make($file)
            ->resize(200, 200, function ($constraint) {
                $constraint->aspectRatio();
                $constraint->upsize();
            })
            ->encode('png', 90);
        
        // Store file
        Storage::put($filename, $image);
        
        // Update theme
        $theme->update(['logo_url' => Storage::url($filename)]);
        
        // Clear cache
        $this->clearThemeCache($theme->site_id);
        
        return $theme;
    }

    /**
     * Upload favicon
     */
    public function uploadFavicon($themeId, UploadedFile $file)
    {
        $theme = Theme::where('theme_id', $themeId)->firstOrFail();
        
        // Validate file
        $this->validateImageFile($file);
        
        // Delete old favicon if exists
        if ($theme->favicon_url) {
            $this->deleteFile($theme->favicon_url);
        }
        
        // Generate filename
        $filename = 'favicons/' . $theme->theme_id . '_favicon_' . time() . '.ico';
        
        // Resize and convert to ICO format
        $image = Image::make($file)
            ->resize(32, 32)
            ->encode('png', 90);
        
        // Store file
        Storage::put($filename, $image);
        
        // Update theme
        $theme->update(['favicon_url' => Storage::url($filename)]);
        
        // Clear cache
        $this->clearThemeCache($theme->site_id);
        
        return $theme;
    }

    /**
     * Generate theme CSS
     */
    public function generateThemeCss($themeId)
    {
        $theme = Theme::where('theme_id', $themeId)->firstOrFail();
        $css = $theme->generateCss();
        
        // Store CSS file
        $filename = "themes/{$theme->theme_id}.css";
        Storage::put($filename, $css);
        
        return [
            'css' => $css,
            'css_url' => Storage::url($filename),
        ];
    }

    /**
     * Get theme preview
     */
    public function getThemePreview($themeId)
    {
        $theme = Theme::where('theme_id', $themeId)->firstOrFail();
        
        return [
            'theme' => $theme,
            'css' => $theme->generateCss(),
            'preview_html' => $this->generatePreviewHtml($theme),
        ];
    }

    /**
     * Generate preview HTML
     */
    protected function generatePreviewHtml($theme)
    {
        $colorScheme = $theme->color_scheme ?? [];
        $typography = $theme->typography ?? [];
        $layoutSettings = $theme->layout_settings ?? [];
        $buttonStyles = $theme->button_styles ?? [];
        $chatBubbleStyles = $theme->chat_bubble_styles ?? [];
        
        return view('theme.preview', compact(
            'theme', 'colorScheme', 'typography', 'layoutSettings', 
            'buttonStyles', 'chatBubbleStyles'
        ))->render();
    }

    /**
     * Get predefined themes
     */
    public function getPredefinedThemes()
    {
        return [
            'modern_blue' => [
                'name' => 'Modern Blue',
                'description' => 'Clean and modern blue theme',
                'color_scheme' => [
                    'primary' => '#2563EB',
                    'secondary' => '#64748B',
                    'accent' => '#06B6D4',
                    'background' => '#FFFFFF',
                    'surface' => '#F8FAFC',
                    'text_primary' => '#0F172A',
                    'text_secondary' => '#64748B',
                ],
            ],
            'warm_orange' => [
                'name' => 'Warm Orange',
                'description' => 'Friendly warm orange theme',
                'color_scheme' => [
                    'primary' => '#EA580C',
                    'secondary' => '#78716C',
                    'accent' => '#F59E0B',
                    'background' => '#FFFBEB',
                    'surface' => '#FEF3C7',
                    'text_primary' => '#1C1917',
                    'text_secondary' => '#78716C',
                ],
            ],
            'nature_green' => [
                'name' => 'Nature Green',
                'description' => 'Fresh and natural green theme',
                'color_scheme' => [
                    'primary' => '#059669',
                    'secondary' => '#6B7280',
                    'accent' => '#10B981',
                    'background' => '#F0FDF4',
                    'surface' => '#DCFCE7',
                    'text_primary' => '#14532D',
                    'text_secondary' => '#6B7280',
                ],
            ],
            'elegant_purple' => [
                'name' => 'Elegant Purple',
                'description' => 'Sophisticated purple theme',
                'color_scheme' => [
                    'primary' => '#7C3AED',
                    'secondary' => '#6B7280',
                    'accent' => '#A855F7',
                    'background' => '#FAFAF9',
                    'surface' => '#F3F4F6',
                    'text_primary' => '#1F2937',
                    'text_secondary' => '#6B7280',
                ],
            ],
            'dark_mode' => [
                'name' => 'Dark Mode',
                'description' => 'Sleek dark theme',
                'color_scheme' => [
                    'primary' => '#3B82F6',
                    'secondary' => '#9CA3AF',
                    'accent' => '#10B981',
                    'background' => '#111827',
                    'surface' => '#1F2937',
                    'text_primary' => '#F9FAFB',
                    'text_secondary' => '#D1D5DB',
                ],
            ],
        ];
    }

    /**
     * Apply predefined theme
     */
    public function applyPredefinedTheme($siteId, $themeKey)
    {
        $predefinedThemes = $this->getPredefinedThemes();
        
        if (!isset($predefinedThemes[$themeKey])) {
            throw new \InvalidArgumentException('Invalid predefined theme key');
        }
        
        $themeData = $predefinedThemes[$themeKey];
        
        return $this->createTheme($siteId, $themeData);
    }

    /**
     * Validate image file
     */
    protected function validateImageFile(UploadedFile $file)
    {
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        $maxSize = 2 * 1024 * 1024; // 2MB
        
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            throw new \InvalidArgumentException('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.');
        }
        
        if ($file->getSize() > $maxSize) {
            throw new \InvalidArgumentException('File size too large. Maximum size is 2MB.');
        }
    }

    /**
     * Delete file from storage
     */
    protected function deleteFile($url)
    {
        $path = str_replace(Storage::url(''), '', $url);
        if (Storage::exists($path)) {
            Storage::delete($path);
        }
    }

    /**
     * Clear theme cache
     */
    public function clearThemeCache($siteId)
    {
        Cache::forget("active_theme_{$siteId}");
    }

    /**
     * Export theme
     */
    public function exportTheme($themeId)
    {
        $theme = Theme::where('theme_id', $themeId)->firstOrFail();
        
        $exportData = $theme->only([
            'name', 'description', 'color_scheme', 'typography', 
            'layout_settings', 'custom_css', 'background_settings',
            'button_styles', 'chat_bubble_styles', 'animation_settings',
            'responsive_settings', 'metadata'
        ]);
        
        $exportData['export_version'] = '1.0';
        $exportData['exported_at'] = now()->toISOString();
        
        return $exportData;
    }

    /**
     * Import theme
     */
    public function importTheme($siteId, $themeData)
    {
        // Validate import data
        if (!isset($themeData['name'])) {
            throw new \InvalidArgumentException('Theme name is required');
        }
        
        // Remove export metadata
        unset($themeData['export_version'], $themeData['exported_at']);
        
        return $this->createTheme($siteId, $themeData);
    }
}

<?php

namespace App\Services;

use App\Models\User;
use App\Models\Site;
use Illuminate\Support\Facades\Cache;

class PermissionService
{
    /**
     * Available permissions
     */
    const PERMISSIONS = [
        // Site management
        'manage_sites' => 'Manage sites',
        'create_sites' => 'Create sites',
        'edit_sites' => 'Edit sites',
        'delete_sites' => 'Delete sites',
        'view_sites' => 'View sites',

        // User management
        'manage_users' => 'Manage users',
        'create_users' => 'Create users',
        'edit_users' => 'Edit users',
        'delete_users' => 'Delete users',
        'view_users' => 'View users',
        'assign_users_to_sites' => 'Assign users to sites',

        // Chat session management
        'manage_chat_sessions' => 'Manage chat sessions',
        'handle_chat_sessions' => 'Handle chat sessions',
        'assign_sessions' => 'Assign sessions to agents',
        'transfer_sessions' => 'Transfer sessions between agents',
        'close_sessions' => 'Close chat sessions',
        'view_session_history' => 'View session history',

        // Messaging
        'send_messages' => 'Send messages',
        'receive_messages' => 'Receive messages',
        'upload_files' => 'Upload files in chat',
        'download_files' => 'Download files from chat',

        // Contact messages
        'manage_contact_messages' => 'Manage contact messages',
        'view_contact_messages' => 'View contact messages',
        'respond_to_contacts' => 'Respond to contact messages',

        // Templates
        'manage_templates' => 'Manage chat templates',
        'use_templates' => 'Use chat templates',
        'create_templates' => 'Create templates',
        'edit_templates' => 'Edit templates',
        'delete_templates' => 'Delete templates',

        // Analytics and reporting
        'view_analytics' => 'View analytics',
        'view_reports' => 'View reports',
        'export_data' => 'Export data',
        'view_visitor_info' => 'View visitor information',

        // System settings
        'manage_settings' => 'Manage system settings',
        'manage_working_hours' => 'Manage working hours',
        'manage_auto_messages' => 'Manage auto messages',
        'manage_themes' => 'Manage themes',

        // Webhooks
        'manage_webhooks' => 'Manage webhooks',
        'create_webhooks' => 'Create webhooks',
        'edit_webhooks' => 'Edit webhooks',
        'delete_webhooks' => 'Delete webhooks',

        // Languages and translations
        'manage_languages' => 'Manage languages',
        'manage_translations' => 'Manage translations',
    ];

    /**
     * Role-based permissions
     */
    const ROLE_PERMISSIONS = [
        'admin' => [
            'manage_sites', 'create_sites', 'edit_sites', 'delete_sites', 'view_sites',
            'manage_users', 'create_users', 'edit_users', 'delete_users', 'view_users', 'assign_users_to_sites',
            'manage_chat_sessions', 'handle_chat_sessions', 'assign_sessions', 'transfer_sessions', 'close_sessions', 'view_session_history',
            'send_messages', 'receive_messages', 'upload_files', 'download_files',
            'manage_contact_messages', 'view_contact_messages', 'respond_to_contacts',
            'manage_templates', 'use_templates', 'create_templates', 'edit_templates', 'delete_templates',
            'view_analytics', 'view_reports', 'export_data', 'view_visitor_info',
            'manage_settings', 'manage_working_hours', 'manage_auto_messages', 'manage_themes',
            'manage_webhooks', 'create_webhooks', 'edit_webhooks', 'delete_webhooks',
            'manage_languages', 'manage_translations',
        ],
        'supervisor' => [
            'view_sites',
            'view_users',
            'manage_chat_sessions', 'handle_chat_sessions', 'assign_sessions', 'transfer_sessions', 'close_sessions', 'view_session_history',
            'send_messages', 'receive_messages', 'upload_files', 'download_files',
            'manage_contact_messages', 'view_contact_messages', 'respond_to_contacts',
            'manage_templates', 'use_templates', 'create_templates', 'edit_templates',
            'view_analytics', 'view_reports', 'export_data', 'view_visitor_info',
            'manage_working_hours', 'manage_auto_messages',
        ],
        'agent' => [
            'handle_chat_sessions', 'view_session_history',
            'send_messages', 'receive_messages', 'upload_files',
            'view_contact_messages', 'respond_to_contacts',
            'use_templates',
            'view_visitor_info',
        ]
    ];

    /**
     * Check if user has permission
     */
    public function hasPermission(User $user, string $permission, int $siteId = null): bool
    {
        // Cache key for permission check
        $cacheKey = "user_permission_{$user->id}_{$permission}" . ($siteId ? "_{$siteId}" : '');
        
        return Cache::remember($cacheKey, 300, function () use ($user, $permission, $siteId) {
            return $user->hasPermission($permission, $siteId);
        });
    }

    /**
     * Get all permissions for user
     */
    public function getUserPermissions(User $user, int $siteId = null): array
    {
        $cacheKey = "user_permissions_{$user->id}" . ($siteId ? "_{$siteId}" : '');
        
        return Cache::remember($cacheKey, 300, function () use ($user, $siteId) {
            $permissions = [];

            // Get global permissions
            if (is_array($user->permissions)) {
                $permissions = array_merge($permissions, $user->permissions);
            }

            // Get role-based permissions
            if (isset(self::ROLE_PERMISSIONS[$user->role])) {
                $permissions = array_merge($permissions, self::ROLE_PERMISSIONS[$user->role]);
            }

            // Get site-specific permissions
            if ($siteId) {
                $siteUser = $user->sites()->where('site_id', $siteId)->first();
                if ($siteUser && $siteUser->pivot->is_active) {
                    $sitePermissions = $siteUser->pivot->permissions ?? [];
                    if (is_array($sitePermissions)) {
                        $permissions = array_merge($permissions, $sitePermissions);
                    }

                    // Add role-based permissions for site role
                    $siteRole = $siteUser->pivot->role;
                    if (isset(self::ROLE_PERMISSIONS[$siteRole])) {
                        $permissions = array_merge($permissions, self::ROLE_PERMISSIONS[$siteRole]);
                    }
                }
            }

            return array_unique($permissions);
        });
    }

    /**
     * Assign user to site with role and permissions
     */
    public function assignUserToSite(User $user, Site $site, string $role, array $permissions = []): bool
    {
        try {
            $user->sites()->syncWithoutDetaching([
                $site->id => [
                    'role' => $role,
                    'is_active' => true,
                    'permissions' => $permissions,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]
            ]);

            // Clear permission cache
            $this->clearUserPermissionCache($user->id);

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to assign user to site', [
                'user_id' => $user->id,
                'site_id' => $site->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Update user site permissions
     */
    public function updateUserSitePermissions(User $user, Site $site, array $permissions): bool
    {
        try {
            $user->sites()->updateExistingPivot($site->id, [
                'permissions' => $permissions,
                'updated_at' => now(),
            ]);

            // Clear permission cache
            $this->clearUserPermissionCache($user->id);

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to update user site permissions', [
                'user_id' => $user->id,
                'site_id' => $site->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Remove user from site
     */
    public function removeUserFromSite(User $user, Site $site): bool
    {
        try {
            $user->sites()->detach($site->id);

            // Clear permission cache
            $this->clearUserPermissionCache($user->id);

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to remove user from site', [
                'user_id' => $user->id,
                'site_id' => $site->id,
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Get available permissions list
     */
    public function getAvailablePermissions(): array
    {
        return self::PERMISSIONS;
    }

    /**
     * Get role permissions
     */
    public function getRolePermissions(string $role): array
    {
        return self::ROLE_PERMISSIONS[$role] ?? [];
    }

    /**
     * Clear user permission cache
     */
    public function clearUserPermissionCache(int $userId): void
    {
        $patterns = [
            "user_permission_{$userId}_*",
            "user_permissions_{$userId}*",
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    /**
     * Clear all permission cache
     */
    public function clearAllPermissionCache(): void
    {
        Cache::flush();
    }

    /**
     * Validate permissions array
     */
    public function validatePermissions(array $permissions): array
    {
        $validPermissions = array_keys(self::PERMISSIONS);
        return array_intersect($permissions, $validPermissions);
    }

    /**
     * Get permission groups for UI
     */
    public function getPermissionGroups(): array
    {
        return [
            'Site Management' => [
                'manage_sites', 'create_sites', 'edit_sites', 'delete_sites', 'view_sites'
            ],
            'User Management' => [
                'manage_users', 'create_users', 'edit_users', 'delete_users', 'view_users', 'assign_users_to_sites'
            ],
            'Chat Management' => [
                'manage_chat_sessions', 'handle_chat_sessions', 'assign_sessions', 'transfer_sessions', 'close_sessions', 'view_session_history'
            ],
            'Messaging' => [
                'send_messages', 'receive_messages', 'upload_files', 'download_files'
            ],
            'Contact Management' => [
                'manage_contact_messages', 'view_contact_messages', 'respond_to_contacts'
            ],
            'Templates' => [
                'manage_templates', 'use_templates', 'create_templates', 'edit_templates', 'delete_templates'
            ],
            'Analytics' => [
                'view_analytics', 'view_reports', 'export_data', 'view_visitor_info'
            ],
            'System Settings' => [
                'manage_settings', 'manage_working_hours', 'manage_auto_messages', 'manage_themes'
            ],
            'Webhooks' => [
                'manage_webhooks', 'create_webhooks', 'edit_webhooks', 'delete_webhooks'
            ],
            'Localization' => [
                'manage_languages', 'manage_translations'
            ]
        ];
    }
}

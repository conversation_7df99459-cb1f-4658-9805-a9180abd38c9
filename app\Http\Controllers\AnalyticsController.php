<?php

namespace App\Http\Controllers;

use App\Services\AnalyticsService;
use App\Models\VisitorTracking;
use App\Models\MouseTracking;
use App\Models\ClickEvent;
use App\Models\FormEvent;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    protected $analyticsService;

    public function __construct(AnalyticsService $analyticsService)
    {
        $this->analyticsService = $analyticsService;
    }

    /**
     * Get visitor behavior overview
     */
    public function getOverview(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $overview = $this->analyticsService->getVisitorOverview($siteId, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $overview,
        ]);
    }

    /**
     * Get device and browser statistics
     */
    public function getDeviceStats(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $stats = $this->analyticsService->getDeviceStats($siteId, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get geographic statistics
     */
    public function getGeographicStats(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $stats = $this->analyticsService->getGeographicStats($siteId, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get page performance statistics
     */
    public function getPageStats(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $stats = $this->analyticsService->getPageStats($siteId, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Get click heatmap data
     */
    public function getClickHeatmap(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'page_url' => 'required|url',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $heatmap = $this->analyticsService->getClickHeatmap(
            $siteId, 
            $request->page_url, 
            $startDate, 
            $endDate
        );

        return response()->json([
            'success' => true,
            'data' => [
                'page_url' => $request->page_url,
                'heatmap_data' => $heatmap,
            ],
        ]);
    }

    /**
     * Get mouse movement heatmap
     */
    public function getMouseHeatmap(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'page_url' => 'required|url',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $heatmap = $this->analyticsService->getMouseHeatmap(
            $siteId, 
            $request->page_url, 
            $startDate, 
            $endDate
        );

        return response()->json([
            'success' => true,
            'data' => [
                'page_url' => $request->page_url,
                'heatmap_data' => $heatmap,
            ],
        ]);
    }

    /**
     * Get form analytics
     */
    public function getFormAnalytics(Request $request, $siteId, $formId): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $analytics = $this->analyticsService->getFormAnalytics($siteId, $formId, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $analytics,
        ]);
    }

    /**
     * Get real-time visitor activity
     */
    public function getRealTimeActivity(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'minutes' => 'nullable|integer|min:1|max:1440', // Max 24 hours
        ]);

        $minutes = $request->minutes ?? 30;
        $activity = $this->analyticsService->getRealTimeActivity($siteId, $minutes);

        return response()->json([
            'success' => true,
            'data' => $activity,
        ]);
    }

    /**
     * Get engagement metrics
     */
    public function getEngagementMetrics(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $metrics = $this->analyticsService->getEngagementMetrics($siteId, $startDate, $endDate);

        return response()->json([
            'success' => true,
            'data' => $metrics,
        ]);
    }

    /**
     * Get visitor journey
     */
    public function getVisitorJourney(Request $request, $siteId, $visitorId): JsonResponse
    {
        $request->validate([
            'session_id' => 'nullable|string',
        ]);

        $journey = $this->analyticsService->getVisitorJourney(
            $siteId, 
            $visitorId, 
            $request->session_id
        );

        return response()->json([
            'success' => true,
            'data' => $journey,
        ]);
    }

    /**
     * Get conversion funnel analysis
     */
    public function getConversionFunnel(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'funnel_steps' => 'required|array',
            'funnel_steps.*.name' => 'required|string',
            'funnel_steps.*.page_url' => 'nullable|string',
            'funnel_steps.*.event_type' => 'nullable|in:click,form_submit',
            'funnel_steps.*.element_selector' => 'nullable|string',
            'funnel_steps.*.form_id' => 'nullable|string',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $funnel = $this->analyticsService->getConversionFunnel(
            $siteId, 
            $request->funnel_steps, 
            $startDate, 
            $endDate
        );

        return response()->json([
            'success' => true,
            'data' => $funnel,
        ]);
    }

    /**
     * Get most clicked elements for a page
     */
    public function getMostClickedElements(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'page_url' => 'required|url',
            'limit' => 'nullable|integer|min:1|max:50',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $limit = $request->limit ?? 10;
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        $elements = ClickEvent::getMostClickedElements(
            $siteId, 
            $request->page_url, 
            $limit, 
            $startDate, 
            $endDate
        );

        return response()->json([
            'success' => true,
            'data' => [
                'page_url' => $request->page_url,
                'elements' => $elements,
            ],
        ]);
    }

    /**
     * Track visitor behavior (for widget integration)
     */
    public function trackVisitor(Request $request, $siteId): JsonResponse
    {
        $request->validate([
            'visitor_id' => 'required|string',
            'session_id' => 'required|string',
            'page_url' => 'required|url',
            'page_title' => 'nullable|string',
            'referrer' => 'nullable|url',
            'utm_source' => 'nullable|string',
            'utm_medium' => 'nullable|string',
            'utm_campaign' => 'nullable|string',
            'utm_term' => 'nullable|string',
            'utm_content' => 'nullable|string',
            'device_info' => 'nullable|array',
            'browser_info' => 'nullable|array',
            'screen_resolution' => 'nullable|array',
            'viewport_size' => 'nullable|array',
            'user_agent' => 'nullable|string',
            'ip_address' => 'nullable|ip',
            'country' => 'nullable|string',
            'city' => 'nullable|string',
            'timezone' => 'nullable|string',
            'language' => 'nullable|string',
        ]);

        $tracking = VisitorTracking::updateOrCreate(
            [
                'site_id' => $siteId,
                'visitor_id' => $request->visitor_id,
                'session_id' => $request->session_id,
            ],
            $request->only([
                'page_url', 'page_title', 'referrer', 'utm_source', 'utm_medium',
                'utm_campaign', 'utm_term', 'utm_content', 'device_info',
                'browser_info', 'screen_resolution', 'viewport_size', 'user_agent',
                'ip_address', 'country', 'city', 'timezone', 'language'
            ])
        );

        return response()->json([
            'success' => true,
            'data' => [
                'tracking_id' => $tracking->tracking_id,
            ],
        ]);
    }

    /**
     * Clear analytics cache
     */
    public function clearCache($siteId): JsonResponse
    {
        $this->analyticsService->clearCache($siteId);

        return response()->json([
            'success' => true,
            'message' => 'Analytics cache cleared successfully',
        ]);
    }
}

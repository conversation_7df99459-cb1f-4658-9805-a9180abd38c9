<?php

namespace App\Services;

use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\User;
use App\Models\Site;
use App\Models\VisitorTracking;
use App\Models\ContactMessage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class StatisticsService
{
    /**
     * Get dashboard overview statistics
     */
    public function getDashboardOverview($siteId = null, $startDate = null, $endDate = null)
    {
        $cacheKey = "dashboard_overview_" . ($siteId ?? 'all') . '_' . md5($startDate . $endDate);
        
        return Cache::remember($cacheKey, 300, function () use ($siteId, $startDate, $endDate) {
            $query = ChatSession::query();
            
            if ($siteId) {
                $query->where('site_id', $siteId);
            }
            
            if ($startDate && $endDate) {
                $query->whereBetween('started_at', [$startDate, $endDate]);
            }
            
            $totalSessions = $query->count();
            $activeSessions = $query->where('status', 'active')->count();
            $completedSessions = $query->where('status', 'closed')->count();
            $avgResponseTime = $query->whereNotNull('first_response_at')
                ->selectRaw('AVG(TIMESTAMPDIFF(SECOND, started_at, first_response_at)) as avg_response')
                ->first()->avg_response ?? 0;
            
            $avgSessionDuration = $query->whereNotNull('ended_at')
                ->selectRaw('AVG(TIMESTAMPDIFF(SECOND, started_at, ended_at)) as avg_duration')
                ->first()->avg_duration ?? 0;
            
            // Get satisfaction ratings
            $satisfactionQuery = $query->whereNotNull('satisfaction_rating');
            $avgSatisfaction = $satisfactionQuery->avg('satisfaction_rating') ?? 0;
            $totalRatings = $satisfactionQuery->count();
            
            // Get agent statistics
            $onlineAgents = User::where('status', 'online')
                ->when($siteId, function ($q) use ($siteId) {
                    $q->whereHas('sites', function ($sq) use ($siteId) {
                        $sq->where('site_id', $siteId);
                    });
                })
                ->count();
            
            $totalAgents = User::where('role', 'agent')
                ->when($siteId, function ($q) use ($siteId) {
                    $q->whereHas('sites', function ($sq) use ($siteId) {
                        $sq->where('site_id', $siteId);
                    });
                })
                ->count();
            
            return [
                'total_sessions' => $totalSessions,
                'active_sessions' => $activeSessions,
                'completed_sessions' => $completedSessions,
                'completion_rate' => $totalSessions > 0 ? round(($completedSessions / $totalSessions) * 100, 2) : 0,
                'avg_response_time' => round($avgResponseTime, 2),
                'avg_session_duration' => round($avgSessionDuration, 2),
                'avg_satisfaction' => round($avgSatisfaction, 2),
                'total_ratings' => $totalRatings,
                'online_agents' => $onlineAgents,
                'total_agents' => $totalAgents,
                'agent_utilization' => $totalAgents > 0 ? round(($onlineAgents / $totalAgents) * 100, 2) : 0,
            ];
        });
    }

    /**
     * Get session statistics over time
     */
    public function getSessionStatistics($siteId = null, $period = 'day', $startDate = null, $endDate = null)
    {
        $cacheKey = "session_stats_{$period}_" . ($siteId ?? 'all') . '_' . md5($startDate . $endDate);
        
        return Cache::remember($cacheKey, 600, function () use ($siteId, $period, $startDate, $endDate) {
            $query = ChatSession::query();
            
            if ($siteId) {
                $query->where('site_id', $siteId);
            }
            
            if ($startDate && $endDate) {
                $query->whereBetween('started_at', [$startDate, $endDate]);
            } else {
                // Default to last 30 days
                $query->where('started_at', '>=', now()->subDays(30));
            }
            
            $dateFormat = $this->getDateFormat($period);
            $groupBy = $this->getGroupByFormat($period);
            
            $statistics = $query->selectRaw("
                    {$groupBy} as period,
                    COUNT(*) as total_sessions,
                    SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as completed_sessions,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_sessions,
                    AVG(CASE WHEN first_response_at IS NOT NULL 
                        THEN TIMESTAMPDIFF(SECOND, started_at, first_response_at) 
                        ELSE NULL END) as avg_response_time,
                    AVG(CASE WHEN ended_at IS NOT NULL 
                        THEN TIMESTAMPDIFF(SECOND, started_at, ended_at) 
                        ELSE NULL END) as avg_duration,
                    AVG(satisfaction_rating) as avg_satisfaction
                ")
                ->groupBy(DB::raw($groupBy))
                ->orderBy('period')
                ->get()
                ->map(function ($item) {
                    return [
                        'period' => $item->period,
                        'total_sessions' => (int) $item->total_sessions,
                        'completed_sessions' => (int) $item->completed_sessions,
                        'active_sessions' => (int) $item->active_sessions,
                        'completion_rate' => $item->total_sessions > 0 
                            ? round(($item->completed_sessions / $item->total_sessions) * 100, 2) 
                            : 0,
                        'avg_response_time' => round($item->avg_response_time ?? 0, 2),
                        'avg_duration' => round($item->avg_duration ?? 0, 2),
                        'avg_satisfaction' => round($item->avg_satisfaction ?? 0, 2),
                    ];
                });
            
            return $statistics;
        });
    }

    /**
     * Get agent performance statistics
     */
    public function getAgentPerformance($siteId = null, $startDate = null, $endDate = null)
    {
        $cacheKey = "agent_performance_" . ($siteId ?? 'all') . '_' . md5($startDate . $endDate);
        
        return Cache::remember($cacheKey, 600, function () use ($siteId, $startDate, $endDate) {
            $query = ChatSession::with('agent');
            
            if ($siteId) {
                $query->where('site_id', $siteId);
            }
            
            if ($startDate && $endDate) {
                $query->whereBetween('started_at', [$startDate, $endDate]);
            }
            
            $agentStats = $query->whereNotNull('agent_id')
                ->selectRaw("
                    agent_id,
                    COUNT(*) as total_sessions,
                    SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as completed_sessions,
                    AVG(CASE WHEN first_response_at IS NOT NULL 
                        THEN TIMESTAMPDIFF(SECOND, started_at, first_response_at) 
                        ELSE NULL END) as avg_response_time,
                    AVG(CASE WHEN ended_at IS NOT NULL 
                        THEN TIMESTAMPDIFF(SECOND, started_at, ended_at) 
                        ELSE NULL END) as avg_session_duration,
                    AVG(satisfaction_rating) as avg_satisfaction,
                    COUNT(CASE WHEN satisfaction_rating IS NOT NULL THEN 1 END) as total_ratings
                ")
                ->groupBy('agent_id')
                ->orderBy('total_sessions', 'desc')
                ->get()
                ->map(function ($stat) {
                    $agent = User::find($stat->agent_id);
                    
                    return [
                        'agent_id' => $stat->agent_id,
                        'agent_name' => $agent ? $agent->name : 'Unknown',
                        'agent_email' => $agent ? $agent->email : 'Unknown',
                        'total_sessions' => (int) $stat->total_sessions,
                        'completed_sessions' => (int) $stat->completed_sessions,
                        'completion_rate' => $stat->total_sessions > 0 
                            ? round(($stat->completed_sessions / $stat->total_sessions) * 100, 2) 
                            : 0,
                        'avg_response_time' => round($stat->avg_response_time ?? 0, 2),
                        'avg_session_duration' => round($stat->avg_session_duration ?? 0, 2),
                        'avg_satisfaction' => round($stat->avg_satisfaction ?? 0, 2),
                        'total_ratings' => (int) $stat->total_ratings,
                    ];
                });
            
            return $agentStats;
        });
    }

    /**
     * Get response time distribution
     */
    public function getResponseTimeDistribution($siteId = null, $startDate = null, $endDate = null)
    {
        $cacheKey = "response_time_dist_" . ($siteId ?? 'all') . '_' . md5($startDate . $endDate);
        
        return Cache::remember($cacheKey, 600, function () use ($siteId, $startDate, $endDate) {
            $query = ChatSession::whereNotNull('first_response_at');
            
            if ($siteId) {
                $query->where('site_id', $siteId);
            }
            
            if ($startDate && $endDate) {
                $query->whereBetween('started_at', [$startDate, $endDate]);
            }
            
            $distribution = $query->selectRaw("
                    CASE 
                        WHEN TIMESTAMPDIFF(SECOND, started_at, first_response_at) <= 30 THEN '0-30s'
                        WHEN TIMESTAMPDIFF(SECOND, started_at, first_response_at) <= 60 THEN '31-60s'
                        WHEN TIMESTAMPDIFF(SECOND, started_at, first_response_at) <= 120 THEN '1-2min'
                        WHEN TIMESTAMPDIFF(SECOND, started_at, first_response_at) <= 300 THEN '2-5min'
                        WHEN TIMESTAMPDIFF(SECOND, started_at, first_response_at) <= 600 THEN '5-10min'
                        ELSE '10min+'
                    END as time_range,
                    COUNT(*) as count
                ")
                ->groupBy('time_range')
                ->orderByRaw("
                    CASE time_range
                        WHEN '0-30s' THEN 1
                        WHEN '31-60s' THEN 2
                        WHEN '1-2min' THEN 3
                        WHEN '2-5min' THEN 4
                        WHEN '5-10min' THEN 5
                        WHEN '10min+' THEN 6
                    END
                ")
                ->get();
            
            $total = $distribution->sum('count');
            
            return $distribution->map(function ($item) use ($total) {
                return [
                    'time_range' => $item->time_range,
                    'count' => (int) $item->count,
                    'percentage' => $total > 0 ? round(($item->count / $total) * 100, 2) : 0,
                ];
            });
        });
    }

    /**
     * Get satisfaction rating distribution
     */
    public function getSatisfactionDistribution($siteId = null, $startDate = null, $endDate = null)
    {
        $cacheKey = "satisfaction_dist_" . ($siteId ?? 'all') . '_' . md5($startDate . $endDate);
        
        return Cache::remember($cacheKey, 600, function () use ($siteId, $startDate, $endDate) {
            $query = ChatSession::whereNotNull('satisfaction_rating');
            
            if ($siteId) {
                $query->where('site_id', $siteId);
            }
            
            if ($startDate && $endDate) {
                $query->whereBetween('started_at', [$startDate, $endDate]);
            }
            
            $distribution = $query->selectRaw('satisfaction_rating, COUNT(*) as count')
                ->groupBy('satisfaction_rating')
                ->orderBy('satisfaction_rating')
                ->get();
            
            $total = $distribution->sum('count');
            
            return $distribution->map(function ($item) use ($total) {
                return [
                    'rating' => (int) $item->satisfaction_rating,
                    'count' => (int) $item->count,
                    'percentage' => $total > 0 ? round(($item->count / $total) * 100, 2) : 0,
                ];
            });
        });
    }

    /**
     * Get message volume statistics
     */
    public function getMessageVolumeStats($siteId = null, $period = 'day', $startDate = null, $endDate = null)
    {
        $cacheKey = "message_volume_{$period}_" . ($siteId ?? 'all') . '_' . md5($startDate . $endDate);
        
        return Cache::remember($cacheKey, 600, function () use ($siteId, $period, $startDate, $endDate) {
            $query = ChatMessage::with('session');
            
            if ($siteId) {
                $query->whereHas('session', function ($q) use ($siteId) {
                    $q->where('site_id', $siteId);
                });
            }
            
            if ($startDate && $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            } else {
                $query->where('created_at', '>=', now()->subDays(30));
            }
            
            $groupBy = $this->getGroupByFormat($period, 'created_at');
            
            $stats = $query->selectRaw("
                    {$groupBy} as period,
                    COUNT(*) as total_messages,
                    SUM(CASE WHEN sender_type = 'visitor' THEN 1 ELSE 0 END) as visitor_messages,
                    SUM(CASE WHEN sender_type = 'agent' THEN 1 ELSE 0 END) as agent_messages,
                    SUM(CASE WHEN sender_type = 'system' THEN 1 ELSE 0 END) as system_messages
                ")
                ->groupBy(DB::raw($groupBy))
                ->orderBy('period')
                ->get()
                ->map(function ($item) {
                    return [
                        'period' => $item->period,
                        'total_messages' => (int) $item->total_messages,
                        'visitor_messages' => (int) $item->visitor_messages,
                        'agent_messages' => (int) $item->agent_messages,
                        'system_messages' => (int) $item->system_messages,
                    ];
                });
            
            return $stats;
        });
    }

    /**
     * Get date format for SQL based on period
     */
    protected function getDateFormat($period)
    {
        switch ($period) {
            case 'hour':
                return '%Y-%m-%d %H:00:00';
            case 'day':
                return '%Y-%m-%d';
            case 'week':
                return '%Y-%u';
            case 'month':
                return '%Y-%m';
            case 'year':
                return '%Y';
            default:
                return '%Y-%m-%d';
        }
    }

    /**
     * Get GROUP BY format for SQL based on period
     */
    protected function getGroupByFormat($period, $column = 'started_at')
    {
        switch ($period) {
            case 'hour':
                return "DATE_FORMAT({$column}, '%Y-%m-%d %H:00:00')";
            case 'day':
                return "DATE({$column})";
            case 'week':
                return "YEARWEEK({$column})";
            case 'month':
                return "DATE_FORMAT({$column}, '%Y-%m')";
            case 'year':
                return "YEAR({$column})";
            default:
                return "DATE({$column})";
        }
    }

    /**
     * Get visitor conversion funnel
     */
    public function getVisitorConversionFunnel($siteId = null, $startDate = null, $endDate = null)
    {
        $cacheKey = "conversion_funnel_" . ($siteId ?? 'all') . '_' . md5($startDate . $endDate);

        return Cache::remember($cacheKey, 600, function () use ($siteId, $startDate, $endDate) {
            $visitorQuery = VisitorTracking::query();
            $sessionQuery = ChatSession::query();

            if ($siteId) {
                $visitorQuery->where('site_id', $siteId);
                $sessionQuery->where('site_id', $siteId);
            }

            if ($startDate && $endDate) {
                $visitorQuery->whereBetween('visited_at', [$startDate, $endDate]);
                $sessionQuery->whereBetween('started_at', [$startDate, $endDate]);
            }

            $totalVisitors = $visitorQuery->distinct('visitor_id')->count();
            $engagedVisitors = $visitorQuery->where('visit_duration', '>', 30)->distinct('visitor_id')->count();
            $chatInitiated = $sessionQuery->count();
            $chatCompleted = $sessionQuery->where('status', 'closed')->count();
            $satisfiedCustomers = $sessionQuery->where('satisfaction_rating', '>=', 4)->count();

            return [
                [
                    'stage' => 'Total Visitors',
                    'count' => $totalVisitors,
                    'percentage' => 100,
                    'conversion_rate' => null,
                ],
                [
                    'stage' => 'Engaged Visitors',
                    'count' => $engagedVisitors,
                    'percentage' => $totalVisitors > 0 ? round(($engagedVisitors / $totalVisitors) * 100, 2) : 0,
                    'conversion_rate' => $totalVisitors > 0 ? round(($engagedVisitors / $totalVisitors) * 100, 2) : 0,
                ],
                [
                    'stage' => 'Chat Initiated',
                    'count' => $chatInitiated,
                    'percentage' => $totalVisitors > 0 ? round(($chatInitiated / $totalVisitors) * 100, 2) : 0,
                    'conversion_rate' => $engagedVisitors > 0 ? round(($chatInitiated / $engagedVisitors) * 100, 2) : 0,
                ],
                [
                    'stage' => 'Chat Completed',
                    'count' => $chatCompleted,
                    'percentage' => $totalVisitors > 0 ? round(($chatCompleted / $totalVisitors) * 100, 2) : 0,
                    'conversion_rate' => $chatInitiated > 0 ? round(($chatCompleted / $chatInitiated) * 100, 2) : 0,
                ],
                [
                    'stage' => 'Satisfied Customers',
                    'count' => $satisfiedCustomers,
                    'percentage' => $totalVisitors > 0 ? round(($satisfiedCustomers / $totalVisitors) * 100, 2) : 0,
                    'conversion_rate' => $chatCompleted > 0 ? round(($satisfiedCustomers / $chatCompleted) * 100, 2) : 0,
                ],
            ];
        });
    }

    /**
     * Get peak hours analysis
     */
    public function getPeakHoursAnalysis($siteId = null, $startDate = null, $endDate = null)
    {
        $cacheKey = "peak_hours_" . ($siteId ?? 'all') . '_' . md5($startDate . $endDate);

        return Cache::remember($cacheKey, 600, function () use ($siteId, $startDate, $endDate) {
            $query = ChatSession::query();

            if ($siteId) {
                $query->where('site_id', $siteId);
            }

            if ($startDate && $endDate) {
                $query->whereBetween('started_at', [$startDate, $endDate]);
            } else {
                $query->where('started_at', '>=', now()->subDays(30));
            }

            $hourlyStats = $query->selectRaw("
                    HOUR(started_at) as hour,
                    COUNT(*) as session_count,
                    AVG(CASE WHEN first_response_at IS NOT NULL
                        THEN TIMESTAMPDIFF(SECOND, started_at, first_response_at)
                        ELSE NULL END) as avg_response_time,
                    AVG(satisfaction_rating) as avg_satisfaction
                ")
                ->groupBy('hour')
                ->orderBy('hour')
                ->get()
                ->map(function ($item) {
                    return [
                        'hour' => (int) $item->hour,
                        'hour_label' => sprintf('%02d:00', $item->hour),
                        'session_count' => (int) $item->session_count,
                        'avg_response_time' => round($item->avg_response_time ?? 0, 2),
                        'avg_satisfaction' => round($item->avg_satisfaction ?? 0, 2),
                    ];
                });

            // Fill missing hours with zero values
            $allHours = collect(range(0, 23))->map(function ($hour) use ($hourlyStats) {
                $existing = $hourlyStats->firstWhere('hour', $hour);

                return $existing ?? [
                    'hour' => $hour,
                    'hour_label' => sprintf('%02d:00', $hour),
                    'session_count' => 0,
                    'avg_response_time' => 0,
                    'avg_satisfaction' => 0,
                ];
            });

            return $allHours;
        });
    }

    /**
     * Clear statistics cache
     */
    public function clearCache($siteId = null)
    {
        $patterns = [
            "dashboard_overview_" . ($siteId ?? 'all') . "_*",
            "session_stats_*_" . ($siteId ?? 'all') . "_*",
            "agent_performance_" . ($siteId ?? 'all') . "_*",
            "response_time_dist_" . ($siteId ?? 'all') . "_*",
            "satisfaction_dist_" . ($siteId ?? 'all') . "_*",
            "message_volume_*_" . ($siteId ?? 'all') . "_*",
            "conversion_funnel_" . ($siteId ?? 'all') . "_*",
            "peak_hours_" . ($siteId ?? 'all') . "_*",
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }
}

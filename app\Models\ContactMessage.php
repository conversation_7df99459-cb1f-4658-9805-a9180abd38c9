<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class ContactMessage extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'message_id',
        'site_id',
        'visitor_id',
        'assigned_user_id',
        'name',
        'email',
        'phone',
        'subject',
        'message',
        'status',
        'priority',
        'category',
        'source',
        'ip_address',
        'user_agent',
        'page_url',
        'referrer',
        'response_message',
        'responded_at',
        'response_time',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'responded_at' => 'datetime',
        'response_time' => 'integer',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Status constants
     */
    const STATUS_NEW = 'new';
    const STATUS_ASSIGNED = 'assigned';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_RESPONDED = 'responded';
    const STATUS_CLOSED = 'closed';
    const STATUS_SPAM = 'spam';

    /**
     * Priority constants
     */
    const PRIORITY_LOW = 'low';
    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($message) {
            if (!$message->message_id) {
                $message->message_id = Str::uuid();
            }
            if (!$message->status) {
                $message->status = self::STATUS_NEW;
            }
            if (!$message->priority) {
                $message->priority = self::PRIORITY_NORMAL;
            }
        });
    }

    /**
     * Get the site that owns the contact message
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Get the visitor that sent the contact message
     */
    public function visitor()
    {
        return $this->belongsTo(Visitor::class);
    }

    /**
     * Get the user assigned to handle the contact message
     */
    public function assignedUser()
    {
        return $this->belongsTo(User::class, 'assigned_user_id');
    }

    /**
     * Check if message is new
     */
    public function isNew()
    {
        return $this->status === self::STATUS_NEW;
    }

    /**
     * Check if message is assigned
     */
    public function isAssigned()
    {
        return $this->status === self::STATUS_ASSIGNED;
    }

    /**
     * Check if message is in progress
     */
    public function isInProgress()
    {
        return $this->status === self::STATUS_IN_PROGRESS;
    }

    /**
     * Check if message has been responded to
     */
    public function isResponded()
    {
        return $this->status === self::STATUS_RESPONDED;
    }

    /**
     * Check if message is closed
     */
    public function isClosed()
    {
        return $this->status === self::STATUS_CLOSED;
    }

    /**
     * Check if message is marked as spam
     */
    public function isSpam()
    {
        return $this->status === self::STATUS_SPAM;
    }

    /**
     * Assign message to user
     */
    public function assignTo(User $user)
    {
        $this->update([
            'assigned_user_id' => $user->id,
            'status' => self::STATUS_ASSIGNED,
        ]);

        return $this;
    }

    /**
     * Mark as in progress
     */
    public function markInProgress()
    {
        $this->update(['status' => self::STATUS_IN_PROGRESS]);
        return $this;
    }

    /**
     * Respond to message
     */
    public function respond(string $responseMessage, User $user = null)
    {
        $responseTime = $this->created_at->diffInSeconds(now());
        
        $this->update([
            'response_message' => $responseMessage,
            'responded_at' => now(),
            'response_time' => $responseTime,
            'status' => self::STATUS_RESPONDED,
        ]);

        return $this;
    }

    /**
     * Close message
     */
    public function close()
    {
        $this->update(['status' => self::STATUS_CLOSED]);
        return $this;
    }

    /**
     * Mark as spam
     */
    public function markAsSpam()
    {
        $this->update(['status' => self::STATUS_SPAM]);
        return $this;
    }

    /**
     * Get priority color
     */
    public function getPriorityColorAttribute()
    {
        $colors = [
            self::PRIORITY_LOW => 'green',
            self::PRIORITY_NORMAL => 'blue',
            self::PRIORITY_HIGH => 'orange',
            self::PRIORITY_URGENT => 'red',
        ];

        return $colors[$this->priority] ?? 'gray';
    }

    /**
     * Get status color
     */
    public function getStatusColorAttribute()
    {
        $colors = [
            self::STATUS_NEW => 'blue',
            self::STATUS_ASSIGNED => 'yellow',
            self::STATUS_IN_PROGRESS => 'orange',
            self::STATUS_RESPONDED => 'green',
            self::STATUS_CLOSED => 'gray',
            self::STATUS_SPAM => 'red',
        ];

        return $colors[$this->status] ?? 'gray';
    }

    /**
     * Get formatted response time
     */
    public function getFormattedResponseTimeAttribute()
    {
        if (!$this->response_time) {
            return null;
        }

        $hours = floor($this->response_time / 3600);
        $minutes = floor(($this->response_time % 3600) / 60);

        if ($hours > 0) {
            return "{$hours}h {$minutes}m";
        }

        return "{$minutes}m";
    }

    /**
     * Get sender display name
     */
    public function getSenderDisplayNameAttribute()
    {
        return $this->name ?: 'Anonymous';
    }

    /**
     * Get short message preview
     */
    public function getMessagePreviewAttribute()
    {
        return Str::limit($this->message, 100);
    }

    /**
     * Scope for new messages
     */
    public function scopeNew($query)
    {
        return $query->where('status', self::STATUS_NEW);
    }

    /**
     * Scope for assigned messages
     */
    public function scopeAssigned($query)
    {
        return $query->where('status', self::STATUS_ASSIGNED);
    }

    /**
     * Scope for messages in progress
     */
    public function scopeInProgress($query)
    {
        return $query->where('status', self::STATUS_IN_PROGRESS);
    }

    /**
     * Scope for responded messages
     */
    public function scopeResponded($query)
    {
        return $query->where('status', self::STATUS_RESPONDED);
    }

    /**
     * Scope for closed messages
     */
    public function scopeClosed($query)
    {
        return $query->where('status', self::STATUS_CLOSED);
    }

    /**
     * Scope for messages by priority
     */
    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope for messages by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for messages assigned to user
     */
    public function scopeAssignedTo($query, $userId)
    {
        return $query->where('assigned_user_id', $userId);
    }

    /**
     * Scope for unassigned messages
     */
    public function scopeUnassigned($query)
    {
        return $query->whereNull('assigned_user_id');
    }

    /**
     * Scope for messages by site
     */
    public function scopeBySite($query, $siteId)
    {
        return $query->where('site_id', $siteId);
    }

    /**
     * Scope for recent messages
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Convert to API array
     */
    public function toApiArray()
    {
        return [
            'id' => $this->id,
            'message_id' => $this->message_id,
            'name' => $this->sender_display_name,
            'email' => $this->email,
            'phone' => $this->phone,
            'subject' => $this->subject,
            'message' => $this->message,
            'message_preview' => $this->message_preview,
            'status' => $this->status,
            'status_color' => $this->status_color,
            'priority' => $this->priority,
            'priority_color' => $this->priority_color,
            'category' => $this->category,
            'source' => $this->source,
            'page_url' => $this->page_url,
            'assigned_user' => $this->assignedUser ? [
                'id' => $this->assignedUser->id,
                'name' => $this->assignedUser->name,
            ] : null,
            'response_message' => $this->response_message,
            'responded_at' => $this->responded_at ? $this->responded_at->toISOString() : null,
            'response_time' => $this->formatted_response_time,
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
        ];
    }
}

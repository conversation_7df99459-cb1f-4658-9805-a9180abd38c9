<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('st_themes', function (Blueprint $table) {
            $table->id();
            $table->string('theme_id')->unique();
            $table->unsignedBigInteger('site_id');
            $table->string('name');
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(false);
            $table->boolean('is_default')->default(false);
            
            // Color scheme settings
            $table->json('color_scheme')->nullable();
            
            // Typography settings
            $table->json('typography')->nullable();
            
            // Layout settings
            $table->json('layout_settings')->nullable();
            
            // Custom CSS
            $table->longText('custom_css')->nullable();
            
            // Logo and favicon
            $table->string('logo_url')->nullable();
            $table->string('favicon_url')->nullable();
            
            // Background settings
            $table->json('background_settings')->nullable();
            
            // Button styles
            $table->json('button_styles')->nullable();
            
            // Chat bubble styles
            $table->json('chat_bubble_styles')->nullable();
            
            // Animation settings
            $table->json('animation_settings')->nullable();
            
            // Responsive settings
            $table->json('responsive_settings')->nullable();
            
            // Additional metadata
            $table->json('metadata')->nullable();
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['site_id', 'is_active']);
            $table->index(['site_id', 'is_default']);
            $table->index('theme_id');
            
            // Foreign key constraint
            $table->foreign('site_id')->references('id')->on('st_sites')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('st_themes');
    }
};

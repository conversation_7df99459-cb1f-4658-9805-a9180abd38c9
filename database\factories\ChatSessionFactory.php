<?php

namespace Database\Factories;

use App\Models\ChatSession;
use App\Models\Site;
use App\Models\User;
use App\Models\Visitor;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class ChatSessionFactory extends Factory
{
    protected $model = ChatSession::class;

    public function definition()
    {
        $startedAt = $this->faker->dateTimeBetween('-1 month', 'now');
        $status = $this->faker->randomElement(['waiting', 'active', 'ended']);
        
        return [
            'session_id' => Str::uuid(),
            'site_id' => Site::factory(),
            'visitor_id' => Visitor::factory(),
            'agent_id' => $status !== 'waiting' ? User::factory() : null,
            'status' => $status,
            'visitor_name' => $this->faker->name(),
            'visitor_email' => $this->faker->safeEmail(),
            'visitor_phone' => $this->faker->optional()->phoneNumber(),
            'initial_message' => $this->faker->sentence(),
            'started_at' => $startedAt,
            'assigned_at' => $status !== 'waiting' ? $this->faker->dateTimeBetween($startedAt, 'now') : null,
            'ended_at' => $status === 'ended' ? $this->faker->dateTimeBetween($startedAt, 'now') : null,
            'rating' => $status === 'ended' ? $this->faker->optional(0.7)->numberBetween(1, 5) : null,
            'feedback' => $status === 'ended' ? $this->faker->optional(0.5)->paragraph() : null,
            'tags' => json_encode($this->faker->optional(0.6)->words(3)),
            'priority' => $this->faker->randomElement(['low', 'normal', 'high', 'urgent']),
            'department' => $this->faker->optional()->randomElement(['sales', 'support', 'billing', 'technical']),
            'language' => $this->faker->randomElement(['en', 'zh', 'es', 'fr', 'de']),
            'referrer' => $this->faker->optional()->url(),
            'user_agent' => $this->faker->userAgent(),
            'ip_address' => $this->faker->ipv4(),
            'country' => $this->faker->countryCode(),
            'city' => $this->faker->city(),
            'custom_fields' => json_encode([
                'product_interest' => $this->faker->optional()->word(),
                'budget_range' => $this->faker->optional()->randomElement(['<1000', '1000-5000', '5000-10000', '>10000']),
                'company_size' => $this->faker->optional()->randomElement(['1-10', '11-50', '51-200', '200+']),
            ]),
        ];
    }

    public function waiting()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 'waiting',
                'agent_id' => null,
                'assigned_at' => null,
                'ended_at' => null,
                'rating' => null,
                'feedback' => null,
            ];
        });
    }

    public function active()
    {
        return $this->state(function (array $attributes) {
            $startedAt = $attributes['started_at'] ?? now()->subHour();
            
            return [
                'status' => 'active',
                'agent_id' => User::factory(),
                'assigned_at' => $this->faker->dateTimeBetween($startedAt, 'now'),
                'ended_at' => null,
                'rating' => null,
                'feedback' => null,
            ];
        });
    }

    public function ended()
    {
        return $this->state(function (array $attributes) {
            $startedAt = $attributes['started_at'] ?? now()->subHours(2);
            $assignedAt = $this->faker->dateTimeBetween($startedAt, now()->subHour());
            
            return [
                'status' => 'ended',
                'agent_id' => User::factory(),
                'assigned_at' => $assignedAt,
                'ended_at' => $this->faker->dateTimeBetween($assignedAt, 'now'),
                'rating' => $this->faker->optional(0.8)->numberBetween(1, 5),
                'feedback' => $this->faker->optional(0.6)->paragraph(),
            ];
        });
    }

    public function highPriority()
    {
        return $this->state(function (array $attributes) {
            return [
                'priority' => 'urgent',
            ];
        });
    }

    public function withRating($rating = null)
    {
        return $this->state(function (array $attributes) use ($rating) {
            return [
                'status' => 'ended',
                'rating' => $rating ?? $this->faker->numberBetween(1, 5),
                'feedback' => $this->faker->paragraph(),
                'ended_at' => now(),
            ];
        });
    }

    public function forSite($siteId)
    {
        return $this->state(function (array $attributes) use ($siteId) {
            return [
                'site_id' => $siteId,
            ];
        });
    }

    public function withAgent($agentId)
    {
        return $this->state(function (array $attributes) use ($agentId) {
            return [
                'agent_id' => $agentId,
                'status' => 'active',
                'assigned_at' => now(),
            ];
        });
    }
}

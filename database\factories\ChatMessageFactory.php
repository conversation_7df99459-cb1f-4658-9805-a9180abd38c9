<?php

namespace Database\Factories;

use App\Models\ChatMessage;
use App\Models\ChatSession;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class ChatMessageFactory extends Factory
{
    protected $model = ChatMessage::class;

    public function definition()
    {
        $senderType = $this->faker->randomElement(['visitor', 'agent', 'system']);
        $messageType = $this->faker->randomElement(['text', 'file', 'system']);
        
        return [
            'message_id' => Str::uuid(),
            'session_id' => ChatSession::factory(),
            'sender_type' => $senderType,
            'sender_id' => $senderType === 'agent' ? User::factory() : null,
            'message' => $this->generateMessage($messageType),
            'message_type' => $messageType,
            'file_url' => $messageType === 'file' ? $this->faker->url() : null,
            'file_name' => $messageType === 'file' ? $this->faker->word() . '.pdf' : null,
            'file_size' => $messageType === 'file' ? $this->faker->numberBetween(1024, 5242880) : null,
            'file_type' => $messageType === 'file' ? $this->faker->randomElement(['pdf', 'jpg', 'png', 'doc']) : null,
            'is_read' => $this->faker->boolean(70),
            'read_at' => $this->faker->optional(0.7)->dateTimeBetween('-1 hour', 'now'),
            'metadata' => json_encode([
                'ip_address' => $this->faker->ipv4(),
                'user_agent' => $this->faker->userAgent(),
                'timestamp' => now()->toISOString(),
            ]),
            'sent_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
        ];
    }

    protected function generateMessage($type)
    {
        switch ($type) {
            case 'text':
                return $this->faker->randomElement([
                    'Hello, how can I help you today?',
                    'Thank you for contacting us.',
                    'I need help with my order.',
                    'Can you provide more information?',
                    'That sounds great, thank you!',
                    'I\'m having trouble with the website.',
                    'What are your business hours?',
                    'How long will shipping take?',
                    'Is there a discount available?',
                    'I\'d like to speak with a manager.',
                ]);
            case 'file':
                return 'File attachment: ' . $this->faker->word() . '.pdf';
            case 'system':
                return $this->faker->randomElement([
                    'Agent joined the conversation',
                    'Agent left the conversation',
                    'Session transferred to another agent',
                    'Session ended by agent',
                    'Session ended by visitor',
                    'Visitor is typing...',
                    'Agent is typing...',
                ]);
            default:
                return $this->faker->sentence();
        }
    }

    public function fromVisitor()
    {
        return $this->state(function (array $attributes) {
            return [
                'sender_type' => 'visitor',
                'sender_id' => null,
                'message' => $this->faker->randomElement([
                    'Hi, I need help with my order',
                    'Can someone assist me?',
                    'I have a question about your product',
                    'Is anyone available to chat?',
                    'I\'m having trouble with checkout',
                ]),
            ];
        });
    }

    public function fromAgent()
    {
        return $this->state(function (array $attributes) {
            return [
                'sender_type' => 'agent',
                'sender_id' => User::factory(),
                'message' => $this->faker->randomElement([
                    'Hello! How can I help you today?',
                    'Thank you for contacting us',
                    'I\'d be happy to assist you with that',
                    'Let me check that for you',
                    'Is there anything else I can help with?',
                ]),
            ];
        });
    }

    public function systemMessage()
    {
        return $this->state(function (array $attributes) {
            return [
                'sender_type' => 'system',
                'sender_id' => null,
                'message_type' => 'system',
                'message' => $this->faker->randomElement([
                    'Agent joined the conversation',
                    'Agent left the conversation',
                    'Session transferred to another agent',
                    'Session ended',
                ]),
            ];
        });
    }

    public function fileMessage()
    {
        return $this->state(function (array $attributes) {
            $fileName = $this->faker->word() . '.' . $this->faker->randomElement(['pdf', 'jpg', 'png', 'doc']);
            
            return [
                'message_type' => 'file',
                'message' => 'File attachment: ' . $fileName,
                'file_url' => '/storage/chat_files/' . $fileName,
                'file_name' => $fileName,
                'file_size' => $this->faker->numberBetween(1024, 5242880),
                'file_type' => pathinfo($fileName, PATHINFO_EXTENSION),
            ];
        });
    }

    public function unread()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_read' => false,
                'read_at' => null,
            ];
        });
    }

    public function read()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_read' => true,
                'read_at' => now(),
            ];
        });
    }

    public function forSession($sessionId)
    {
        return $this->state(function (array $attributes) use ($sessionId) {
            return [
                'session_id' => $sessionId,
            ];
        });
    }

    public function recent()
    {
        return $this->state(function (array $attributes) {
            return [
                'sent_at' => $this->faker->dateTimeBetween('-10 minutes', 'now'),
            ];
        });
    }

    public function old()
    {
        return $this->state(function (array $attributes) {
            return [
                'sent_at' => $this->faker->dateTimeBetween('-1 month', '-1 day'),
            ];
        });
    }
}

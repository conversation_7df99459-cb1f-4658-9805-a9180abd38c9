<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>New Contact Message</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .content {
            background-color: #fff;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
        }
        .field {
            margin-bottom: 15px;
        }
        .field-label {
            font-weight: bold;
            color: #495057;
        }
        .field-value {
            margin-top: 5px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .priority {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .priority-low { background-color: #d4edda; color: #155724; }
        .priority-medium { background-color: #fff3cd; color: #856404; }
        .priority-high { background-color: #f8d7da; color: #721c24; }
        .priority-urgent { background-color: #f5c6cb; color: #721c24; }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            font-size: 14px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>New Contact Message Received</h1>
        <p>A new contact message has been submitted through your website.</p>
    </div>

    <div class="content">
        <div class="field">
            <div class="field-label">Reference Number:</div>
            <div class="field-value">{{ $message->reference_number }}</div>
        </div>

        <div class="field">
            <div class="field-label">Name:</div>
            <div class="field-value">{{ $message->name }}</div>
        </div>

        <div class="field">
            <div class="field-label">Email:</div>
            <div class="field-value">{{ $message->email }}</div>
        </div>

        @if($message->phone)
        <div class="field">
            <div class="field-label">Phone:</div>
            <div class="field-value">{{ $message->phone }}</div>
        </div>
        @endif

        <div class="field">
            <div class="field-label">Subject:</div>
            <div class="field-value">{{ $message->subject }}</div>
        </div>

        <div class="field">
            <div class="field-label">Priority:</div>
            <div class="field-value">
                <span class="priority priority-{{ $message->priority }}">{{ ucfirst($message->priority) }}</span>
            </div>
        </div>

        <div class="field">
            <div class="field-label">Message:</div>
            <div class="field-value">{{ $message->message }}</div>
        </div>

        @if($message->attachments)
        <div class="field">
            <div class="field-label">Attachments:</div>
            <div class="field-value">
                @php
                    $attachments = json_decode($message->attachments, true);
                @endphp
                @foreach($attachments as $attachment)
                    <div>{{ $attachment['original_name'] }} ({{ number_format($attachment['size'] / 1024, 2) }} KB)</div>
                @endforeach
            </div>
        </div>
        @endif

        <div class="field">
            <div class="field-label">Submitted:</div>
            <div class="field-value">{{ $message->submitted_at->format('F j, Y \a\t g:i A') }}</div>
        </div>

        @if($message->site)
        <div class="field">
            <div class="field-label">Site:</div>
            <div class="field-value">{{ $message->site->site_name }}</div>
        </div>
        @endif

        <a href="{{ $adminUrl }}" class="button">View in Admin Panel</a>
    </div>

    <div class="footer">
        <p>This is an automated notification from your customer service system.</p>
        <p>Please do not reply to this email directly.</p>
    </div>
</body>
</html>

@echo off
chcp 65001 >nul
title WebSocket服务配置
color 0B

echo.
echo ========================================
echo    WebSocket服务配置脚本
echo ========================================
echo.

set PROJECT_DIR=%~dp0
set PROJECT_DIR=%PROJECT_DIR:~0,-1%

:: 检查PHP
if exist "D:\BtSoft\php\72\php.exe" (
    set PHP_PATH=D:\BtSoft\php\72\php.exe
) else if exist "D:\BtSoft\php\73\php.exe" (
    set PHP_PATH=D:\BtSoft\php\73\php.exe
) else if exist "D:\BtSoft\php\74\php.exe" (
    set PHP_PATH=D:\BtSoft\php\74\php.exe
) else (
    echo ❌ 未找到PHP
    pause
    exit /b 1
)

echo 项目目录: %PROJECT_DIR%
echo PHP路径: %PHP_PATH%
echo.

echo [1/4] 安装WebSocket依赖...
if exist composer.phar (
    "%PHP_PATH%" composer.phar require ratchet/pawl textalk/websocket-client --no-interaction
) else (
    composer require ratchet/pawl textalk/websocket-client --no-interaction
)

if %errorlevel% neq 0 (
    echo ❌ WebSocket依赖安装失败
    pause
    exit /b 1
)
echo ✅ WebSocket依赖安装完成

echo.
echo [2/4] 创建WebSocket启动脚本...
echo @echo off > start_websocket.bat
echo title WebSocket服务器 >> start_websocket.bat
echo cd /d "%PROJECT_DIR%" >> start_websocket.bat
echo echo 启动WebSocket服务器... >> start_websocket.bat
echo echo 端口: 8080 >> start_websocket.bat
echo echo 按Ctrl+C停止服务 >> start_websocket.bat
echo. >> start_websocket.bat
echo "%PHP_PATH%" artisan websocket:serve >> start_websocket.bat
echo pause >> start_websocket.bat

echo ✅ WebSocket启动脚本创建完成

echo.
echo [3/4] 创建队列处理脚本...
echo @echo off > start_queue.bat
echo title 队列处理器 >> start_queue.bat
echo cd /d "%PROJECT_DIR%" >> start_queue.bat
echo echo 启动队列处理器... >> start_queue.bat
echo echo 按Ctrl+C停止服务 >> start_queue.bat
echo. >> start_queue.bat
echo "%PHP_PATH%" artisan queue:work --sleep=3 --tries=3 >> start_queue.bat
echo pause >> start_queue.bat

echo ✅ 队列处理脚本创建完成

echo.
echo [4/4] 更新环境配置...
if exist .env (
    powershell -Command "(Get-Content .env) -replace 'BROADCAST_DRIVER=.*', 'BROADCAST_DRIVER=redis' | Set-Content .env"
    powershell -Command "(Get-Content .env) -replace 'QUEUE_CONNECTION=.*', 'QUEUE_CONNECTION=redis' | Set-Content .env"
    
    :: 添加WebSocket配置
    echo. >> .env
    echo # WebSocket配置 >> .env
    echo WEBSOCKET_HOST=127.0.0.1 >> .env
    echo WEBSOCKET_PORT=8080 >> .env
    
    echo ✅ 环境配置更新完成
) else (
    echo ❌ .env文件不存在
)

echo.
echo ========================================
echo    🎉 WebSocket配置完成！
echo ========================================
echo.
echo 已创建的文件：
echo - start_websocket.bat (启动WebSocket服务)
echo - start_queue.bat (启动队列处理)
echo.
echo 使用方法：
echo 1. 双击 start_websocket.bat 启动WebSocket服务
echo 2. 双击 start_queue.bat 启动队列处理
echo 3. 在宝塔面板防火墙中开放8080端口
echo.
echo 注意：两个服务都需要保持运行状态
echo.
pause

<?php

namespace App\Mail;

use App\Models\ContactMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ContactMessageReceived extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $contactMessage;

    /**
     * Create a new message instance.
     */
    public function __construct(ContactMessage $contactMessage)
    {
        $this->contactMessage = $contactMessage;
    }

    /**
     * Build the message.
     */
    public function build()
    {
        return $this->subject('New Contact Message - ' . $this->contactMessage->subject)
                    ->view('emails.contact-message-received')
                    ->with([
                        'message' => $this->contactMessage,
                        'adminUrl' => config('app.url') . '/admin/contact/' . $this->contactMessage->id
                    ]);
    }
}

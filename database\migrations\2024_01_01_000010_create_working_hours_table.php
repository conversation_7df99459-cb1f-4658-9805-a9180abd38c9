<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWorkingHoursTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('working_hours', function (Blueprint $table) {
            $table->id();
            $table->foreignId('site_id')->constrained()->onDelete('cascade');
            
            $table->enum('day_of_week', ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']);
            $table->boolean('is_working_day')->default(true);
            $table->time('start_time')->nullable();
            $table->time('end_time')->nullable();
            $table->time('break_start_time')->nullable();
            $table->time('break_end_time')->nullable();
            $table->string('timezone')->default('UTC');
            
            // Holiday/Special dates
            $table->date('special_date')->nullable();
            $table->boolean('is_holiday')->default(false);
            $table->string('holiday_name')->nullable();
            
            // Override settings
            $table->boolean('is_override')->default(false); // For special dates
            $table->date('override_date')->nullable();
            $table->time('override_start_time')->nullable();
            $table->time('override_end_time')->nullable();
            
            $table->timestamps();
            
            $table->index(['site_id', 'day_of_week']);
            $table->index(['site_id', 'special_date']);
            $table->index(['site_id', 'is_working_day']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('working_hours');
    }
}

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'
import { useNotificationStore } from './notifications.js'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const token = ref(localStorage.getItem('auth_token'))
  const isLoading = ref(false)
  const websocket = ref(null)
  const isConnected = ref(false)

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const userRole = computed(() => user.value?.role || null)
  const userName = computed(() => user.value?.name || '')
  const userEmail = computed(() => user.value?.email || '')
  const userAvatar = computed(() => user.value?.avatar || '/images/default-avatar.png')
  const siteId = computed(() => user.value?.site_id || null)

  // Actions
  const login = async (credentials) => {
    try {
      isLoading.value = true
      
      const response = await axios.post('/auth/login', credentials)
      
      if (response.data.success) {
        const { token: authToken, user: userData } = response.data.data
        
        // Store token and user data
        token.value = authToken
        user.value = userData
        
        localStorage.setItem('auth_token', authToken)
        localStorage.setItem('user_data', JSON.stringify(userData))
        
        // Set axios default header
        axios.defaults.headers.common['Authorization'] = `Bearer ${authToken}`
        
        // Initialize WebSocket connection
        await initializeWebSocket()
        
        return { success: true }
      } else {
        throw new Error(response.data.message || 'Login failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || 'Login failed'
      }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      // Close WebSocket connection
      if (websocket.value) {
        websocket.value.close()
        websocket.value = null
        isConnected.value = false
      }

      // Call logout API
      if (token.value) {
        await axios.post('/auth/logout')
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // Clear local data
      token.value = null
      user.value = null
      
      localStorage.removeItem('auth_token')
      localStorage.removeItem('user_data')
      
      delete axios.defaults.headers.common['Authorization']
    }
  }

  const fetchUser = async () => {
    try {
      if (!token.value) {
        throw new Error('No token available')
      }

      const response = await axios.get('/auth/user')
      
      if (response.data.success) {
        user.value = response.data.data
        localStorage.setItem('user_data', JSON.stringify(response.data.data))
        return response.data.data
      } else {
        throw new Error('Failed to fetch user data')
      }
    } catch (error) {
      console.error('Fetch user error:', error)
      // If token is invalid, logout
      if (error.response?.status === 401) {
        await logout()
      }
      throw error
    }
  }

  const updateProfile = async (profileData) => {
    try {
      isLoading.value = true
      
      const response = await axios.put('/auth/profile', profileData)
      
      if (response.data.success) {
        user.value = { ...user.value, ...response.data.data }
        localStorage.setItem('user_data', JSON.stringify(user.value))
        
        const notificationStore = useNotificationStore()
        notificationStore.addNotification({
          type: 'success',
          title: 'Profile Updated',
          message: 'Your profile has been updated successfully'
        })
        
        return { success: true }
      } else {
        throw new Error(response.data.message || 'Profile update failed')
      }
    } catch (error) {
      console.error('Profile update error:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || 'Profile update failed'
      }
    } finally {
      isLoading.value = false
    }
  }

  const updateStatus = async (status, statusMessage = '') => {
    try {
      const response = await axios.put('/admin/workspace/status', {
        status,
        status_message: statusMessage
      })
      
      if (response.data.success) {
        user.value.status = status
        user.value.status_message = statusMessage
        localStorage.setItem('user_data', JSON.stringify(user.value))
        
        return { success: true }
      } else {
        throw new Error(response.data.message || 'Status update failed')
      }
    } catch (error) {
      console.error('Status update error:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || 'Status update failed'
      }
    }
  }

  const initializeWebSocket = async () => {
    if (!user.value || websocket.value) {
      return
    }

    try {
      const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`
      websocket.value = new WebSocket(wsUrl)

      websocket.value.onopen = () => {
        console.log('WebSocket connected')
        isConnected.value = true
        
        // Send authentication
        websocket.value.send(JSON.stringify({
          type: 'auth',
          token: token.value,
          user_id: user.value.id,
          site_id: user.value.site_id
        }))
      }

      websocket.value.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          handleWebSocketMessage(data)
        } catch (error) {
          console.error('WebSocket message parse error:', error)
        }
      }

      websocket.value.onclose = () => {
        console.log('WebSocket disconnected')
        isConnected.value = false
        
        // Attempt to reconnect after 3 seconds
        setTimeout(() => {
          if (user.value && !websocket.value) {
            initializeWebSocket()
          }
        }, 3000)
      }

      websocket.value.onerror = (error) => {
        console.error('WebSocket error:', error)
        isConnected.value = false
      }

    } catch (error) {
      console.error('WebSocket initialization error:', error)
    }
  }

  const handleWebSocketMessage = (data) => {
    const notificationStore = useNotificationStore()
    
    switch (data.type) {
      case 'new_chat_request':
        notificationStore.addNotification({
          type: 'info',
          title: 'New Chat Request',
          message: `New chat request from ${data.visitor_name || 'Anonymous'}`,
          action: {
            label: 'View',
            handler: () => {
              // Navigate to workspace
              window.$router?.push('/admin/workspace')
            }
          }
        })
        break

      case 'chat_transferred_to_you':
        notificationStore.addNotification({
          type: 'info',
          title: 'Chat Transferred',
          message: `Chat transferred to you from ${data.from_agent}`,
          action: {
            label: 'Accept',
            handler: () => {
              // Navigate to workspace
              window.$router?.push('/admin/workspace')
            }
          }
        })
        break

      case 'new_message':
        // Handle new message notification
        if (data.sender_type !== 'agent' || data.sender_id !== user.value.id) {
          notificationStore.addNotification({
            type: 'info',
            title: 'New Message',
            message: `New message from ${data.sender_name}`,
            duration: 3000
          })
        }
        break

      case 'agent_status_change':
        // Handle agent status change
        console.log('Agent status changed:', data)
        break

      case 'system_notification':
        notificationStore.addNotification({
          type: data.notification_type || 'info',
          title: data.title,
          message: data.message
        })
        break

      default:
        console.log('Unhandled WebSocket message:', data)
    }
  }

  const sendWebSocketMessage = (message) => {
    if (websocket.value && isConnected.value) {
      websocket.value.send(JSON.stringify(message))
    }
  }

  // Initialize from localStorage on store creation
  const initializeFromStorage = () => {
    const storedUser = localStorage.getItem('user_data')
    if (storedUser && token.value) {
      try {
        user.value = JSON.parse(storedUser)
        axios.defaults.headers.common['Authorization'] = `Bearer ${token.value}`
      } catch (error) {
        console.error('Error parsing stored user data:', error)
        localStorage.removeItem('user_data')
        localStorage.removeItem('auth_token')
      }
    }
  }

  // Initialize on store creation
  initializeFromStorage()

  return {
    // State
    user,
    token,
    isLoading,
    websocket,
    isConnected,
    
    // Getters
    isAuthenticated,
    userRole,
    userName,
    userEmail,
    userAvatar,
    siteId,
    
    // Actions
    login,
    logout,
    fetchUser,
    updateProfile,
    updateStatus,
    initializeWebSocket,
    sendWebSocketMessage
  }
})

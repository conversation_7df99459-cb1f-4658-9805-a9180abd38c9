@echo off
chcp 65001 >nul
title 宝塔面板配置助手
color 0E

echo.
echo ========================================
echo    宝塔面板配置助手
echo ========================================
echo.

echo 请按照以下步骤在宝塔面板中进行配置：
echo.

echo 📋 第一步：创建网站
echo ----------------------------------------
echo 1. 登录宝塔面板
echo 2. 点击"网站" → "添加站点"
echo 3. 域名填写: aichat.jagship.com
echo 4. 根目录填写: D:\wwwroot\aichat.jagship.com\public
echo 5. PHP版本选择: 7.2
echo 6. 勾选"创建数据库"
echo 7. 数据库编码选择: utf8mb4
echo 8. 点击"提交"
echo.
pause

echo.
echo 📋 第二步：配置PHP扩展
echo ----------------------------------------
echo 1. 进入"软件商店" → "已安装"
echo 2. 找到PHP 7.2，点击"设置"
echo 3. 点击"安装扩展"，安装以下扩展：
echo    ✓ redis
echo    ✓ opcache
echo    ✓ fileinfo
echo    ✓ zip
echo    ✓ curl
echo    ✓ gd
echo    ✓ mbstring
echo 4. 点击"禁用函数"，删除以下函数：
echo    - putenv
echo    - getenv
echo    - proc_open
echo    - proc_close
echo 5. 点击"重载配置"
echo.
pause

echo.
echo 📋 第三步：配置Nginx
echo ----------------------------------------
echo 1. 在网站列表中点击站点名称后的"设置"
echo 2. 点击"配置文件"
echo 3. 将以下内容复制到配置文件中：
echo.
echo server {
echo     listen 80;
echo     server_name aichat.jagship.com;
echo     root D:/wwwroot/aichat.jagship.com/public;
echo     index index.php index.html;
echo     charset utf-8;
echo     client_max_body_size 50M;
echo.
echo     location / {
echo         try_files $uri $uri/ /index.php?$query_string;
echo     }
echo.
echo     location ~ \.php$ {
echo         fastcgi_pass 127.0.0.1:9000;
echo         fastcgi_index index.php;
echo         fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
echo         include fastcgi_params;
echo     }
echo.
echo     location /ws {
echo         proxy_pass http://127.0.0.1:8080;
echo         proxy_http_version 1.1;
echo         proxy_set_header Upgrade $http_upgrade;
echo         proxy_set_header Connection "upgrade";
echo         proxy_set_header Host $host;
echo     }
echo }
echo.
echo 4. 保存配置
echo.
pause

echo.
echo 📋 第四步：配置Redis
echo ----------------------------------------
echo 1. 在"软件商店"中找到Redis，点击"设置"
echo 2. 点击"配置修改"
echo 3. 确保以下配置正确：
echo    bind 127.0.0.1
echo    port 6379
echo 4. 点击"服务"，启动Redis
echo.
pause

echo.
echo 📋 第五步：配置防火墙
echo ----------------------------------------
echo 1. 点击"安全"
echo 2. 添加以下端口：
echo    - 8080 (WebSocket端口)
echo    - 80 (HTTP端口)
echo    - 443 (HTTPS端口，如果需要)
echo.
pause

echo.
echo 📋 第六步：配置hosts文件（本地测试）
echo ----------------------------------------
echo 1. 以管理员身份打开记事本
echo 2. 打开文件: C:\Windows\System32\drivers\etc\hosts
echo 3. 在文件末尾添加：
echo    127.0.0.1 aichat.jagship.com
echo 4. 保存文件
echo.
pause

echo.
echo ========================================
echo    🎉 配置完成！
echo ========================================
echo.
echo 现在您可以：
echo 1. 访问 http://aichat.jagship.com 测试网站
echo 2. 运行 start_websocket.bat 启动WebSocket服务
echo 3. 运行 start_queue.bat 启动队列处理
echo.
echo 如果遇到问题，请检查：
echo - PHP扩展是否正确安装
echo - 数据库连接是否正确
echo - 文件权限是否正确
echo.
pause

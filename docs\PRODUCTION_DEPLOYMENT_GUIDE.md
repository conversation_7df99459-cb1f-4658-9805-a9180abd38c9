# 生产环境部署教程 - CentOS 7 + 宝塔面板

## 环境信息
- **操作系统**: CentOS 7
- **宝塔面板**: 免费版 9.4.0
- **Web服务器**: Nginx 1.20.2
- **PHP版本**: 7.2
- **数据库**: MySQL 5.7.44
- **缓存**: Redis 7.4.3
- **数据库管理**: phpMyAdmin 5.0

## 第一步：服务器准备

### 1.1 服务器基础配置
```bash
# 更新系统
yum update -y

# 安装基础工具
yum install -y wget curl vim git unzip

# 设置时区
timedatectl set-timezone Asia/Shanghai

# 配置防火墙
systemctl start firewalld
systemctl enable firewalld

# 开放必要端口
firewall-cmd --permanent --add-port=80/tcp
firewall-cmd --permanent --add-port=443/tcp
firewall-cmd --permanent --add-port=8888/tcp  # 宝塔面板
firewall-cmd --permanent --add-port=8080/tcp  # WebSocket
firewall-cmd --reload
```

### 1.2 安装宝塔面板
```bash
# 下载并安装宝塔面板
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh ed8484bec

# 安装完成后会显示面板地址和初始账号密码
# 记录下这些信息，例如：
# 面板地址: http://your-server-ip:8888/xxxxxxxx
# 用户名: xxxxxxxx
# 密码: xxxxxxxx
```

### 1.3 初始化宝塔面板
1. 访问面板地址并登录
2. 绑定宝塔账号（推荐）
3. 选择安装LNMP环境

## 第二步：安装运行环境

### 2.1 一键安装LNMP
在宝塔面板首页选择"一键安装"：
- **Nginx 1.20.2** - 编译安装（推荐）
- **MySQL 5.7.44** - 编译安装
- **PHP 7.2** - 编译安装
- **Redis 7.4.3** - 编译安装
- **phpMyAdmin 5.0** - 安装

等待安装完成（约20-30分钟）

### 2.2 配置PHP扩展
1. 进入"软件商店" → "已安装"
2. 找到PHP 7.2，点击"设置"
3. 安装以下扩展：
   ```
   ✅ opcache      # 必装：PHP加速器
   ✅ redis        # 必装：Redis扩展
   ✅ memcached    # 推荐：缓存扩展
   ✅ imagemagick  # 必装：图像处理
   ✅ fileinfo     # 必装：文件信息
   ✅ exif         # 推荐：图片信息
   ✅ intl         # 必装：国际化
   ✅ zip          # 必装：压缩文件
   ✅ curl         # 必装：网络请求
   ✅ gd           # 必装：图像处理
   ✅ swoole       # 可选：高性能扩展
   ```

### 2.3 优化PHP配置
在PHP设置中修改配置文件：
```ini
# 性能优化
memory_limit = 512M
max_execution_time = 300
max_input_time = 300
post_max_size = 100M
upload_max_filesize = 100M
max_file_uploads = 50

# 生产环境安全设置
display_errors = Off
log_errors = On
error_log = /www/wwwlogs/php_errors.log

# OPcache优化
opcache.enable = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 10000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1

# 时区设置
date.timezone = Asia/Shanghai
```

## 第三步：创建网站和数据库

### 3.1 创建网站
1. 点击"网站" → "添加站点"
2. 填写信息：
   - **域名**: `your-domain.com`
   - **根目录**: `/www/wwwroot/your-domain.com`
   - **PHP版本**: 7.2
   - **创建数据库**: 是
   - **数据库编码**: utf8mb4

### 3.2 配置SSL证书
1. 在网站设置中点击"SSL"
2. 选择"Let's Encrypt"免费证书
3. 或上传自己的SSL证书
4. 开启"强制HTTPS"

### 3.3 记录数据库信息
```
数据库名: your_domain_com
用户名: your_domain_com
密码: 宝塔生成的随机密码
主机: localhost
端口: 3306
```

## 第四步：配置Redis

### 4.1 Redis基础配置
1. 进入"软件商店" → "已安装" → "Redis 7.4.3"
2. 点击"设置" → "配置修改"
3. 修改配置：
```redis
# 网络配置
bind 127.0.0.1
port 6379
timeout 300

# 安全配置
requirepass your_strong_password_here

# 内存配置
maxmemory 1gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000

# 日志配置
loglevel notice
logfile /www/server/redis/redis.log
```

### 4.2 启动Redis服务
```bash
systemctl start redis
systemctl enable redis
```

## 第五步：部署项目代码

### 5.1 上传项目文件
方法一：使用宝塔文件管理器上传压缩包并解压
方法二：使用Git克隆（推荐）
```bash
cd /www/wwwroot/your-domain.com
rm -rf * .*  # 清空目录
git clone https://github.com/your-repo/chat_web.git .
```

### 5.2 安装Composer
```bash
# 下载Composer
curl -sS https://getcomposer.org/installer | php
mv composer.phar /usr/local/bin/composer

# 配置国内镜像（可选）
composer config -g repo.packagist composer https://mirrors.aliyun.com/composer/
```

### 5.3 安装项目依赖
```bash
cd /www/wwwroot/your-domain.com

# 安装PHP依赖
composer install --no-dev --optimize-autoloader

# 如果需要前端构建
curl -sL https://rpm.nodesource.com/setup_16.x | bash -
yum install -y nodejs
npm install
npm run production
```

### 5.4 配置环境变量
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

生产环境`.env`配置：
```env
APP_NAME="Customer Service System"
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://your-domain.com

LOG_CHANNEL=daily
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_domain_com
DB_USERNAME=your_domain_com
DB_PASSWORD=your_database_password

BROADCAST_DRIVER=redis
CACHE_DRIVER=redis
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=your_redis_password
REDIS_PORT=6379

# 邮件配置
MAIL_MAILER=smtp
MAIL_HOST=smtp.your-domain.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# WebSocket配置
WEBSOCKET_HOST=0.0.0.0
WEBSOCKET_PORT=8080
```

### 5.5 初始化应用
```bash
# 生成应用密钥
php artisan key:generate

# 运行数据库迁移
php artisan migrate --force

# 运行数据填充
php artisan db:seed --force

# 创建存储链接
php artisan storage:link

# 优化应用
php artisan config:cache
php artisan route:cache
php artisan view:cache
composer dump-autoload --optimize
```

## 第六步：配置Nginx

### 6.1 网站配置
在宝塔面板中，点击网站设置 → 配置文件，替换为：

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    root /www/wwwroot/your-domain.com/public;
    index index.php index.html;

    # SSL配置
    ssl_certificate /www/server/panel/vhost/cert/your-domain.com/fullchain.pem;
    ssl_certificate_key /www/server/panel/vhost/cert/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:HIGH:!aNULL:!MD5:!RC4:!DHE;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

    # 文件上传限制
    client_max_body_size 100M;
    client_body_timeout 60s;

    # 访问日志
    access_log /www/wwwlogs/your-domain.com.log;
    error_log /www/wwwlogs/your-domain.com.error.log;

    # Laravel路由
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # PHP处理
    location ~ \.php$ {
        fastcgi_pass unix:/tmp/php-cgi-72.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        include fastcgi_params;
        
        # 超时设置
        fastcgi_connect_timeout 300;
        fastcgi_send_timeout 300;
        fastcgi_read_timeout 300;
        fastcgi_buffer_size 64k;
        fastcgi_buffers 4 64k;
        fastcgi_busy_buffers_size 128k;
    }

    # WebSocket代理
    location /ws {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # 安全配置
    location ~ /\.(ht|env) {
        deny all;
    }

    location ~ ^/(storage|vendor|tests|database)/ {
        deny all;
    }

    # 禁止访问敏感文件
    location ~* \.(sql|log|conf)$ {
        deny all;
    }
}
```

## 第七步：配置系统服务

### 7.1 创建WebSocket服务
```bash
# 创建服务文件
vim /etc/systemd/system/chat-websocket.service
```

```ini
[Unit]
Description=Chat WebSocket Server
After=network.target mysql.service redis.service

[Service]
Type=simple
User=www
Group=www
WorkingDirectory=/www/wwwroot/your-domain.com
ExecStart=/usr/bin/php artisan websocket:serve
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 7.2 创建队列服务
```bash
# 创建队列服务文件
vim /etc/systemd/system/chat-queue.service
```

```ini
[Unit]
Description=Chat Queue Worker
After=network.target mysql.service redis.service

[Service]
Type=simple
User=www
Group=www
WorkingDirectory=/www/wwwroot/your-domain.com
ExecStart=/usr/bin/php artisan queue:work --sleep=3 --tries=3 --max-time=3600
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

### 7.3 启动服务
```bash
# 重载systemd配置
systemctl daemon-reload

# 启动并设置开机自启
systemctl enable chat-websocket
systemctl start chat-websocket

systemctl enable chat-queue
systemctl start chat-queue

# 检查服务状态
systemctl status chat-websocket
systemctl status chat-queue
```

### 7.4 配置定时任务
```bash
# 编辑crontab
crontab -e -u www

# 添加Laravel调度任务
* * * * * cd /www/wwwroot/your-domain.com && php artisan schedule:run >> /dev/null 2>&1

# 添加日志清理任务（每天凌晨2点）
0 2 * * * find /www/wwwroot/your-domain.com/storage/logs -name "*.log" -mtime +30 -delete

# 添加系统优化任务（每周一凌晨1点）
0 1 * * 1 cd /www/wwwroot/your-domain.com && php artisan system:optimize --all >> /dev/null 2>&1
```

## 第八步：设置文件权限

```bash
cd /www/wwwroot/your-domain.com

# 设置所有者
chown -R www:www .

# 设置目录权限
find . -type d -exec chmod 755 {} \;

# 设置文件权限
find . -type f -exec chmod 644 {} \;

# 设置特殊权限
chmod -R 775 storage
chmod -R 775 bootstrap/cache
chmod 644 .env
```

## 第九步：安全加固

### 9.1 配置防火墙
```bash
# 只开放必要端口
firewall-cmd --permanent --remove-service=ssh  # 如果SSH端口已改
firewall-cmd --permanent --add-port=22/tcp     # SSH端口
firewall-cmd --permanent --add-port=80/tcp     # HTTP
firewall-cmd --permanent --add-port=443/tcp    # HTTPS
firewall-cmd --permanent --add-port=8888/tcp   # 宝塔面板
firewall-cmd --permanent --add-port=8080/tcp   # WebSocket
firewall-cmd --reload
```

### 9.2 配置fail2ban（可选）
```bash
# 安装fail2ban
yum install -y epel-release
yum install -y fail2ban

# 配置fail2ban
cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
vim /etc/fail2ban/jail.local

# 启动fail2ban
systemctl enable fail2ban
systemctl start fail2ban
```

### 9.3 隐藏服务器信息
在Nginx配置中添加：
```nginx
# 隐藏Nginx版本
server_tokens off;

# 隐藏PHP版本
fastcgi_hide_header X-Powered-By;
```

## 第十步：监控和备份

### 10.1 配置监控
在宝塔面板中：
1. 安装"系统监控"插件
2. 设置监控告警
3. 配置邮件通知

### 10.2 配置自动备份
```bash
# 创建备份脚本
vim /www/backup/backup_chat.sh
```

```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/www/backup"
PROJECT_DIR="/www/wwwroot/your-domain.com"
DB_NAME="your_domain_com"
DB_USER="your_domain_com"
DB_PASS="your_database_password"

# 创建备份目录
mkdir -p $BACKUP_DIR/database
mkdir -p $BACKUP_DIR/files

# 备份数据库
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/database/chat_${DATE}.sql
gzip $BACKUP_DIR/database/chat_${DATE}.sql

# 备份文件
tar -czf $BACKUP_DIR/files/chat_files_${DATE}.tar.gz $PROJECT_DIR/storage/app/public $PROJECT_DIR/.env

# 清理30天前的备份
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
```

```bash
# 设置执行权限
chmod +x /www/backup/backup_chat.sh

# 添加到定时任务（每天凌晨3点备份）
echo "0 3 * * * /www/backup/backup_chat.sh >> /www/backup/backup.log 2>&1" | crontab -
```

## 第十一步：性能优化

### 11.1 MySQL优化
编辑MySQL配置：
```bash
vim /etc/my.cnf
```

```ini
[mysqld]
# 基础配置
max_connections = 200
max_connect_errors = 10000
table_open_cache = 2048
max_allowed_packet = 64M

# InnoDB配置
innodb_buffer_pool_size = 1G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2

# 查询缓存
query_cache_type = 1
query_cache_size = 128M
query_cache_limit = 2M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

### 11.2 Redis优化
已在前面配置中包含

### 11.3 PHP-FPM优化
```bash
vim /www/server/php/72/etc/php-fpm.conf
```

```ini
[www]
pm = dynamic
pm.max_children = 50
pm.start_servers = 10
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 1000
```

## 第十二步：测试验证

### 12.1 功能测试
```bash
cd /www/wwwroot/your-domain.com

# 运行系统测试
php tests/run_tests.php https://your-domain.com

# 检查服务状态
systemctl status nginx
systemctl status mysql
systemctl status redis
systemctl status chat-websocket
systemctl status chat-queue
```

### 12.2 性能测试
```bash
# 安装压测工具
yum install -y httpd-tools

# 简单压测
ab -n 1000 -c 10 https://your-domain.com/

# 检查WebSocket连接
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" http://your-domain.com:8080/
```

## 第十三步：创建管理员账户

### 13.1 使用Artisan命令
```bash
cd /www/wwwroot/your-domain.com
php artisan tinker
```

```php
$user = new App\Models\User();
$user->name = 'Administrator';
$user->email = '<EMAIL>';
$user->password = Hash::make('your_secure_password');
$user->role = 'admin';
$user->is_active = true;
$user->site_id = 1;
$user->save();
```

## 故障排除

### 常见问题
1. **WebSocket连接失败**: 检查防火墙和服务状态
2. **文件上传失败**: 检查目录权限和PHP配置
3. **数据库连接失败**: 检查数据库服务和连接信息
4. **Redis连接失败**: 检查Redis服务和密码配置
5. **SSL证书问题**: 检查证书文件路径和权限

### 日志查看
```bash
# 应用日志
tail -f /www/wwwroot/your-domain.com/storage/logs/laravel.log

# Nginx日志
tail -f /www/wwwlogs/your-domain.com.log
tail -f /www/wwwlogs/your-domain.com.error.log

# 系统服务日志
journalctl -u chat-websocket -f
journalctl -u chat-queue -f
```

---

**生产环境部署完成！** 🚀

现在您可以通过 https://your-domain.com 访问系统，开始正式使用客服系统。

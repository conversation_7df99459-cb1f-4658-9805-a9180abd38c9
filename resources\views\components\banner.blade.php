<div class="custom-component custom-component-{{ $component->component_id }} banner-component" 
     data-component-id="{{ $component->component_id }}"
     data-component-type="{{ $component->type }}">
    
    @if($settings['show_close_button'] ?? true)
    <button class="banner-close" onclick="this.parentElement.style.display='none'">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
        </svg>
    </button>
    @endif
    
    <div class="banner-content">
        @if($settings['link_url'] ?? false)
        <a href="{{ $settings['link_url'] }}" class="banner-link">
            {{ $settings['text'] ?? $content ?? 'Banner message' }}
        </a>
        @else
        {{ $settings['text'] ?? $content ?? 'Banner message' }}
        @endif
    </div>
</div>

<style>
.custom-component-{{ $component->component_id }} {
    position: relative;
    background-color: {{ $settings['background_color'] ?? '#3B82F6' }};
    color: {{ $settings['text_color'] ?? '#FFFFFF' }};
    padding: 12px 16px;
    text-align: center;
    font-weight: 500;
    @if($styles)
        @foreach($styles as $property => $value)
        {{ str_replace('_', '-', $property) }}: {{ $value }};
        @endforeach
    @endif
}

.custom-component-{{ $component->component_id }} .banner-close {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    opacity: 0.8;
}

.custom-component-{{ $component->component_id }} .banner-close:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.1);
}

.custom-component-{{ $component->component_id }} .banner-link {
    color: inherit;
    text-decoration: none;
}

.custom-component-{{ $component->component_id }} .banner-link:hover {
    text-decoration: underline;
}

@if($settings['auto_hide_delay'] ?? false)
.custom-component-{{ $component->component_id }} {
    animation: bannerAutoHide {{ $settings['auto_hide_delay'] }}s ease-in-out forwards;
}

@keyframes bannerAutoHide {
    0%, 90% { opacity: 1; }
    100% { opacity: 0; display: none; }
}
@endif
</style>

@if($settings['auto_hide_delay'] ?? false)
<script>
setTimeout(function() {
    const banner = document.querySelector('.custom-component-{{ $component->component_id }}');
    if (banner) {
        banner.style.display = 'none';
    }
}, {{ $settings['auto_hide_delay'] * 1000 }});
</script>
@endif

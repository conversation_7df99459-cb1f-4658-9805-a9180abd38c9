<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVisitorsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('visitors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('site_id')->constrained()->onDelete('cascade');
            $table->string('visitor_id')->unique(); // UUID for anonymous visitors
            $table->string('name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            
            // Browser & Device Info
            $table->string('user_agent')->nullable();
            $table->string('browser')->nullable();
            $table->string('browser_version')->nullable();
            $table->string('os')->nullable();
            $table->string('device_type')->nullable(); // desktop, mobile, tablet
            $table->string('screen_resolution')->nullable();
            
            // Location Info
            $table->string('ip_address')->nullable();
            $table->string('country')->nullable();
            $table->string('country_code')->nullable();
            $table->string('region')->nullable();
            $table->string('city')->nullable();
            $table->string('timezone')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            
            // Referrer Info
            $table->text('referrer_url')->nullable();
            $table->string('referrer_domain')->nullable();
            $table->string('utm_source')->nullable();
            $table->string('utm_medium')->nullable();
            $table->string('utm_campaign')->nullable();
            $table->string('utm_term')->nullable();
            $table->string('utm_content')->nullable();
            
            // Visit Info
            $table->timestamp('first_visit_at');
            $table->timestamp('last_visit_at');
            $table->integer('visit_count')->default(1);
            $table->integer('page_views')->default(0);
            $table->integer('total_time_spent')->default(0); // in seconds
            
            // GDPR Compliance
            $table->boolean('gdpr_consent')->default(false);
            $table->timestamp('gdpr_consent_at')->nullable();
            $table->json('gdpr_data')->nullable();
            
            $table->timestamps();
            
            $table->index(['site_id', 'visitor_id']);
            $table->index(['site_id', 'ip_address']);
            $table->index(['site_id', 'first_visit_at']);
            $table->index(['site_id', 'last_visit_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('visitors');
    }
}

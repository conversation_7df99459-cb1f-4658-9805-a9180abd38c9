========================================
    客服系统手动部署指南
========================================

如果自动脚本无法运行，请按照以下步骤手动部署：

第一步：检查环境
----------------------------------------
1. 确认宝塔面板已安装并运行
2. 确认已安装 PHP 7.2 (路径通常是 D:\BtSoft\php\72\php.exe)
3. 确认已安装 MySQL 和 Redis
4. 确认已安装 Composer

第二步：下载 Composer（如果没有）
----------------------------------------
1. 访问：https://getcomposer.org/download/
2. 下载 Composer-Setup.exe
3. 安装时选择 PHP 路径：D:\BtSoft\php\72\php.exe
4. 安装完成后，打开命令提示符输入 composer --version 验证

第三步：手动执行命令
----------------------------------------
1. 以管理员身份打开命令提示符
2. 逐条执行以下命令：

cd /d D:\wwwroot

composer config -g secure-http false

composer config -g repo.packagist composer http://packagist.phpcomposer.com

composer create-project --prefer-dist laravel/laravel aichat.jagship.com "7.*"

cd aichat.jagship.com

php artisan key:generate

第四步：配置环境文件
----------------------------------------
1. 进入项目目录：D:\wwwroot\aichat.jagship.com
2. 复制 .env.example 为 .env
3. 编辑 .env 文件，修改以下内容：

APP_NAME="Customer Service System"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://aichat.jagship.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=您的数据库名
DB_USERNAME=您的数据库用户名
DB_PASSWORD=您的数据库密码

REDIS_HOST=127.0.0.1
REDIS_PORT=6379

第五步：在宝塔面板创建网站
----------------------------------------
1. 登录宝塔面板
2. 点击"网站" → "添加站点"
3. 域名：aichat.jagship.com
4. 根目录：D:\wwwroot\aichat.jagship.com\public
5. PHP版本：7.2
6. 创建数据库：是
7. 提交

第六步：配置 PHP 扩展
----------------------------------------
1. 软件商店 → 已安装 → PHP 7.2 → 设置
2. 安装扩展：redis, opcache, fileinfo, zip, curl, gd
3. 禁用函数：删除 putenv, getenv
4. 重载配置

第七步：运行数据库迁移
----------------------------------------
在命令提示符中执行：

cd /d D:\wwwroot\aichat.jagship.com
php artisan migrate

第八步：测试访问
----------------------------------------
1. 配置 hosts 文件（C:\Windows\System32\drivers\etc\hosts）
   添加：127.0.0.1 aichat.jagship.com
2. 浏览器访问：http://aichat.jagship.com

常见问题解决：
----------------------------------------
1. 如果提示 "Could not open input file: artisan"
   - 检查是否在正确的项目目录中
   - 检查 artisan 文件是否存在

2. 如果提示 SSL 证书错误
   - 执行：composer config -g secure-http false

3. 如果提示 putenv() 被禁用
   - 在宝塔面板 PHP 设置中删除 putenv 禁用函数

4. 如果数据库连接失败
   - 检查 .env 文件中的数据库配置
   - 确认数据库服务已启动

5. 如果页面显示 500 错误
   - 检查 storage 目录权限
   - 查看 storage/logs/laravel.log 错误日志

联系支持：
----------------------------------------
如果仍有问题，请提供以下信息：
1. 错误截图
2. 执行的具体命令
3. 错误日志内容
4. 宝塔面板版本和PHP版本

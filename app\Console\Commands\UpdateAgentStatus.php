<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\WorkModeService;
use Illuminate\Support\Facades\Log;

class UpdateAgentStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agents:update-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update agent status based on working hours';

    /**
     * The work mode service instance.
     *
     * @var WorkModeService
     */
    protected $workModeService;

    /**
     * Create a new command instance.
     *
     * @param WorkModeService $workModeService
     */
    public function __construct(WorkModeService $workModeService)
    {
        parent::__construct();
        $this->workModeService = $workModeService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Updating agent status based on working hours...');

        try {
            $this->workModeService->updateAgentStatusByWorkingHours();
            
            $this->info('Agent status updated successfully.');
            
            Log::info('Agent status update command completed successfully');
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error('Failed to update agent status: ' . $e->getMessage());
            
            Log::error('Agent status update command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            
            return Command::FAILURE;
        }
    }
}

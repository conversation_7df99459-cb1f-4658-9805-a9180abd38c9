<template>
  <div class="fixed inset-0 flex items-end justify-center px-4 py-6 pointer-events-none sm:p-6 sm:items-start sm:justify-end z-50">
    <div class="w-full flex flex-col items-center space-y-4 sm:items-end">
      <transition-group
        name="notification"
        tag="div"
        class="w-full flex flex-col items-center space-y-4 sm:items-end"
      >
        <div
          v-for="notification in notifications"
          :key="notification.id"
          class="max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
        >
          <div class="p-4">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <component :is="getIcon(notification.type)" class="h-6 w-6" :class="getIconColor(notification.type)" />
              </div>
              <div class="ml-3 w-0 flex-1 pt-0.5">
                <p class="text-sm font-medium text-gray-900">{{ notification.title }}</p>
                <p class="mt-1 text-sm text-gray-500">{{ notification.message }}</p>
                <div v-if="notification.action" class="mt-3 flex space-x-7">
                  <button
                    @click="handleAction(notification)"
                    class="bg-white rounded-md text-sm font-medium text-blue-600 hover:text-blue-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    {{ notification.action.label }}
                  </button>
                </div>
              </div>
              <div class="ml-4 flex-shrink-0 flex">
                <button
                  @click="removeNotification(notification.id)"
                  class="bg-white rounded-md inline-flex text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <span class="sr-only">Close</span>
                  <XMarkIcon class="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
          
          <!-- Progress bar for timed notifications -->
          <div v-if="notification.duration > 0" class="h-1 bg-gray-200">
            <div
              class="h-full transition-all duration-100 ease-linear"
              :class="getProgressBarColor(notification.type)"
              :style="{ width: getProgressWidth(notification) + '%' }"
            ></div>
          </div>
        </div>
      </transition-group>
    </div>
  </div>
</template>

<script>
import { computed, onMounted, onUnmounted } from 'vue'
import { useNotificationStore } from '../../stores/notifications.js'
import {
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XCircleIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

export default {
  name: 'NotificationContainer',
  components: {
    CheckCircleIcon,
    ExclamationTriangleIcon,
    InformationCircleIcon,
    XCircleIcon,
    XMarkIcon
  },
  setup() {
    const notificationStore = useNotificationStore()

    const notifications = computed(() => notificationStore.notifications)

    const getIcon = (type) => {
      switch (type) {
        case 'success':
          return CheckCircleIcon
        case 'warning':
          return ExclamationTriangleIcon
        case 'error':
          return XCircleIcon
        default:
          return InformationCircleIcon
      }
    }

    const getIconColor = (type) => {
      switch (type) {
        case 'success':
          return 'text-green-400'
        case 'warning':
          return 'text-yellow-400'
        case 'error':
          return 'text-red-400'
        default:
          return 'text-blue-400'
      }
    }

    const getProgressBarColor = (type) => {
      switch (type) {
        case 'success':
          return 'bg-green-400'
        case 'warning':
          return 'bg-yellow-400'
        case 'error':
          return 'bg-red-400'
        default:
          return 'bg-blue-400'
      }
    }

    const getProgressWidth = (notification) => {
      if (notification.duration === 0) return 100
      
      const elapsed = Date.now() - notification.createdAt.getTime()
      const progress = Math.max(0, 100 - (elapsed / notification.duration) * 100)
      return progress
    }

    const removeNotification = (id) => {
      notificationStore.removeNotification(id)
    }

    const handleAction = (notification) => {
      if (notification.action && notification.action.handler) {
        notification.action.handler()
      }
      removeNotification(notification.id)
    }

    // Update progress bars
    let progressInterval
    onMounted(() => {
      progressInterval = setInterval(() => {
        // Force reactivity update for progress bars
        notifications.value.forEach(notification => {
          if (notification.duration > 0) {
            const elapsed = Date.now() - notification.createdAt.getTime()
            if (elapsed >= notification.duration) {
              removeNotification(notification.id)
            }
          }
        })
      }, 100)
    })

    onUnmounted(() => {
      if (progressInterval) {
        clearInterval(progressInterval)
      }
    })

    return {
      notifications,
      getIcon,
      getIconColor,
      getProgressBarColor,
      getProgressWidth,
      removeNotification,
      handleAction
    }
  }
}
</script>

<style scoped>
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}
</style>

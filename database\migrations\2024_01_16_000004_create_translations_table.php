<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('st_translations', function (Blueprint $table) {
            $table->id();
            $table->string('translation_id')->unique();
            $table->unsignedBigInteger('language_id');
            $table->string('namespace')->nullable(); // For organizing translations (e.g., 'admin', 'frontend')
            $table->string('group')->nullable(); // Translation group (e.g., 'chat', 'system', 'validation')
            $table->string('key'); // Translation key (e.g., 'welcome_message', 'send_button')
            $table->text('value'); // Translation value
            $table->boolean('is_system')->default(false); // System vs custom translations
            $table->boolean('is_approved')->default(false); // Translation approval status
            $table->text('context')->nullable(); // Context or description for translators
            $table->json('metadata')->nullable(); // Additional metadata
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['language_id', 'namespace', 'group']);
            $table->index(['language_id', 'key']);
            $table->index(['language_id', 'is_approved']);
            $table->index(['language_id', 'is_system']);
            $table->index('translation_id');
            
            // Unique constraint: one translation per language/namespace/group/key combination
            $table->unique(['language_id', 'namespace', 'group', 'key'], 'unique_translation_key');
            
            // Foreign key constraint
            $table->foreign('language_id')->references('id')->on('st_languages')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('st_translations');
    }
};

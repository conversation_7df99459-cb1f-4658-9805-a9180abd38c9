<template>
  <div>
    <!-- Hero section -->
    <div class="relative bg-white overflow-hidden">
      <div class="max-w-7xl mx-auto">
        <div class="relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
          <svg class="hidden lg:block absolute right-0 inset-y-0 h-full w-48 text-white transform translate-x-1/2" fill="currentColor" viewBox="0 0 100 100" preserveAspectRatio="none" aria-hidden="true">
            <polygon points="50,0 100,0 50,100 0,100" />
          </svg>

          <main class="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
            <div class="sm:text-center lg:text-left">
              <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
                <span class="block xl:inline">Need help?</span>
                <span class="block text-blue-600 xl:inline">We're here for you</span>
              </h1>
              <p class="mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                Get instant support from our friendly customer service team. We're available 24/7 to help you with any questions or concerns.
              </p>
              <div class="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                <div class="rounded-md shadow">
                  <router-link to="/chat" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 md:py-4 md:text-lg md:px-10">
                    Start Live Chat
                  </router-link>
                </div>
                <div class="mt-3 sm:mt-0 sm:ml-3">
                  <router-link to="/contact" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 md:py-4 md:text-lg md:px-10">
                    Contact Us
                  </router-link>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
      <div class="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
        <img class="h-56 w-full object-cover sm:h-72 md:h-96 lg:w-full lg:h-full" src="/images/customer-service-hero.jpg" alt="Customer service representative">
      </div>
    </div>

    <!-- Features section -->
    <div class="py-12 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="lg:text-center">
          <h2 class="text-base text-blue-600 font-semibold tracking-wide uppercase">Support</h2>
          <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
            How can we help you today?
          </p>
          <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
            Choose the support option that works best for you. Our team is ready to assist you.
          </p>
        </div>

        <div class="mt-10">
          <dl class="space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10">
            <div class="relative">
              <dt>
                <div class="absolute flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
                  <ChatBubbleLeftRightIcon class="h-6 w-6" />
                </div>
                <p class="ml-16 text-lg leading-6 font-medium text-gray-900">Live Chat</p>
              </dt>
              <dd class="mt-2 ml-16 text-base text-gray-500">
                Get instant help from our support team. Chat with a real person in real-time.
              </dd>
              <div class="mt-4 ml-16">
                <router-link to="/chat" class="text-blue-600 hover:text-blue-500 font-medium">
                  Start chatting →
                </router-link>
              </div>
            </div>

            <div class="relative">
              <dt>
                <div class="absolute flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
                  <EnvelopeIcon class="h-6 w-6" />
                </div>
                <p class="ml-16 text-lg leading-6 font-medium text-gray-900">Email Support</p>
              </dt>
              <dd class="mt-2 ml-16 text-base text-gray-500">
                Send us a detailed message and we'll get back to you within 24 hours.
              </dd>
              <div class="mt-4 ml-16">
                <router-link to="/contact" class="text-blue-600 hover:text-blue-500 font-medium">
                  Send message →
                </router-link>
              </div>
            </div>

            <div class="relative">
              <dt>
                <div class="absolute flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
                  <PhoneIcon class="h-6 w-6" />
                </div>
                <p class="ml-16 text-lg leading-6 font-medium text-gray-900">Phone Support</p>
              </dt>
              <dd class="mt-2 ml-16 text-base text-gray-500">
                Call us directly for urgent matters. Available during business hours.
              </dd>
              <div class="mt-4 ml-16">
                <a href="tel:******-0123" class="text-blue-600 hover:text-blue-500 font-medium">
                  +****************
                </a>
              </div>
            </div>

            <div class="relative">
              <dt>
                <div class="absolute flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
                  <QuestionMarkCircleIcon class="h-6 w-6" />
                </div>
                <p class="ml-16 text-lg leading-6 font-medium text-gray-900">FAQ</p>
              </dt>
              <dd class="mt-2 ml-16 text-base text-gray-500">
                Find answers to common questions in our comprehensive FAQ section.
              </dd>
              <div class="mt-4 ml-16">
                <a href="#faq" class="text-blue-600 hover:text-blue-500 font-medium">
                  Browse FAQ →
                </a>
              </div>
            </div>
          </dl>
        </div>
      </div>
    </div>

    <!-- FAQ section -->
    <div id="faq" class="bg-gray-50">
      <div class="max-w-7xl mx-auto py-12 px-4 sm:py-16 sm:px-6 lg:px-8">
        <div class="max-w-3xl mx-auto divide-y-2 divide-gray-200">
          <h2 class="text-center text-3xl font-extrabold text-gray-900 sm:text-4xl">
            Frequently Asked Questions
          </h2>
          <dl class="mt-6 space-y-6 divide-y divide-gray-200">
            <div v-for="faq in faqs" :key="faq.id" class="pt-6">
              <dt class="text-lg">
                <button @click="toggleFaq(faq.id)" class="text-left w-full flex justify-between items-start text-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                  <span class="font-medium text-gray-900">{{ faq.question }}</span>
                  <span class="ml-6 h-7 flex items-center">
                    <ChevronDownIcon class="h-6 w-6 transform transition-transform duration-200" :class="{ 'rotate-180': openFaqs.includes(faq.id) }" />
                  </span>
                </button>
              </dt>
              <dd v-if="openFaqs.includes(faq.id)" class="mt-2 pr-12">
                <p class="text-base text-gray-500">{{ faq.answer }}</p>
              </dd>
            </div>
          </dl>
        </div>
      </div>
    </div>

    <!-- CTA section -->
    <div class="bg-blue-600">
      <div class="max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-extrabold text-white sm:text-4xl">
          <span class="block">Still need help?</span>
          <span class="block">Start a conversation with us.</span>
        </h2>
        <p class="mt-4 text-lg leading-6 text-blue-200">
          Our support team is standing by to help you with any questions or concerns.
        </p>
        <router-link to="/chat" class="mt-8 w-full inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-blue-600 bg-white hover:bg-blue-50 sm:w-auto">
          Start Live Chat Now
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import {
  ChatBubbleLeftRightIcon,
  EnvelopeIcon,
  PhoneIcon,
  QuestionMarkCircleIcon,
  ChevronDownIcon
} from '@heroicons/vue/24/outline'

export default {
  name: 'Home',
  components: {
    ChatBubbleLeftRightIcon,
    EnvelopeIcon,
    PhoneIcon,
    QuestionMarkCircleIcon,
    ChevronDownIcon
  },
  setup() {
    const openFaqs = ref([])

    const faqs = [
      {
        id: 1,
        question: "What are your support hours?",
        answer: "Our live chat support is available 24/7. Email support responses are typically sent within 24 hours during business days. Phone support is available Monday-Friday, 9 AM to 6 PM EST."
      },
      {
        id: 2,
        question: "How quickly will I get a response?",
        answer: "Live chat responses are immediate during staffed hours. Email responses are typically sent within 24 hours. Phone support provides immediate assistance during business hours."
      },
      {
        id: 3,
        question: "Can I attach files to my support request?",
        answer: "Yes, you can attach files when using our contact form or during live chat sessions. We accept most common file formats including images, documents, and screenshots."
      },
      {
        id: 4,
        question: "Is my information secure?",
        answer: "Absolutely. We use industry-standard encryption and security measures to protect your personal information and conversation data. Your privacy is our top priority."
      },
      {
        id: 5,
        question: "Can I request a specific agent?",
        answer: "While we can't guarantee a specific agent will be available, you can mention your preference in your message and we'll do our best to accommodate your request."
      },
      {
        id: 6,
        question: "What if I'm not satisfied with the support I receive?",
        answer: "We strive for excellent customer service. If you're not satisfied, please ask to speak with a supervisor or use our feedback system to let us know how we can improve."
      }
    ]

    const toggleFaq = (id) => {
      const index = openFaqs.value.indexOf(id)
      if (index > -1) {
        openFaqs.value.splice(index, 1)
      } else {
        openFaqs.value.push(id)
      }
    }

    return {
      faqs,
      openFaqs,
      toggleFaq
    }
  }
}
</script>

<?php

namespace App\WebSocket;

use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\User;
use App\Models\Visitor;

class ChatServer implements MessageComponentInterface
{
    protected $clients;
    protected $sessions;
    protected $agents;
    protected $visitors;

    public function __construct()
    {
        $this->clients = new \SplObjectStorage;
        $this->sessions = [];
        $this->agents = [];
        $this->visitors = [];
    }

    public function onOpen(ConnectionInterface $conn)
    {
        $this->clients->attach($conn);
        
        Log::info("New connection! ({$conn->resourceId})");
        
        // Send welcome message
        $conn->send(json_encode([
            'type' => 'connection',
            'status' => 'connected',
            'connection_id' => $conn->resourceId,
            'timestamp' => now()->toISOString()
        ]));
    }

    public function onMessage(ConnectionInterface $from, $msg)
    {
        try {
            $data = json_decode($msg, true);
            
            if (!$data || !isset($data['type'])) {
                $this->sendError($from, 'Invalid message format');
                return;
            }

            Log::info("Message received", ['type' => $data['type'], 'from' => $from->resourceId]);

            switch ($data['type']) {
                case 'auth':
                    $this->handleAuth($from, $data);
                    break;
                    
                case 'visitor_join':
                    $this->handleVisitorJoin($from, $data);
                    break;
                    
                case 'agent_join':
                    $this->handleAgentJoin($from, $data);
                    break;
                    
                case 'chat_message':
                    $this->handleChatMessage($from, $data);
                    break;
                    
                case 'typing_start':
                    $this->handleTypingStart($from, $data);
                    break;
                    
                case 'typing_stop':
                    $this->handleTypingStop($from, $data);
                    break;
                    
                case 'session_transfer':
                    $this->handleSessionTransfer($from, $data);
                    break;
                    
                case 'session_close':
                    $this->handleSessionClose($from, $data);
                    break;
                    
                case 'agent_status':
                    $this->handleAgentStatus($from, $data);
                    break;
                    
                case 'heartbeat':
                    $this->handleHeartbeat($from, $data);
                    break;
                    
                default:
                    $this->sendError($from, 'Unknown message type: ' . $data['type']);
            }
            
        } catch (\Exception $e) {
            Log::error('WebSocket message handling error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->sendError($from, 'Internal server error');
        }
    }

    public function onClose(ConnectionInterface $conn)
    {
        $this->clients->detach($conn);
        
        // Clean up agent/visitor data
        $this->cleanupConnection($conn);
        
        Log::info("Connection {$conn->resourceId} has disconnected");
    }

    public function onError(ConnectionInterface $conn, \Exception $e)
    {
        Log::error("WebSocket error on connection {$conn->resourceId}", [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        
        $conn->close();
    }

    protected function handleAuth($conn, $data)
    {
        if (!isset($data['token'])) {
            $this->sendError($conn, 'Authentication token required');
            return;
        }

        // Validate token and get user info
        // This would integrate with your authentication system
        $conn->send(json_encode([
            'type' => 'auth_success',
            'user_id' => $data['user_id'] ?? null,
            'timestamp' => now()->toISOString()
        ]));
    }

    protected function handleVisitorJoin($conn, $data)
    {
        $sessionId = $data['session_id'] ?? null;
        $siteId = $data['site_id'] ?? null;
        $visitorId = $data['visitor_id'] ?? null;

        if (!$sessionId || !$siteId || !$visitorId) {
            $this->sendError($conn, 'Missing required parameters');
            return;
        }

        // Store visitor connection
        $this->visitors[$conn->resourceId] = [
            'connection' => $conn,
            'session_id' => $sessionId,
            'site_id' => $siteId,
            'visitor_id' => $visitorId,
            'joined_at' => now()
        ];

        // Add to session
        if (!isset($this->sessions[$sessionId])) {
            $this->sessions[$sessionId] = [
                'visitors' => [],
                'agents' => []
            ];
        }
        
        $this->sessions[$sessionId]['visitors'][$conn->resourceId] = $conn;

        // Notify agents about new visitor
        $this->notifyAgents($siteId, [
            'type' => 'visitor_joined',
            'session_id' => $sessionId,
            'visitor_id' => $visitorId,
            'timestamp' => now()->toISOString()
        ]);

        $conn->send(json_encode([
            'type' => 'visitor_joined',
            'session_id' => $sessionId,
            'status' => 'success',
            'timestamp' => now()->toISOString()
        ]));
    }

    protected function handleAgentJoin($conn, $data)
    {
        $userId = $data['user_id'] ?? null;
        $siteId = $data['site_id'] ?? null;

        if (!$userId || !$siteId) {
            $this->sendError($conn, 'Missing required parameters');
            return;
        }

        // Store agent connection
        $this->agents[$conn->resourceId] = [
            'connection' => $conn,
            'user_id' => $userId,
            'site_id' => $siteId,
            'status' => 'online',
            'joined_at' => now()
        ];

        // Update agent status in Redis
        Redis::hset("agent_status:{$siteId}", $userId, json_encode([
            'status' => 'online',
            'connection_id' => $conn->resourceId,
            'last_seen' => now()->toISOString()
        ]));

        $conn->send(json_encode([
            'type' => 'agent_joined',
            'user_id' => $userId,
            'status' => 'success',
            'timestamp' => now()->toISOString()
        ]));
    }

    protected function handleChatMessage($conn, $data)
    {
        $sessionId = $data['session_id'] ?? null;
        $message = $data['message'] ?? null;
        $senderType = $data['sender_type'] ?? 'visitor';

        if (!$sessionId || !$message) {
            $this->sendError($conn, 'Missing required parameters');
            return;
        }

        // Save message to database
        $chatMessage = ChatMessage::create([
            'chat_session_id' => $sessionId,
            'user_id' => $senderType === 'agent' ? ($data['user_id'] ?? null) : null,
            'message_id' => \Str::uuid(),
            'content' => $message,
            'sender_type' => $senderType,
            'type' => $data['message_type'] ?? 'text',
            'metadata' => $data['metadata'] ?? null
        ]);

        // Broadcast message to session participants
        $this->broadcastToSession($sessionId, [
            'type' => 'new_message',
            'message_id' => $chatMessage->message_id,
            'session_id' => $sessionId,
            'content' => $message,
            'sender_type' => $senderType,
            'user_id' => $data['user_id'] ?? null,
            'timestamp' => $chatMessage->created_at->toISOString()
        ]);
    }

    protected function broadcastToSession($sessionId, $message)
    {
        if (!isset($this->sessions[$sessionId])) {
            return;
        }

        $session = $this->sessions[$sessionId];
        $messageJson = json_encode($message);

        // Send to all visitors in session
        foreach ($session['visitors'] as $conn) {
            if ($conn->getConnection()->getState() === \Ratchet\RFC6455\Messaging\MessageInterface::TYPE_TEXT) {
                $conn->send($messageJson);
            }
        }

        // Send to all agents in session
        foreach ($session['agents'] as $conn) {
            if ($conn->getConnection()->getState() === \Ratchet\RFC6455\Messaging\MessageInterface::TYPE_TEXT) {
                $conn->send($messageJson);
            }
        }
    }

    protected function notifyAgents($siteId, $message)
    {
        $messageJson = json_encode($message);
        
        foreach ($this->agents as $agent) {
            if ($agent['site_id'] == $siteId) {
                $agent['connection']->send($messageJson);
            }
        }
    }

    protected function sendError($conn, $message)
    {
        $conn->send(json_encode([
            'type' => 'error',
            'message' => $message,
            'timestamp' => now()->toISOString()
        ]));
    }

    protected function cleanupConnection($conn)
    {
        $connectionId = $conn->resourceId;
        
        // Remove from agents
        if (isset($this->agents[$connectionId])) {
            $agent = $this->agents[$connectionId];
            Redis::hdel("agent_status:{$agent['site_id']}", $agent['user_id']);
            unset($this->agents[$connectionId]);
        }
        
        // Remove from visitors
        if (isset($this->visitors[$connectionId])) {
            $visitor = $this->visitors[$connectionId];
            $sessionId = $visitor['session_id'];
            
            if (isset($this->sessions[$sessionId]['visitors'][$connectionId])) {
                unset($this->sessions[$sessionId]['visitors'][$connectionId]);
            }
            
            unset($this->visitors[$connectionId]);
        }
        
        // Clean up empty sessions
        foreach ($this->sessions as $sessionId => $session) {
            if (empty($session['visitors']) && empty($session['agents'])) {
                unset($this->sessions[$sessionId]);
            }
        }
    }

    protected function handleHeartbeat($conn, $data)
    {
        $conn->send(json_encode([
            'type' => 'heartbeat_response',
            'timestamp' => now()->toISOString()
        ]));
    }
}

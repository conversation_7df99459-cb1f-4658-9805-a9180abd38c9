<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Visitor;
use App\Models\VisitorBehavior;
use App\Models\ChatSession;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Carbon\Carbon;

class VisitorController extends Controller
{
    /**
     * Initialize visitor session
     */
    public function initSession(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'site_id' => 'required|exists:sites,id',
            'page_url' => 'required|url',
            'referrer' => 'nullable|url',
            'user_agent' => 'nullable|string',
            'screen_resolution' => 'nullable|string',
            'language' => 'nullable|string|max:10',
            'timezone' => 'nullable|string|max:50'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check for existing visitor by IP and user agent
        $visitor = Visitor::where('ip_address', $request->ip())
            ->where('user_agent', $request->user_agent)
            ->where('site_id', $request->site_id)
            ->where('last_visit_at', '>=', now()->subHours(24))
            ->first();

        if (!$visitor) {
            $visitor = Visitor::create([
                'visitor_id' => Str::uuid(),
                'site_id' => $request->site_id,
                'ip_address' => $request->ip(),
                'user_agent' => $request->user_agent,
                'language' => $request->language ?? 'en',
                'timezone' => $request->timezone,
                'first_visit_at' => now(),
                'last_visit_at' => now(),
                'visit_count' => 1
            ]);
        } else {
            $visitor->update([
                'last_visit_at' => now(),
                'visit_count' => $visitor->visit_count + 1
            ]);
        }

        // Track page visit
        $this->trackPageVisit($visitor, $request);

        return response()->json([
            'success' => true,
            'data' => [
                'visitor_id' => $visitor->visitor_id,
                'session_id' => Str::uuid(), // Generate session ID for tracking
                'is_returning' => $visitor->visit_count > 1
            ]
        ]);
    }

    /**
     * Track visitor behavior
     */
    public function trackBehavior(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'visitor_id' => 'required|string',
            'event_type' => 'required|in:page_view,click,scroll,mouse_move,form_interaction,time_spent',
            'page_url' => 'required|url',
            'element_selector' => 'nullable|string',
            'element_text' => 'nullable|string',
            'coordinates' => 'nullable|array',
            'coordinates.x' => 'nullable|integer',
            'coordinates.y' => 'nullable|integer',
            'scroll_depth' => 'nullable|integer|min:0|max:100',
            'time_spent' => 'nullable|integer|min:0',
            'form_field' => 'nullable|string',
            'additional_data' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $visitor = Visitor::where('visitor_id', $request->visitor_id)->first();

        if (!$visitor) {
            return response()->json([
                'success' => false,
                'message' => 'Visitor not found'
            ], 404);
        }

        // Create behavior record
        VisitorBehavior::create([
            'visitor_id' => $visitor->id,
            'event_type' => $request->event_type,
            'page_url' => $request->page_url,
            'element_selector' => $request->element_selector,
            'element_text' => $request->element_text,
            'coordinates_x' => $request->coordinates['x'] ?? null,
            'coordinates_y' => $request->coordinates['y'] ?? null,
            'scroll_depth' => $request->scroll_depth,
            'time_spent' => $request->time_spent,
            'form_field' => $request->form_field,
            'additional_data' => $request->additional_data ? json_encode($request->additional_data) : null,
            'created_at' => now()
        ]);

        // Update visitor last activity
        $visitor->update(['last_visit_at' => now()]);

        return response()->json([
            'success' => true,
            'message' => 'Behavior tracked successfully'
        ]);
    }

    /**
     * Get visitors list (admin)
     */
    public function index(Request $request): JsonResponse
    {
        $query = Visitor::with(['site', 'chatSessions'])
            ->withCount(['chatSessions', 'behaviors']);

        // Apply filters
        if ($request->has('site_id')) {
            $query->where('site_id', $request->site_id);
        }

        if ($request->has('date_from')) {
            $query->where('first_visit_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->where('first_visit_at', '<=', $request->date_to);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('ip_address', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'last_visit_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 20);
        $visitors = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => [
                'visitors' => $visitors->items(),
                'pagination' => [
                    'current_page' => $visitors->currentPage(),
                    'last_page' => $visitors->lastPage(),
                    'per_page' => $visitors->perPage(),
                    'total' => $visitors->total()
                ]
            ]
        ]);
    }

    /**
     * Get visitor details
     */
    public function show(Request $request, string $visitorId): JsonResponse
    {
        $visitor = Visitor::with(['site', 'chatSessions.agent'])
            ->withCount(['chatSessions', 'behaviors'])
            ->where('visitor_id', $visitorId)
            ->first();

        if (!$visitor) {
            return response()->json([
                'success' => false,
                'message' => 'Visitor not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'visitor' => $visitor,
                'stats' => [
                    'total_sessions' => $visitor->chat_sessions_count,
                    'total_behaviors' => $visitor->behaviors_count,
                    'average_session_duration' => $this->getAverageSessionDuration($visitor),
                    'most_visited_pages' => $this->getMostVisitedPages($visitor),
                    'device_info' => $this->getDeviceInfo($visitor)
                ]
            ]
        ]);
    }

    /**
     * Get visitor chat sessions
     */
    public function getSessions(Request $request, string $visitorId): JsonResponse
    {
        $visitor = Visitor::where('visitor_id', $visitorId)->first();

        if (!$visitor) {
            return response()->json([
                'success' => false,
                'message' => 'Visitor not found'
            ], 404);
        }

        $sessions = ChatSession::where('visitor_id', $visitor->id)
            ->with(['agent'])
            ->withCount('messages')
            ->orderBy('started_at', 'desc')
            ->paginate(10);

        return response()->json([
            'success' => true,
            'data' => [
                'sessions' => $sessions->items(),
                'pagination' => [
                    'current_page' => $sessions->currentPage(),
                    'last_page' => $sessions->lastPage(),
                    'per_page' => $sessions->perPage(),
                    'total' => $sessions->total()
                ]
            ]
        ]);
    }

    /**
     * Get visitor behavior data
     */
    public function getBehavior(Request $request, string $visitorId): JsonResponse
    {
        $visitor = Visitor::where('visitor_id', $visitorId)->first();

        if (!$visitor) {
            return response()->json([
                'success' => false,
                'message' => 'Visitor not found'
            ], 404);
        }

        $behaviors = VisitorBehavior::where('visitor_id', $visitor->id)
            ->orderBy('created_at', 'desc')
            ->paginate(50);

        // Get behavior analytics
        $analytics = [
            'page_views' => VisitorBehavior::where('visitor_id', $visitor->id)
                ->where('event_type', 'page_view')
                ->count(),
            'clicks' => VisitorBehavior::where('visitor_id', $visitor->id)
                ->where('event_type', 'click')
                ->count(),
            'form_interactions' => VisitorBehavior::where('visitor_id', $visitor->id)
                ->where('event_type', 'form_interaction')
                ->count(),
            'average_time_per_page' => VisitorBehavior::where('visitor_id', $visitor->id)
                ->where('event_type', 'time_spent')
                ->avg('time_spent'),
            'most_clicked_elements' => VisitorBehavior::where('visitor_id', $visitor->id)
                ->where('event_type', 'click')
                ->whereNotNull('element_text')
                ->groupBy('element_text')
                ->selectRaw('element_text, COUNT(*) as count')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->get()
        ];

        return response()->json([
            'success' => true,
            'data' => [
                'behaviors' => $behaviors->items(),
                'analytics' => $analytics,
                'pagination' => [
                    'current_page' => $behaviors->currentPage(),
                    'last_page' => $behaviors->lastPage(),
                    'per_page' => $behaviors->perPage(),
                    'total' => $behaviors->total()
                ]
            ]
        ]);
    }

    /**
     * Delete visitor (admin only)
     */
    public function deleteVisitor(Request $request, string $visitorId): JsonResponse
    {
        $visitor = Visitor::where('visitor_id', $visitorId)->first();

        if (!$visitor) {
            return response()->json([
                'success' => false,
                'message' => 'Visitor not found'
            ], 404);
        }

        // Delete related data
        VisitorBehavior::where('visitor_id', $visitor->id)->delete();
        ChatSession::where('visitor_id', $visitor->id)->delete();
        
        $visitor->delete();

        return response()->json([
            'success' => true,
            'message' => 'Visitor deleted successfully'
        ]);
    }

    /**
     * Track page visit
     */
    private function trackPageVisit(Visitor $visitor, Request $request): void
    {
        VisitorBehavior::create([
            'visitor_id' => $visitor->id,
            'event_type' => 'page_view',
            'page_url' => $request->page_url,
            'additional_data' => json_encode([
                'referrer' => $request->referrer,
                'screen_resolution' => $request->screen_resolution
            ]),
            'created_at' => now()
        ]);
    }

    /**
     * Get average session duration for visitor
     */
    private function getAverageSessionDuration(Visitor $visitor): ?float
    {
        return ChatSession::where('visitor_id', $visitor->id)
            ->whereNotNull('ended_at')
            ->avg(\DB::raw('TIMESTAMPDIFF(MINUTE, started_at, ended_at)'));
    }

    /**
     * Get most visited pages for visitor
     */
    private function getMostVisitedPages(Visitor $visitor): array
    {
        return VisitorBehavior::where('visitor_id', $visitor->id)
            ->where('event_type', 'page_view')
            ->groupBy('page_url')
            ->selectRaw('page_url, COUNT(*) as visits')
            ->orderBy('visits', 'desc')
            ->limit(5)
            ->get()
            ->toArray();
    }

    /**
     * Get device info for visitor
     */
    private function getDeviceInfo(Visitor $visitor): array
    {
        $userAgent = $visitor->user_agent;
        
        // Simple user agent parsing (in production, use a proper library)
        $device = 'Unknown';
        $browser = 'Unknown';
        $os = 'Unknown';

        if (strpos($userAgent, 'Mobile') !== false) {
            $device = 'Mobile';
        } elseif (strpos($userAgent, 'Tablet') !== false) {
            $device = 'Tablet';
        } else {
            $device = 'Desktop';
        }

        if (strpos($userAgent, 'Chrome') !== false) {
            $browser = 'Chrome';
        } elseif (strpos($userAgent, 'Firefox') !== false) {
            $browser = 'Firefox';
        } elseif (strpos($userAgent, 'Safari') !== false) {
            $browser = 'Safari';
        } elseif (strpos($userAgent, 'Edge') !== false) {
            $browser = 'Edge';
        }

        if (strpos($userAgent, 'Windows') !== false) {
            $os = 'Windows';
        } elseif (strpos($userAgent, 'Mac') !== false) {
            $os = 'macOS';
        } elseif (strpos($userAgent, 'Linux') !== false) {
            $os = 'Linux';
        } elseif (strpos($userAgent, 'Android') !== false) {
            $os = 'Android';
        } elseif (strpos($userAgent, 'iOS') !== false) {
            $os = 'iOS';
        }

        return [
            'device' => $device,
            'browser' => $browser,
            'os' => $os,
            'user_agent' => $userAgent
        ];
    }
}

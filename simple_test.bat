@echo off
title 简单测试 - 永不闪退
color 0F

echo.
echo ==========================================
echo    简单测试脚本 - 这个窗口不会关闭
echo ==========================================
echo.

echo 测试1: 显示当前目录
echo 当前目录: %CD%
echo.

echo 测试2: 检查PHP文件
if exist "D:\BtSoft\php\72\php.exe" (
    echo ✅ 找到 D:\BtSoft\php\72\php.exe
) else (
    echo ❌ 没找到 D:\BtSoft\php\72\php.exe
)

if exist "D:\BtSoft\php\73\php.exe" (
    echo ✅ 找到 D:\BtSoft\php\73\php.exe
) else (
    echo ❌ 没找到 D:\BtSoft\php\73\php.exe
)

if exist "D:\BtSoft\php\74\php.exe" (
    echo ✅ 找到 D:\BtSoft\php\74\php.exe
) else (
    echo ❌ 没找到 D:\BtSoft\php\74\php.exe
)

echo.
echo 测试3: 检查项目文件
if exist composer.json (
    echo ✅ composer.json 存在
) else (
    echo ❌ composer.json 不存在
)

if exist artisan (
    echo ✅ artisan 存在
) else (
    echo ❌ artisan 不存在
)

if exist .env.example (
    echo ✅ .env.example 存在
) else (
    echo ❌ .env.example 不存在
)

echo.
echo 测试4: 检查Composer
composer --version >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Composer命令可用
    composer --version
) else (
    echo ❌ Composer命令不可用
)

echo.
echo 测试5: 检查网络
ping -n 1 baidu.com >nul 2>&1
if %errorlevel%==0 (
    echo ✅ 网络连接正常
) else (
    echo ❌ 网络连接异常
)

echo.
echo ==========================================
echo 测试完成！
echo.
echo 请截图这个窗口的内容发给我
echo 这样我就能知道具体是什么问题了
echo.
echo 这个窗口不会自动关闭
echo 按任意键手动关闭...
echo ==========================================

pause >nul

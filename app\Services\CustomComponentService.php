<?php

namespace App\Services;

use App\Models\CustomComponent;
use App\Models\Site;
use Illuminate\Support\Facades\Cache;

class CustomComponentService
{
    /**
     * Get components for site and position
     */
    public function getComponentsForPosition($siteId, $position, $context = [])
    {
        $cacheKey = "components_{$siteId}_{$position}";
        
        $components = Cache::remember($cacheKey, 1800, function () use ($siteId, $position) {
            return CustomComponent::where('site_id', $siteId)
                ->byPosition($position)
                ->active()
                ->orderBy('order_index')
                ->get();
        });

        return $components->filter(function ($component) use ($context) {
            return $component->shouldDisplay($context);
        });
    }

    /**
     * Create new component
     */
    public function createComponent($siteId, $data)
    {
        $component = new CustomComponent(array_merge($data, [
            'site_id' => $siteId,
        ]));

        // Set default order index
        if (!isset($data['order_index'])) {
            $maxOrder = CustomComponent::where('site_id', $siteId)
                ->where('position', $data['position'])
                ->max('order_index');
            $component->order_index = ($maxOrder ?? 0) + 1;
        }

        $component->save();

        // Clear cache
        $this->clearComponentCache($siteId);

        return $component;
    }

    /**
     * Update component
     */
    public function updateComponent($componentId, $data)
    {
        $component = CustomComponent::where('component_id', $componentId)->firstOrFail();
        
        $component->fill($data);
        $component->save();

        // Clear cache
        $this->clearComponentCache($component->site_id);

        return $component;
    }

    /**
     * Delete component
     */
    public function deleteComponent($componentId)
    {
        $component = CustomComponent::where('component_id', $componentId)->firstOrFail();
        $siteId = $component->site_id;
        
        $component->delete();

        // Clear cache
        $this->clearComponentCache($siteId);

        return true;
    }

    /**
     * Clone component
     */
    public function cloneComponent($componentId, $newName = null)
    {
        $component = CustomComponent::where('component_id', $componentId)->firstOrFail();
        $clone = $component->cloneComponent($newName);

        // Clear cache
        $this->clearComponentCache($component->site_id);

        return $clone;
    }

    /**
     * Reorder components
     */
    public function reorderComponents($siteId, $position, $componentIds)
    {
        foreach ($componentIds as $index => $componentId) {
            CustomComponent::where('component_id', $componentId)
                ->where('site_id', $siteId)
                ->where('position', $position)
                ->update(['order_index' => $index + 1]);
        }

        // Clear cache
        $this->clearComponentCache($siteId);

        return true;
    }

    /**
     * Toggle component active status
     */
    public function toggleComponent($componentId)
    {
        $component = CustomComponent::where('component_id', $componentId)->firstOrFail();
        $component->is_active = !$component->is_active;
        $component->save();

        // Clear cache
        $this->clearComponentCache($component->site_id);

        return $component;
    }

    /**
     * Generate components CSS for site
     */
    public function generateSiteComponentsCss($siteId)
    {
        $components = CustomComponent::where('site_id', $siteId)
            ->active()
            ->get();

        $css = '';
        foreach ($components as $component) {
            $css .= $component->generateCss() . "\n";
        }

        return $css;
    }

    /**
     * Render components for position
     */
    public function renderComponentsForPosition($siteId, $position, $context = [])
    {
        $components = $this->getComponentsForPosition($siteId, $position, $context);
        
        $html = '';
        foreach ($components as $component) {
            $html .= $component->render($context);
        }

        return $html;
    }

    /**
     * Get component templates
     */
    public function getComponentTemplates()
    {
        return [
            'banner' => [
                'name' => 'Promotional Banner',
                'description' => 'Display promotional messages',
                'template' => [
                    'type' => 'banner',
                    'settings' => [
                        'text' => 'Special offer! Get 20% off today!',
                        'background_color' => '#3B82F6',
                        'text_color' => '#FFFFFF',
                        'show_close_button' => true,
                    ],
                    'styles' => [
                        'padding' => '12px 16px',
                        'text_align' => 'center',
                        'font_weight' => '500',
                    ],
                ],
            ],
            'contact_form' => [
                'name' => 'Pre-chat Contact Form',
                'description' => 'Collect visitor information before chat',
                'template' => [
                    'type' => 'contact_form',
                    'settings' => [
                        'required_fields' => ['name', 'email'],
                        'optional_fields' => ['phone', 'company'],
                        'submit_button_text' => 'Start Chat',
                        'success_message' => 'Thank you! An agent will be with you shortly.',
                    ],
                ],
            ],
            'rating_widget' => [
                'name' => 'Satisfaction Rating',
                'description' => 'Collect customer feedback',
                'template' => [
                    'type' => 'rating_widget',
                    'settings' => [
                        'rating_type' => 'stars',
                        'max_rating' => 5,
                        'show_comment_field' => true,
                        'required_rating' => false,
                    ],
                ],
            ],
            'quick_replies' => [
                'name' => 'Quick Reply Buttons',
                'description' => 'Predefined response options',
                'template' => [
                    'type' => 'quick_replies',
                    'settings' => [
                        'replies' => [
                            'Hello, I need help with...',
                            'I have a question about pricing',
                            'I want to report an issue',
                            'I need technical support',
                        ],
                        'max_visible' => 3,
                        'show_more_button' => true,
                    ],
                ],
            ],
            'social_links' => [
                'name' => 'Social Media Links',
                'description' => 'Links to social media profiles',
                'template' => [
                    'type' => 'social_links',
                    'settings' => [
                        'links' => [
                            ['platform' => 'facebook', 'url' => 'https://facebook.com/yourpage'],
                            ['platform' => 'twitter', 'url' => 'https://twitter.com/yourhandle'],
                            ['platform' => 'linkedin', 'url' => 'https://linkedin.com/company/yourcompany'],
                        ],
                        'icon_style' => 'colored',
                        'size' => 'medium',
                    ],
                ],
            ],
        ];
    }

    /**
     * Create component from template
     */
    public function createFromTemplate($siteId, $templateKey, $customData = [])
    {
        $templates = $this->getComponentTemplates();
        
        if (!isset($templates[$templateKey])) {
            throw new \InvalidArgumentException('Invalid template key');
        }

        $template = $templates[$templateKey]['template'];
        $componentData = array_merge($template, $customData);
        
        if (!isset($componentData['name'])) {
            $componentData['name'] = $templates[$templateKey]['name'];
        }

        return $this->createComponent($siteId, $componentData);
    }

    /**
     * Validate component settings
     */
    public function validateComponentSettings($type, $settings)
    {
        $availableTypes = CustomComponent::getAvailableTypes();
        
        if (!isset($availableTypes[$type])) {
            throw new \InvalidArgumentException('Invalid component type');
        }

        $typeConfig = $availableTypes[$type];
        $requiredSettings = $typeConfig['settings'] ?? [];

        foreach ($requiredSettings as $setting => $settingType) {
            if (!isset($settings[$setting])) {
                continue; // Optional settings
            }

            $value = $settings[$setting];

            switch ($settingType) {
                case 'string':
                    if (!is_string($value)) {
                        throw new \InvalidArgumentException("Setting '{$setting}' must be a string");
                    }
                    break;
                case 'integer':
                    if (!is_int($value)) {
                        throw new \InvalidArgumentException("Setting '{$setting}' must be an integer");
                    }
                    break;
                case 'boolean':
                    if (!is_bool($value)) {
                        throw new \InvalidArgumentException("Setting '{$setting}' must be a boolean");
                    }
                    break;
                case 'array':
                    if (!is_array($value)) {
                        throw new \InvalidArgumentException("Setting '{$setting}' must be an array");
                    }
                    break;
                case 'color':
                    if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $value)) {
                        throw new \InvalidArgumentException("Setting '{$setting}' must be a valid hex color");
                    }
                    break;
            }
        }

        return true;
    }

    /**
     * Clear component cache
     */
    public function clearComponentCache($siteId)
    {
        $positions = array_keys(CustomComponent::getAvailablePositions());
        
        foreach ($positions as $position) {
            Cache::forget("components_{$siteId}_{$position}");
        }
    }

    /**
     * Export components
     */
    public function exportComponents($siteId)
    {
        $components = CustomComponent::where('site_id', $siteId)->get();
        
        $exportData = $components->map(function ($component) {
            return $component->only([
                'name', 'type', 'description', 'position', 'order_index',
                'settings', 'styles', 'content', 'conditions', 'metadata'
            ]);
        });

        return [
            'components' => $exportData,
            'export_version' => '1.0',
            'exported_at' => now()->toISOString(),
        ];
    }

    /**
     * Import components
     */
    public function importComponents($siteId, $importData)
    {
        if (!isset($importData['components'])) {
            throw new \InvalidArgumentException('Invalid import data');
        }

        $imported = [];
        
        foreach ($importData['components'] as $componentData) {
            $component = $this->createComponent($siteId, $componentData);
            $imported[] = $component;
        }

        return $imported;
    }
}

<div class="custom-component custom-component-{{ $component->component_id }} default-component" 
     data-component-id="{{ $component->component_id }}"
     data-component-type="{{ $component->type }}">
    
    <div class="component-header">
        <h4>{{ $component->name }}</h4>
        @if($component->description)
        <p class="component-description">{{ $component->description }}</p>
        @endif
    </div>
    
    <div class="component-content">
        @if($content)
        {!! $content !!}
        @else
        <p>Component content will be displayed here.</p>
        @endif
    </div>
    
    @if($settings && count($settings) > 0)
    <div class="component-settings">
        <details>
            <summary>Component Settings</summary>
            <ul>
                @foreach($settings as $key => $value)
                <li><strong>{{ ucfirst(str_replace('_', ' ', $key)) }}:</strong> 
                    @if(is_array($value))
                        {{ json_encode($value) }}
                    @else
                        {{ $value }}
                    @endif
                </li>
                @endforeach
            </ul>
        </details>
    </div>
    @endif
</div>

<style>
.custom-component-{{ $component->component_id }} {
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    padding: 16px;
    margin: 8px 0;
    background: #F9FAFB;
    @if($styles)
        @foreach($styles as $property => $value)
        {{ str_replace('_', '-', $property) }}: {{ $value }};
        @endforeach
    @endif
}

.custom-component-{{ $component->component_id }} .component-header h4 {
    margin: 0 0 8px 0;
    color: #374151;
    font-size: 16px;
    font-weight: 600;
}

.custom-component-{{ $component->component_id }} .component-description {
    margin: 0 0 12px 0;
    color: #6B7280;
    font-size: 14px;
}

.custom-component-{{ $component->component_id }} .component-content {
    margin: 12px 0;
}

.custom-component-{{ $component->component_id }} .component-settings {
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #E5E7EB;
}

.custom-component-{{ $component->component_id }} .component-settings details {
    font-size: 12px;
    color: #6B7280;
}

.custom-component-{{ $component->component_id }} .component-settings summary {
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 8px;
}

.custom-component-{{ $component->component_id }} .component-settings ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.custom-component-{{ $component->component_id }} .component-settings li {
    padding: 4px 0;
    border-bottom: 1px solid #F3F4F6;
}

.custom-component-{{ $component->component_id }} .component-settings li:last-child {
    border-bottom: none;
}
</style>

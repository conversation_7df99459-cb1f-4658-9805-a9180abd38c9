<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class Site extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'domain',
        'site_key',
        'description',
        'logo',
        'allowed_domains',
        'is_active',
        'theme_settings',
        'widget_settings',
        'branding_settings',
        'working_hours',
        'auto_messages',
        'file_upload_settings',
        'visitor_tracking_enabled',
        'gdpr_enabled',
        'default_language',
        'supported_languages',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'allowed_domains' => 'array',
        'is_active' => 'boolean',
        'theme_settings' => 'array',
        'widget_settings' => 'array',
        'branding_settings' => 'array',
        'working_hours' => 'array',
        'auto_messages' => 'array',
        'file_upload_settings' => 'array',
        'visitor_tracking_enabled' => 'boolean',
        'gdpr_enabled' => 'boolean',
        'supported_languages' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($site) {
            if (!$site->site_key) {
                $site->site_key = Str::random(32);
            }
        });
    }

    /**
     * Get the users that have access to this site
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'site_users')
                    ->withPivot('role', 'is_active', 'permissions')
                    ->withTimestamps();
    }

    /**
     * Get the visitors for this site
     */
    public function visitors()
    {
        return $this->hasMany(Visitor::class);
    }

    /**
     * Get the chat sessions for this site
     */
    public function chatSessions()
    {
        return $this->hasMany(ChatSession::class);
    }

    /**
     * Get the contact messages for this site
     */
    public function contactMessages()
    {
        return $this->hasMany(ContactMessage::class);
    }

    /**
     * Get the visitor tracking data for this site
     */
    public function visitorTracking()
    {
        return $this->hasMany(VisitorTracking::class);
    }

    /**
     * Get the chat templates for this site
     */
    public function chatTemplates()
    {
        return $this->hasMany(ChatTemplate::class);
    }

    /**
     * Get the working hours for this site
     */
    public function workingHours()
    {
        return $this->hasMany(WorkingHour::class);
    }

    /**
     * Get the system settings for this site
     */
    public function systemSettings()
    {
        return $this->hasMany(SystemSetting::class);
    }

    /**
     * Get the webhooks for this site
     */
    public function webhooks()
    {
        return $this->hasMany(Webhook::class);
    }

    /**
     * Check if site is currently online based on working hours
     */
    public function isOnline()
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now();
        $dayOfWeek = strtolower($now->format('l'));
        
        $workingHour = $this->workingHours()
            ->where('day_of_week', $dayOfWeek)
            ->where('is_working_day', true)
            ->first();

        if (!$workingHour) {
            return false;
        }

        $currentTime = $now->format('H:i:s');
        
        return $currentTime >= $workingHour->start_time && 
               $currentTime <= $workingHour->end_time;
    }

    /**
     * Get online agents count
     */
    public function getOnlineAgentsCount()
    {
        return $this->users()
            ->where('is_active', true)
            ->where('status', 'online')
            ->where('last_activity_at', '>', now()->subMinutes(5))
            ->count();
    }

    /**
     * Get available agents (online and can handle more chats)
     */
    public function getAvailableAgents()
    {
        return $this->users()
            ->where('is_active', true)
            ->where('status', 'online')
            ->where('last_activity_at', '>', now()->subMinutes(5))
            ->get()
            ->filter(function ($user) {
                return $user->canHandleMoreChats($this->id);
            });
    }

    /**
     * Get active chat sessions count
     */
    public function getActiveChatSessionsCount()
    {
        return $this->chatSessions()
            ->whereIn('status', ['waiting', 'active'])
            ->count();
    }

    /**
     * Get waiting chat sessions count
     */
    public function getWaitingChatSessionsCount()
    {
        return $this->chatSessions()
            ->where('status', 'waiting')
            ->count();
    }

    /**
     * Get theme setting
     */
    public function getThemeSetting($key, $default = null)
    {
        return data_get($this->theme_settings, $key, $default);
    }

    /**
     * Get widget setting
     */
    public function getWidgetSetting($key, $default = null)
    {
        return data_get($this->widget_settings, $key, $default);
    }

    /**
     * Get auto message
     */
    public function getAutoMessage($type, $language = null)
    {
        $language = $language ?: $this->default_language;
        return data_get($this->auto_messages, "{$type}.{$language}", 
                       data_get($this->auto_messages, "{$type}.{$this->default_language}"));
    }

    /**
     * Check if domain is allowed
     */
    public function isDomainAllowed($domain)
    {
        if (!$this->allowed_domains) {
            return true; // Allow all if not specified
        }

        foreach ($this->allowed_domains as $allowedDomain) {
            if ($allowedDomain === '*' || $domain === $allowedDomain) {
                return true;
            }
            
            // Check wildcard domains
            if (strpos($allowedDomain, '*.') === 0) {
                $pattern = str_replace('*.', '', $allowedDomain);
                if (str_ends_with($domain, $pattern)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * Generate widget code
     */
    public function generateWidgetCode($options = [])
    {
        $baseUrl = config('app.url');
        $siteKey = $this->site_key;
        
        $config = array_merge([
            'position' => $this->getWidgetSetting('position', 'bottom-right'),
            'theme' => $this->getThemeSetting('primary_color', '#007bff'),
            'language' => $this->default_language,
            'showWhenOffline' => true,
        ], $options);

        $configJson = json_encode($config);

        return <<<HTML
<!-- Customer Service Chat Widget -->
<script>
(function() {
    var chatConfig = {$configJson};
    chatConfig.siteKey = '{$siteKey}';
    chatConfig.baseUrl = '{$baseUrl}';
    
    var script = document.createElement('script');
    script.src = '{$baseUrl}/js/widget.js';
    script.async = true;
    script.onload = function() {
        if (typeof ChatWidget !== 'undefined') {
            new ChatWidget(chatConfig);
        }
    };
    document.head.appendChild(script);
})();
</script>
<!-- End Customer Service Chat Widget -->
HTML;
    }

    /**
     * Scope for active sites
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get logo URL
     */
    public function getLogoUrlAttribute()
    {
        if ($this->logo) {
            return asset('storage/' . $this->logo);
        }
        
        return null;
    }

    /**
     * Get display name
     */
    public function getDisplayNameAttribute()
    {
        return $this->name . ' (' . $this->domain . ')';
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\User;
use App\Models\VisitorSession;
use App\Services\ChatService;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class AgentWorkspaceController extends AdminController
{
    protected $chatService;
    protected $notificationService;

    public function __construct(ChatService $chatService, NotificationService $notificationService)
    {
        parent::__construct();
        $this->chatService = $chatService;
        $this->notificationService = $notificationService;
    }

    /**
     * Get agent workspace overview
     */
    public function getWorkspaceOverview(Request $request): JsonResponse
    {
        try {
            $agent = $this->getCurrentAdmin();
            $siteId = $this->getCurrentSiteId($request);

            $overview = [
                'agent_info' => [
                    'id' => $agent->id,
                    'name' => $agent->name,
                    'email' => $agent->email,
                    'status' => $agent->status,
                    'role' => $agent->role,
                    'avatar' => $agent->avatar,
                    'last_activity' => $agent->last_activity_at,
                ],
                
                'active_sessions' => ChatSession::where('site_id', $siteId)
                    ->where('agent_id', $agent->id)
                    ->where('status', 'active')
                    ->with(['visitor', 'messages' => function($query) {
                        $query->latest()->limit(1);
                    }])
                    ->get(),
                
                'pending_sessions' => ChatSession::where('site_id', $siteId)
                    ->where('status', 'waiting')
                    ->whereNull('agent_id')
                    ->with(['visitor'])
                    ->orderBy('created_at')
                    ->limit(10)
                    ->get(),
                
                'today_stats' => [
                    'sessions_handled' => ChatSession::where('site_id', $siteId)
                        ->where('agent_id', $agent->id)
                        ->whereDate('created_at', today())
                        ->count(),
                    
                    'messages_sent' => ChatMessage::whereHas('session', function($query) use ($siteId, $agent) {
                            $query->where('site_id', $siteId)->where('agent_id', $agent->id);
                        })
                        ->where('sender_type', 'agent')
                        ->where('sender_id', $agent->id)
                        ->whereDate('created_at', today())
                        ->count(),
                    
                    'avg_response_time' => ChatMessage::whereHas('session', function($query) use ($siteId, $agent) {
                            $query->where('site_id', $siteId)->where('agent_id', $agent->id);
                        })
                        ->where('sender_type', 'agent')
                        ->where('sender_id', $agent->id)
                        ->whereDate('created_at', today())
                        ->avg('response_time') ?? 0,
                    
                    'satisfaction_rating' => ChatSession::where('site_id', $siteId)
                        ->where('agent_id', $agent->id)
                        ->whereNotNull('satisfaction_rating')
                        ->whereDate('updated_at', today())
                        ->avg('satisfaction_rating') ?? 0,
                ],
                
                'notifications' => $this->getAgentNotifications($agent->id, $siteId),
                
                'quick_stats' => [
                    'online_agents' => User::where('site_id', $siteId)
                        ->where('role', 'agent')
                        ->where('status', 'online')
                        ->count(),
                    
                    'waiting_visitors' => ChatSession::where('site_id', $siteId)
                        ->where('status', 'waiting')
                        ->count(),
                    
                    'active_chats' => ChatSession::where('site_id', $siteId)
                        ->where('status', 'active')
                        ->count(),
                ],
            ];

            return $this->successResponse($overview);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get workspace overview: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get agent notifications
     */
    private function getAgentNotifications($agentId, $siteId)
    {
        return [
            [
                'id' => 1,
                'type' => 'new_chat',
                'title' => 'New chat request',
                'message' => 'A visitor is waiting for assistance',
                'created_at' => now()->subMinutes(2),
                'is_read' => false,
            ],
            [
                'id' => 2,
                'type' => 'chat_transferred',
                'title' => 'Chat transferred',
                'message' => 'A chat has been transferred to you',
                'created_at' => now()->subMinutes(5),
                'is_read' => false,
            ],
        ];
    }

    /**
     * Update agent status
     */
    public function updateAgentStatus(Request $request): JsonResponse
    {
        $request->validate([
            'status' => 'required|string|in:online,away,busy,offline',
            'status_message' => 'nullable|string|max:255',
        ]);

        try {
            $agent = $this->getCurrentAdmin();
            
            $agent->update([
                'status' => $request->status,
                'status_message' => $request->status_message,
                'last_activity_at' => now(),
            ]);

            // Broadcast status change to other agents
            $this->notificationService->broadcastAgentStatusChange($agent);

            return $this->successResponse([
                'status' => $agent->status,
                'status_message' => $agent->status_message,
            ], 'Status updated successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to update status: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Accept a chat session
     */
    public function acceptChat(Request $request, $sessionId): JsonResponse
    {
        try {
            $agent = $this->getCurrentAdmin();
            $siteId = $this->getCurrentSiteId($request);

            $session = ChatSession::where('session_id', $sessionId)
                ->where('site_id', $siteId)
                ->where('status', 'waiting')
                ->whereNull('agent_id')
                ->first();

            if (!$session) {
                return $this->errorResponse('Chat session not found or already assigned', 404);
            }

            // Check agent availability
            if ($agent->status !== 'online') {
                return $this->errorResponse('Agent must be online to accept chats', 400);
            }

            // Check concurrent chat limit
            $activeSessions = ChatSession::where('agent_id', $agent->id)
                ->where('status', 'active')
                ->count();

            $maxConcurrentChats = $agent->max_concurrent_chats ?? 5;
            if ($activeSessions >= $maxConcurrentChats) {
                return $this->errorResponse('Maximum concurrent chats reached', 400);
            }

            // Accept the chat
            $session->update([
                'agent_id' => $agent->id,
                'status' => 'active',
                'accepted_at' => now(),
            ]);

            // Send welcome message
            $welcomeMessage = $this->chatService->sendSystemMessage(
                $session->session_id,
                "Hello! I'm {$agent->name}. How can I help you today?",
                'agent_joined'
            );

            // Notify visitor that agent joined
            $this->notificationService->notifyVisitorAgentJoined($session, $agent);

            return $this->successResponse([
                'session' => $session->load(['visitor', 'messages']),
                'welcome_message' => $welcomeMessage,
            ], 'Chat accepted successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to accept chat: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Transfer a chat session
     */
    public function transferChat(Request $request, $sessionId): JsonResponse
    {
        $request->validate([
            'target_agent_id' => 'required|exists:st_users,id',
            'transfer_reason' => 'nullable|string|max:500',
        ]);

        try {
            $agent = $this->getCurrentAdmin();
            $siteId = $this->getCurrentSiteId($request);

            $session = ChatSession::where('session_id', $sessionId)
                ->where('site_id', $siteId)
                ->where('agent_id', $agent->id)
                ->where('status', 'active')
                ->first();

            if (!$session) {
                return $this->errorResponse('Chat session not found or not assigned to you', 404);
            }

            $targetAgent = User::find($request->target_agent_id);
            if (!$targetAgent || $targetAgent->site_id !== $siteId) {
                return $this->errorResponse('Target agent not found', 404);
            }

            // Check target agent availability
            if ($targetAgent->status !== 'online') {
                return $this->errorResponse('Target agent is not online', 400);
            }

            // Transfer the chat
            $session->update([
                'agent_id' => $targetAgent->id,
                'transferred_from' => $agent->id,
                'transfer_reason' => $request->transfer_reason,
                'transferred_at' => now(),
            ]);

            // Send transfer message
            $transferMessage = $this->chatService->sendSystemMessage(
                $session->session_id,
                "Chat has been transferred to {$targetAgent->name}. " . ($request->transfer_reason ?? ''),
                'chat_transferred'
            );

            // Notify both agents and visitor
            $this->notificationService->notifyChatTransferred($session, $agent, $targetAgent);

            return $this->successResponse([
                'session' => $session->load(['visitor', 'agent']),
                'transfer_message' => $transferMessage,
            ], 'Chat transferred successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to transfer chat: ' . $e->getMessage(), 500);
        }
    }

    /**
     * End a chat session
     */
    public function endChat(Request $request, $sessionId): JsonResponse
    {
        $request->validate([
            'end_reason' => 'nullable|string|max:500',
            'internal_notes' => 'nullable|string|max:1000',
        ]);

        try {
            $agent = $this->getCurrentAdmin();
            $siteId = $this->getCurrentSiteId($request);

            $session = ChatSession::where('session_id', $sessionId)
                ->where('site_id', $siteId)
                ->where('agent_id', $agent->id)
                ->where('status', 'active')
                ->first();

            if (!$session) {
                return $this->errorResponse('Chat session not found or not assigned to you', 404);
            }

            // End the chat
            $session->update([
                'status' => 'ended',
                'ended_at' => now(),
                'ended_by' => $agent->id,
                'end_reason' => $request->end_reason,
                'internal_notes' => $request->internal_notes,
            ]);

            // Send end message
            $endMessage = $this->chatService->sendSystemMessage(
                $session->session_id,
                'Chat session has been ended. Thank you for contacting us!',
                'chat_ended'
            );

            // Notify visitor
            $this->notificationService->notifyVisitorChatEnded($session);

            return $this->successResponse([
                'session' => $session,
                'end_message' => $endMessage,
            ], 'Chat ended successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to end chat: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get chat session details
     */
    public function getChatSession(Request $request, $sessionId): JsonResponse
    {
        try {
            $siteId = $this->getCurrentSiteId($request);
            $agent = $this->getCurrentAdmin();

            $session = ChatSession::where('session_id', $sessionId)
                ->where('site_id', $siteId)
                ->with([
                    'visitor',
                    'agent',
                    'messages' => function($query) {
                        $query->orderBy('created_at');
                    },
                    'visitorSession' => function($query) {
                        $query->with('pageViews');
                    }
                ])
                ->first();

            if (!$session) {
                return $this->errorResponse('Chat session not found', 404);
            }

            // Check if agent has access to this session
            if ($agent->role === 'agent' && $session->agent_id !== $agent->id) {
                return $this->errorResponse('Access denied', 403);
            }

            return $this->successResponse($session);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get chat session: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get visitor information
     */
    public function getVisitorInfo(Request $request, $visitorId): JsonResponse
    {
        try {
            $siteId = $this->getCurrentSiteId($request);

            $visitor = VisitorSession::where('visitor_id', $visitorId)
                ->where('site_id', $siteId)
                ->with([
                    'pageViews' => function($query) {
                        $query->orderBy('created_at', 'desc')->limit(10);
                    },
                    'chatSessions' => function($query) {
                        $query->orderBy('created_at', 'desc')->limit(5);
                    }
                ])
                ->first();

            if (!$visitor) {
                return $this->errorResponse('Visitor not found', 404);
            }

            $visitorInfo = [
                'visitor' => $visitor,
                'stats' => [
                    'total_sessions' => ChatSession::where('visitor_id', $visitorId)
                        ->where('site_id', $siteId)
                        ->count(),
                    
                    'total_messages' => ChatMessage::whereHas('session', function($query) use ($visitorId, $siteId) {
                            $query->where('visitor_id', $visitorId)->where('site_id', $siteId);
                        })
                        ->where('sender_type', 'visitor')
                        ->count(),
                    
                    'avg_session_duration' => ChatSession::where('visitor_id', $visitorId)
                        ->where('site_id', $siteId)
                        ->whereNotNull('ended_at')
                        ->avg(\DB::raw('TIMESTAMPDIFF(SECOND, created_at, ended_at)')) ?? 0,
                    
                    'last_visit' => VisitorSession::where('visitor_id', $visitorId)
                        ->where('site_id', $siteId)
                        ->max('created_at'),
                ],
            ];

            return $this->successResponse($visitorInfo);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get visitor info: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Add internal note to chat session
     */
    public function addInternalNote(Request $request, $sessionId): JsonResponse
    {
        $request->validate([
            'note' => 'required|string|max:1000',
        ]);

        try {
            $agent = $this->getCurrentAdmin();
            $siteId = $this->getCurrentSiteId($request);

            $session = ChatSession::where('session_id', $sessionId)
                ->where('site_id', $siteId)
                ->first();

            if (!$session) {
                return $this->errorResponse('Chat session not found', 404);
            }

            // Add note to session metadata
            $notes = $session->metadata['internal_notes'] ?? [];
            $notes[] = [
                'note' => $request->note,
                'agent_id' => $agent->id,
                'agent_name' => $agent->name,
                'created_at' => now()->toISOString(),
            ];

            $session->update([
                'metadata' => array_merge($session->metadata ?? [], [
                    'internal_notes' => $notes
                ])
            ]);

            return $this->successResponse([
                'note' => end($notes),
                'total_notes' => count($notes),
            ], 'Internal note added successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to add internal note: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get agent performance metrics
     */
    public function getAgentPerformance(Request $request): JsonResponse
    {
        try {
            $agent = $this->getCurrentAdmin();
            $siteId = $this->getCurrentSiteId($request);
            
            $startDate = $request->get('start_date', now()->subDays(30)->toDateString());
            $endDate = $request->get('end_date', now()->toDateString());

            $performance = [
                'sessions_handled' => ChatSession::where('site_id', $siteId)
                    ->where('agent_id', $agent->id)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count(),
                
                'messages_sent' => ChatMessage::whereHas('session', function($query) use ($siteId, $agent) {
                        $query->where('site_id', $siteId)->where('agent_id', $agent->id);
                    })
                    ->where('sender_type', 'agent')
                    ->where('sender_id', $agent->id)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count(),
                
                'avg_response_time' => ChatMessage::whereHas('session', function($query) use ($siteId, $agent) {
                        $query->where('site_id', $siteId)->where('agent_id', $agent->id);
                    })
                    ->where('sender_type', 'agent')
                    ->where('sender_id', $agent->id)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->avg('response_time') ?? 0,
                
                'satisfaction_rating' => ChatSession::where('site_id', $siteId)
                    ->where('agent_id', $agent->id)
                    ->whereNotNull('satisfaction_rating')
                    ->whereBetween('updated_at', [$startDate, $endDate])
                    ->avg('satisfaction_rating') ?? 0,
                
                'total_online_time' => $this->calculateOnlineTime($agent->id, $startDate, $endDate),
                
                'daily_performance' => $this->getDailyPerformance($agent->id, $siteId, $startDate, $endDate),
            ];

            return $this->successResponse($performance);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get agent performance: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Calculate agent online time
     */
    private function calculateOnlineTime($agentId, $startDate, $endDate)
    {
        // This would require tracking agent status changes
        // For now, return a placeholder value
        return 8 * 60 * 60; // 8 hours in seconds
    }

    /**
     * Get daily performance data
     */
    private function getDailyPerformance($agentId, $siteId, $startDate, $endDate)
    {
        return ChatSession::where('site_id', $siteId)
            ->where('agent_id', $agentId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('DATE(created_at) as date, COUNT(*) as sessions, AVG(satisfaction_rating) as avg_rating')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->map(function($item) {
                return [
                    'date' => $item->date,
                    'sessions' => $item->sessions,
                    'avg_rating' => round($item->avg_rating ?? 0, 2),
                ];
            });
    }
}

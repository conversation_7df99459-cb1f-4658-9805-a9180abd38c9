<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVisitorTrackingTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('visitor_tracking', function (Blueprint $table) {
            $table->id();
            $table->foreignId('site_id')->constrained()->onDelete('cascade');
            $table->foreignId('visitor_id')->constrained()->onDelete('cascade');
            $table->string('session_id'); // Browser session ID
            
            // Page View Data
            $table->text('page_url');
            $table->string('page_title')->nullable();
            $table->text('referrer_url')->nullable();
            $table->timestamp('page_load_time');
            $table->timestamp('page_leave_time')->nullable();
            $table->integer('time_on_page')->nullable(); // seconds
            $table->integer('scroll_depth')->nullable(); // percentage
            
            // Interaction Data
            $table->enum('event_type', ['page_view', 'click', 'scroll', 'mouse_move', 'form_interaction', 'file_download', 'external_link'])->default('page_view');
            $table->string('element_selector')->nullable(); // CSS selector for clicked elements
            $table->string('element_text')->nullable();
            $table->json('element_attributes')->nullable();
            
            // Mouse Tracking Data
            $table->integer('mouse_x')->nullable();
            $table->integer('mouse_y')->nullable();
            $table->json('mouse_path')->nullable(); // Array of coordinates for heatmap
            $table->json('click_coordinates')->nullable(); // Click positions
            
            // Form Interaction Data
            $table->string('form_id')->nullable();
            $table->json('form_fields')->nullable();
            $table->enum('form_action', ['focus', 'blur', 'change', 'submit'])->nullable();
            
            // Performance Data
            $table->integer('page_load_duration')->nullable(); // milliseconds
            $table->integer('dom_ready_duration')->nullable(); // milliseconds
            $table->json('performance_metrics')->nullable();
            
            // Device & Browser Data
            $table->integer('viewport_width')->nullable();
            $table->integer('viewport_height')->nullable();
            $table->integer('screen_width')->nullable();
            $table->integer('screen_height')->nullable();
            $table->string('color_depth')->nullable();
            $table->boolean('javascript_enabled')->default(true);
            $table->boolean('cookies_enabled')->default(true);
            
            $table->timestamps();
            
            $table->index(['site_id', 'visitor_id', 'created_at']);
            $table->index(['site_id', 'event_type', 'created_at']);
            $table->index(['visitor_id', 'session_id']);
            $table->index(['page_url', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('visitor_tracking');
    }
}

# Customer Service System API Documentation

## Overview

This document describes the REST API for the International Independent Website Online Customer Service System. The API provides endpoints for authentication, chat management, visitor tracking, analytics, and system configuration.

## Base URL

```
https://your-domain.com/api/v1
```

## Authentication

The API uses Laravel Sanctum for authentication. Include the bearer token in the Authorization header:

```
Authorization: Bearer {your-token}
```

### Authentication Endpoints

#### POST /auth/login
Authenticate a user and receive an access token.

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "user": {
            "id": 1,
            "name": "<PERSON>",
            "email": "<EMAIL>",
            "role": "agent"
        },
        "token": "1|abc123...",
        "expires_at": "2024-01-01T12:00:00Z"
    }
}
```

#### GET /auth/profile
Get the authenticated user's profile.

**Response:**
```json
{
    "success": true,
    "data": {
        "id": 1,
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "role": "agent",
        "status": "online",
        "avatar": "/storage/avatars/user1.jpg"
    }
}
```

#### PUT /auth/profile
Update the authenticated user's profile.

**Request Body:**
```json
{
    "name": "John Smith",
    "phone": "+1234567890",
    "timezone": "America/New_York"
}
```

#### PUT /auth/password
Update the authenticated user's password.

**Request Body:**
```json
{
    "current_password": "oldpassword",
    "new_password": "newpassword123",
    "new_password_confirmation": "newpassword123"
}
```

#### PUT /auth/status
Update the authenticated user's status.

**Request Body:**
```json
{
    "status": "away"
}
```

**Valid statuses:** `online`, `away`, `busy`, `offline`

#### POST /auth/logout
Logout and revoke the current token.

#### POST /auth/refresh
Refresh the current token.

## Chat Management

### Chat Session Endpoints

#### POST /chat/start-session
Start a new chat session.

**Request Body:**
```json
{
    "visitor_id": "uuid-string",
    "site_id": 1,
    "visitor_name": "Jane Visitor",
    "visitor_email": "<EMAIL>",
    "initial_message": "Hello, I need help"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "session": {
            "id": 1,
            "session_id": "uuid-string",
            "status": "waiting",
            "queue_position": 3,
            "estimated_wait_time": 300
        }
    }
}
```

#### GET /chat/sessions/{sessionId}/messages
Get messages for a chat session.

**Query Parameters:**
- `page` (optional): Page number for pagination
- `per_page` (optional): Number of messages per page (default: 20)

**Response:**
```json
{
    "success": true,
    "data": {
        "messages": [
            {
                "id": 1,
                "message": "Hello, how can I help?",
                "sender_type": "agent",
                "sender_name": "John Doe",
                "sent_at": "2024-01-01T12:00:00Z",
                "is_read": true
            }
        ],
        "pagination": {
            "current_page": 1,
            "last_page": 5,
            "per_page": 20,
            "total": 100
        }
    }
}
```

#### POST /chat/sessions/{sessionId}/messages
Send a message in a chat session.

**Request Body:**
```json
{
    "message": "Hello, how can I help you?",
    "sender_type": "agent",
    "message_type": "text"
}
```

**For file uploads:**
```json
{
    "message": "Here's the document you requested",
    "sender_type": "agent",
    "message_type": "file",
    "file": "base64-encoded-file-or-multipart-upload"
}
```

#### POST /chat/sessions/{sessionId}/typing
Set typing indicator for a session.

**Request Body:**
```json
{
    "is_typing": true,
    "sender_type": "agent"
}
```

#### POST /chat/sessions/{sessionId}/end
End a chat session.

**Request Body:**
```json
{
    "reason": "Issue resolved"
}
```

#### POST /chat/sessions/{sessionId}/rate
Rate a completed chat session.

**Request Body:**
```json
{
    "rating": 5,
    "feedback": "Great service!"
}
```

#### GET /chat/sessions/{sessionId}/queue-position
Get queue position for a waiting session.

**Response:**
```json
{
    "success": true,
    "data": {
        "position": 3,
        "estimated_wait_time": 300
    }
}
```

### Agent Management

#### GET /chat/agents
Get list of available agents.

**Response:**
```json
{
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "John Doe",
            "status": "online",
            "current_sessions": 2,
            "max_sessions": 5
        }
    ]
}
```

#### POST /chat/sessions/{sessionId}/assign
Assign an agent to a session.

**Request Body:**
```json
{
    "agent_id": 1
}
```

#### POST /chat/sessions/{sessionId}/transfer
Transfer a session to another agent.

**Request Body:**
```json
{
    "agent_id": 2,
    "reason": "Specialist required"
}
```

## Visitor Management

#### GET /visitors
Get list of visitors.

**Query Parameters:**
- `page` (optional): Page number
- `per_page` (optional): Items per page
- `search` (optional): Search term
- `country` (optional): Filter by country
- `is_returning` (optional): Filter by returning status

#### GET /visitors/{visitorId}
Get visitor details.

#### GET /visitors/{visitorId}/sessions
Get chat sessions for a visitor.

#### GET /visitors/{visitorId}/behaviors
Get behavior tracking data for a visitor.

## Analytics

#### GET /analytics/overview
Get analytics overview.

**Query Parameters:**
- `start_date` (optional): Start date (YYYY-MM-DD)
- `end_date` (optional): End date (YYYY-MM-DD)
- `site_id` (optional): Filter by site

**Response:**
```json
{
    "success": true,
    "data": {
        "total_sessions": 1250,
        "active_sessions": 15,
        "average_response_time": 45,
        "satisfaction_rating": 4.2,
        "conversion_rate": 12.5
    }
}
```

#### GET /analytics/sessions
Get session analytics.

#### GET /analytics/agents
Get agent performance analytics.

#### GET /analytics/visitors
Get visitor analytics.

## Configuration

#### GET /config/sites/{siteId}
Get site configuration.

#### PUT /config/sites/{siteId}
Update site configuration.

#### GET /config/themes
Get available themes.

#### PUT /config/themes/{themeId}
Update theme settings.

## File Upload

#### POST /files/upload
Upload a file.

**Request:**
- Multipart form data with file field
- Maximum file size: 10MB
- Allowed types: jpg, png, pdf, doc, docx

**Response:**
```json
{
    "success": true,
    "data": {
        "file_url": "/storage/uploads/file.pdf",
        "file_name": "document.pdf",
        "file_size": 1024000
    }
}
```

## Error Responses

All endpoints return errors in the following format:

```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        "field_name": ["Validation error message"]
    }
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Too Many Requests
- `500` - Internal Server Error

## Rate Limiting

API requests are rate limited:
- Authenticated users: 60 requests per minute
- Unauthenticated users: 20 requests per minute
- File uploads: 10 requests per hour

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset timestamp

## Webhooks

The system supports webhooks for real-time notifications:

### Webhook Events

- `session.started` - New chat session started
- `session.assigned` - Session assigned to agent
- `session.ended` - Session ended
- `message.sent` - New message sent
- `visitor.online` - Visitor came online
- `visitor.offline` - Visitor went offline

### Webhook Payload

```json
{
    "event": "session.started",
    "data": {
        "session_id": "uuid-string",
        "visitor_id": "uuid-string",
        "site_id": 1,
        "timestamp": "2024-01-01T12:00:00Z"
    },
    "signature": "sha256-hash"
}
```

## SDKs and Libraries

- JavaScript SDK: Available for easy integration
- PHP SDK: Available for server-side integration
- Widget Script: Embeddable chat widget

## Support

For API support, contact: <EMAIL>

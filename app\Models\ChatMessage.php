<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class ChatMessage extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'message_id',
        'chat_session_id',
        'user_id',
        'parent_message_id',
        'content',
        'sender_type',
        'type',
        'file_path',
        'file_name',
        'file_size',
        'file_type',
        'is_read',
        'read_at',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_read' => 'boolean',
        'read_at' => 'datetime',
        'file_size' => 'integer',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($message) {
            if (!$message->message_id) {
                $message->message_id = Str::uuid();
            }
        });
    }

    /**
     * Get the chat session that owns the message
     */
    public function chatSession()
    {
        return $this->belongsTo(ChatSession::class);
    }

    /**
     * Get the user that sent the message
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the parent message (for threaded conversations)
     */
    public function parentMessage()
    {
        return $this->belongsTo(ChatMessage::class, 'parent_message_id');
    }

    /**
     * Get the child messages (replies)
     */
    public function replies()
    {
        return $this->hasMany(ChatMessage::class, 'parent_message_id');
    }

    /**
     * Check if message is from visitor
     */
    public function isFromVisitor()
    {
        return $this->sender_type === 'visitor';
    }

    /**
     * Check if message is from agent
     */
    public function isFromAgent()
    {
        return $this->sender_type === 'agent';
    }

    /**
     * Check if message is system message
     */
    public function isSystemMessage()
    {
        return $this->sender_type === 'system';
    }

    /**
     * Check if message has file attachment
     */
    public function hasFile()
    {
        return !empty($this->file_path);
    }

    /**
     * Get file URL
     */
    public function getFileUrlAttribute()
    {
        if (!$this->file_path) {
            return null;
        }

        return asset('storage/' . $this->file_path);
    }

    /**
     * Get formatted file size
     */
    public function getFormattedFileSizeAttribute()
    {
        if (!$this->file_size) {
            return null;
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if file is image
     */
    public function isImage()
    {
        if (!$this->file_type) {
            return false;
        }

        return strpos($this->file_type, 'image/') === 0;
    }

    /**
     * Check if file is document
     */
    public function isDocument()
    {
        if (!$this->file_type) {
            return false;
        }

        $documentTypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'text/plain',
            'text/csv',
        ];

        return in_array($this->file_type, $documentTypes);
    }

    /**
     * Mark message as read
     */
    public function markAsRead()
    {
        if (!$this->is_read) {
            $this->update([
                'is_read' => true,
                'read_at' => now(),
            ]);
        }

        return $this;
    }

    /**
     * Get sender name
     */
    public function getSenderNameAttribute()
    {
        switch ($this->sender_type) {
            case 'agent':
                return $this->user ? $this->user->name : 'Agent';
            case 'visitor':
                return $this->chatSession && $this->chatSession->visitor 
                    ? ($this->chatSession->visitor->name ?: 'Visitor')
                    : 'Visitor';
            case 'system':
                return 'System';
            default:
                return 'Unknown';
        }
    }

    /**
     * Get sender avatar
     */
    public function getSenderAvatarAttribute()
    {
        switch ($this->sender_type) {
            case 'agent':
                return $this->user ? $this->user->avatar_url : null;
            case 'visitor':
                return $this->chatSession && $this->chatSession->visitor 
                    ? $this->chatSession->visitor->avatar_url
                    : null;
            default:
                return null;
        }
    }

    /**
     * Get message content with file info
     */
    public function getDisplayContentAttribute()
    {
        if ($this->hasFile()) {
            $fileInfo = $this->file_name;
            if ($this->formatted_file_size) {
                $fileInfo .= ' (' . $this->formatted_file_size . ')';
            }
            
            if ($this->content) {
                return $this->content . "\n\n📎 " . $fileInfo;
            }
            
            return "📎 " . $fileInfo;
        }

        return $this->content;
    }

    /**
     * Get time ago format
     */
    public function getTimeAgoAttribute()
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Scope for messages by sender type
     */
    public function scopeBySenderType($query, $senderType)
    {
        return $query->where('sender_type', $senderType);
    }

    /**
     * Scope for messages by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for unread messages
     */
    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    /**
     * Scope for messages with files
     */
    public function scopeWithFiles($query)
    {
        return $query->whereNotNull('file_path');
    }

    /**
     * Scope for recent messages
     */
    public function scopeRecent($query, $minutes = 60)
    {
        return $query->where('created_at', '>=', now()->subMinutes($minutes));
    }

    /**
     * Convert message to array for API response
     */
    public function toApiArray()
    {
        return [
            'id' => $this->id,
            'message_id' => $this->message_id,
            'session_id' => $this->chatSession->session_id,
            'content' => $this->content,
            'display_content' => $this->display_content,
            'sender_type' => $this->sender_type,
            'sender_name' => $this->sender_name,
            'sender_avatar' => $this->sender_avatar,
            'type' => $this->type,
            'is_read' => $this->is_read,
            'has_file' => $this->hasFile(),
            'file' => $this->hasFile() ? [
                'name' => $this->file_name,
                'size' => $this->formatted_file_size,
                'type' => $this->file_type,
                'url' => $this->file_url,
                'is_image' => $this->isImage(),
                'is_document' => $this->isDocument(),
            ] : null,
            'created_at' => $this->created_at->toISOString(),
            'time_ago' => $this->time_ago,
            'metadata' => $this->metadata,
        ];
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class WorkingHour extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'site_id',
        'day_of_week',
        'start_time',
        'end_time',
        'is_working_day',
        'timezone',
        'break_start_time',
        'break_end_time',
        'is_24_hours',
        'holiday_dates',
        'special_hours',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_working_day' => 'boolean',
        'is_24_hours' => 'boolean',
        'holiday_dates' => 'array',
        'special_hours' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Days of the week
     */
    const DAYS_OF_WEEK = [
        'monday' => 'Monday',
        'tuesday' => 'Tuesday',
        'wednesday' => 'Wednesday',
        'thursday' => 'Thursday',
        'friday' => 'Friday',
        'saturday' => 'Saturday',
        'sunday' => 'Sunday',
    ];

    /**
     * Get the site that owns the working hours
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Check if current time is within working hours
     */
    public function isCurrentlyWorking()
    {
        return $this->isWorkingAt(now());
    }

    /**
     * Check if specific time is within working hours
     */
    public function isWorkingAt(Carbon $dateTime)
    {
        // Convert to site timezone
        $siteTime = $dateTime->setTimezone($this->timezone ?: config('app.timezone'));
        
        // Check if it's a holiday
        if ($this->isHoliday($siteTime)) {
            return false;
        }

        // Check if it's a working day
        $dayOfWeek = strtolower($siteTime->format('l'));
        if ($this->day_of_week !== $dayOfWeek || !$this->is_working_day) {
            return false;
        }

        // Check for special hours on this date
        $specialHours = $this->getSpecialHoursForDate($siteTime);
        if ($specialHours) {
            return $this->isTimeInRange($siteTime, $specialHours['start'], $specialHours['end']);
        }

        // Check if it's 24 hours
        if ($this->is_24_hours) {
            return true;
        }

        $currentTime = $siteTime->format('H:i:s');
        
        // Check if within working hours
        if (!$this->isTimeInRange($siteTime, $this->start_time, $this->end_time)) {
            return false;
        }

        // Check if within break time
        if ($this->break_start_time && $this->break_end_time) {
            if ($this->isTimeInRange($siteTime, $this->break_start_time, $this->break_end_time)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if date is a holiday
     */
    public function isHoliday(Carbon $date)
    {
        if (!$this->holiday_dates) {
            return false;
        }

        $dateString = $date->format('Y-m-d');
        return in_array($dateString, $this->holiday_dates);
    }

    /**
     * Get special hours for specific date
     */
    public function getSpecialHoursForDate(Carbon $date)
    {
        if (!$this->special_hours) {
            return null;
        }

        $dateString = $date->format('Y-m-d');
        return $this->special_hours[$dateString] ?? null;
    }

    /**
     * Check if time is within range
     */
    protected function isTimeInRange(Carbon $dateTime, $startTime, $endTime)
    {
        $currentTime = $dateTime->format('H:i:s');
        
        // Handle overnight shifts (e.g., 22:00 - 06:00)
        if ($startTime > $endTime) {
            return $currentTime >= $startTime || $currentTime <= $endTime;
        }
        
        return $currentTime >= $startTime && $currentTime <= $endTime;
    }

    /**
     * Get next working time
     */
    public function getNextWorkingTime()
    {
        $now = now()->setTimezone($this->timezone ?: config('app.timezone'));
        
        // Check next 7 days
        for ($i = 0; $i < 7; $i++) {
            $checkDate = $now->copy()->addDays($i);
            $dayOfWeek = strtolower($checkDate->format('l'));
            
            $workingHour = WorkingHour::where('site_id', $this->site_id)
                ->where('day_of_week', $dayOfWeek)
                ->where('is_working_day', true)
                ->first();
            
            if (!$workingHour) {
                continue;
            }

            // If it's today and we're still within working hours
            if ($i === 0 && $workingHour->isWorkingAt($checkDate)) {
                return $checkDate;
            }

            // Get start time for this day
            $specialHours = $workingHour->getSpecialHoursForDate($checkDate);
            $startTime = $specialHours ? $specialHours['start'] : $workingHour->start_time;
            
            if ($workingHour->is_24_hours) {
                $startTime = '00:00:00';
            }

            $workingStart = $checkDate->copy()->setTimeFromTimeString($startTime);
            
            // If it's today and the working time hasn't started yet
            if ($i === 0 && $workingStart->gt($now)) {
                return $workingStart;
            }
            
            // If it's a future day
            if ($i > 0) {
                return $workingStart;
            }
        }

        return null;
    }

    /**
     * Get working hours summary for display
     */
    public function getSummary()
    {
        if (!$this->is_working_day) {
            return 'Closed';
        }

        if ($this->is_24_hours) {
            return '24 Hours';
        }

        $summary = $this->start_time . ' - ' . $this->end_time;
        
        if ($this->break_start_time && $this->break_end_time) {
            $summary .= ' (Break: ' . $this->break_start_time . ' - ' . $this->break_end_time . ')';
        }

        return $summary;
    }

    /**
     * Scope for working days
     */
    public function scopeWorkingDays($query)
    {
        return $query->where('is_working_day', true);
    }

    /**
     * Scope for specific day
     */
    public function scopeForDay($query, $dayOfWeek)
    {
        return $query->where('day_of_week', strtolower($dayOfWeek));
    }

    /**
     * Scope for site
     */
    public function scopeForSite($query, $siteId)
    {
        return $query->where('site_id', $siteId);
    }

    /**
     * Get formatted working hours for all days
     */
    public static function getFormattedSchedule($siteId)
    {
        $workingHours = self::where('site_id', $siteId)->get()->keyBy('day_of_week');
        $schedule = [];

        foreach (self::DAYS_OF_WEEK as $key => $day) {
            $workingHour = $workingHours->get($key);
            $schedule[$key] = [
                'day' => $day,
                'is_working' => $workingHour ? $workingHour->is_working_day : false,
                'summary' => $workingHour ? $workingHour->getSummary() : 'Closed',
                'start_time' => $workingHour ? $workingHour->start_time : null,
                'end_time' => $workingHour ? $workingHour->end_time : null,
                'is_24_hours' => $workingHour ? $workingHour->is_24_hours : false,
                'break_start_time' => $workingHour ? $workingHour->break_start_time : null,
                'break_end_time' => $workingHour ? $workingHour->break_end_time : null,
            ];
        }

        return $schedule;
    }

    /**
     * Create default working hours for a site
     */
    public static function createDefaultSchedule($siteId, $timezone = null)
    {
        $defaultHours = [
            'monday' => ['09:00:00', '17:00:00', true],
            'tuesday' => ['09:00:00', '17:00:00', true],
            'wednesday' => ['09:00:00', '17:00:00', true],
            'thursday' => ['09:00:00', '17:00:00', true],
            'friday' => ['09:00:00', '17:00:00', true],
            'saturday' => ['10:00:00', '16:00:00', false],
            'sunday' => ['10:00:00', '16:00:00', false],
        ];

        foreach ($defaultHours as $day => [$start, $end, $isWorking]) {
            self::updateOrCreate(
                [
                    'site_id' => $siteId,
                    'day_of_week' => $day,
                ],
                [
                    'start_time' => $start,
                    'end_time' => $end,
                    'is_working_day' => $isWorking,
                    'timezone' => $timezone ?: config('app.timezone'),
                    'is_24_hours' => false,
                ]
            );
        }
    }
}

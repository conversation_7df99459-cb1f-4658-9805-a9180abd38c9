<?php

namespace App\Http\Middleware;

use App\Services\InternationalizationService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LocalizationMiddleware
{
    protected $i18nService;

    public function __construct(InternationalizationService $i18nService)
    {
        $this->i18nService = $i18nService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Get site ID from request (assuming it's available in the request)
        $siteId = $this->getSiteId($request);
        
        if (!$siteId) {
            return $next($request);
        }

        // Determine the language to use
        $languageCode = $this->determineLanguage($request, $siteId);
        
        if ($languageCode) {
            // Set the application locale
            App::setLocale($languageCode);
            
            // Store language in session for future requests
            Session::put('language', $languageCode);
            
            // Add language information to request
            $request->attributes->set('language_code', $languageCode);
            $request->attributes->set('site_id', $siteId);
            
            // Get language details and add to request
            $language = $this->i18nService->getLanguageByCode($siteId, $languageCode);
            if ($language) {
                $request->attributes->set('language', $language);
                
                // Set timezone if available
                if ($language->timezone) {
                    config(['app.timezone' => $language->timezone]);
                }
            }
        }

        return $next($request);
    }

    /**
     * Determine the language to use for the request
     */
    protected function determineLanguage(Request $request, $siteId)
    {
        // Priority order:
        // 1. URL parameter (?lang=en)
        // 2. Session stored language
        // 3. Accept-Language header
        // 4. Site default language

        // 1. Check URL parameter
        if ($request->has('lang')) {
            $langCode = $request->get('lang');
            if ($this->isValidLanguage($siteId, $langCode)) {
                return $langCode;
            }
        }

        // 2. Check session
        $sessionLang = Session::get('language');
        if ($sessionLang && $this->isValidLanguage($siteId, $sessionLang)) {
            return $sessionLang;
        }

        // 3. Check Accept-Language header
        $acceptLanguage = $request->header('Accept-Language');
        if ($acceptLanguage) {
            $preferredLang = $this->parseAcceptLanguage($acceptLanguage, $siteId);
            if ($preferredLang) {
                return $preferredLang;
            }
        }

        // 4. Use site default language
        $defaultLanguage = $this->i18nService->getDefaultLanguage($siteId);
        return $defaultLanguage ? $defaultLanguage->code : null;
    }

    /**
     * Get site ID from request
     */
    protected function getSiteId(Request $request)
    {
        // Try to get site ID from various sources
        
        // 1. Route parameter
        if ($request->route('siteId')) {
            return $request->route('siteId');
        }

        // 2. Request parameter
        if ($request->has('site_id')) {
            return $request->get('site_id');
        }

        // 3. Header
        if ($request->header('X-Site-ID')) {
            return $request->header('X-Site-ID');
        }

        // 4. Subdomain (if using subdomain-based multi-tenancy)
        $host = $request->getHost();
        $subdomain = explode('.', $host)[0];
        
        // This would require a lookup to map subdomain to site ID
        // For now, return null if no site ID found
        return null;
    }

    /**
     * Check if a language code is valid for the site
     */
    protected function isValidLanguage($siteId, $languageCode)
    {
        try {
            $language = $this->i18nService->getLanguageByCode($siteId, $languageCode);
            return $language && $language->is_active;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Parse Accept-Language header and find best match
     */
    protected function parseAcceptLanguage($acceptLanguage, $siteId)
    {
        // Parse Accept-Language header
        // Format: "en-US,en;q=0.9,es;q=0.8,zh;q=0.7"
        
        $languages = [];
        $parts = explode(',', $acceptLanguage);
        
        foreach ($parts as $part) {
            $part = trim($part);
            
            if (strpos($part, ';q=') !== false) {
                list($lang, $quality) = explode(';q=', $part);
                $quality = (float) $quality;
            } else {
                $lang = $part;
                $quality = 1.0;
            }
            
            // Extract main language code (e.g., 'en' from 'en-US')
            $langCode = explode('-', $lang)[0];
            
            $languages[$langCode] = $quality;
        }
        
        // Sort by quality (preference)
        arsort($languages);
        
        // Find the first available language
        foreach (array_keys($languages) as $langCode) {
            if ($this->isValidLanguage($siteId, $langCode)) {
                return $langCode;
            }
        }
        
        return null;
    }
}

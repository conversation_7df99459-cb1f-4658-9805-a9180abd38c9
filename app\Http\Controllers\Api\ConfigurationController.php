<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Site;
use App\Models\SiteConfiguration;
use App\Models\Theme;
use App\Models\Language;
use App\Models\WorkingHours;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;

class ConfigurationController extends Controller
{
    /**
     * Get site configurations
     */
    public function getSiteConfigurations(Request $request): JsonResponse
    {
        $siteId = $request->get('site_id');
        
        if ($siteId) {
            $site = Site::with(['configurations', 'theme', 'workingHours'])->find($siteId);
            
            if (!$site) {
                return response()->json([
                    'success' => false,
                    'message' => 'Site not found'
                ], 404);
            }
            
            return response()->json([
                'success' => true,
                'data' => $site
            ]);
        }

        $sites = Site::with(['configurations', 'theme', 'workingHours'])->get();

        return response()->json([
            'success' => true,
            'data' => $sites
        ]);
    }

    /**
     * Update site configuration
     */
    public function updateSiteConfiguration(Request $request, int $siteId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'site_name' => 'nullable|string|max:255',
            'site_url' => 'nullable|url',
            'contact_email' => 'nullable|email',
            'contact_phone' => 'nullable|string|max:20',
            'timezone' => 'nullable|string|max:50',
            'language' => 'nullable|string|max:10',
            'theme_id' => 'nullable|exists:themes,id',
            'is_active' => 'nullable|boolean',
            'configurations' => 'nullable|array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $site = Site::find($siteId);

        if (!$site) {
            return response()->json([
                'success' => false,
                'message' => 'Site not found'
            ], 404);
        }

        // Update site basic info
        $siteData = array_filter([
            'site_name' => $request->site_name,
            'site_url' => $request->site_url,
            'contact_email' => $request->contact_email,
            'contact_phone' => $request->contact_phone,
            'timezone' => $request->timezone,
            'language' => $request->language,
            'theme_id' => $request->theme_id,
            'is_active' => $request->is_active
        ], function ($value) {
            return $value !== null;
        });

        $site->update($siteData);

        // Update configurations
        if ($request->has('configurations')) {
            foreach ($request->configurations as $key => $value) {
                SiteConfiguration::updateOrCreate(
                    ['site_id' => $site->id, 'key' => $key],
                    ['value' => $value]
                );
            }
        }

        // Clear cache
        Cache::forget("site_config_{$site->id}");

        return response()->json([
            'success' => true,
            'message' => 'Site configuration updated successfully',
            'data' => $site->fresh(['configurations', 'theme'])
        ]);
    }

    /**
     * Get themes
     */
    public function getThemes(Request $request): JsonResponse
    {
        $themes = Theme::where('is_active', true)->get();

        return response()->json([
            'success' => true,
            'data' => $themes
        ]);
    }

    /**
     * Create theme
     */
    public function createTheme(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'primary_color' => 'required|string|max:7',
            'secondary_color' => 'required|string|max:7',
            'accent_color' => 'required|string|max:7',
            'background_color' => 'required|string|max:7',
            'text_color' => 'required|string|max:7',
            'border_radius' => 'nullable|integer|min:0|max:50',
            'font_family' => 'nullable|string|max:100',
            'custom_css' => 'nullable|string',
            'is_default' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // If this is set as default, unset other defaults
        if ($request->boolean('is_default')) {
            Theme::where('is_default', true)->update(['is_default' => false]);
        }

        $theme = Theme::create($request->all());

        return response()->json([
            'success' => true,
            'message' => 'Theme created successfully',
            'data' => $theme
        ]);
    }

    /**
     * Update theme
     */
    public function updateTheme(Request $request, int $themeId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:500',
            'primary_color' => 'nullable|string|max:7',
            'secondary_color' => 'nullable|string|max:7',
            'accent_color' => 'nullable|string|max:7',
            'background_color' => 'nullable|string|max:7',
            'text_color' => 'nullable|string|max:7',
            'border_radius' => 'nullable|integer|min:0|max:50',
            'font_family' => 'nullable|string|max:100',
            'custom_css' => 'nullable|string',
            'is_default' => 'nullable|boolean',
            'is_active' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $theme = Theme::find($themeId);

        if (!$theme) {
            return response()->json([
                'success' => false,
                'message' => 'Theme not found'
            ], 404);
        }

        // If this is set as default, unset other defaults
        if ($request->boolean('is_default')) {
            Theme::where('is_default', true)->where('id', '!=', $themeId)->update(['is_default' => false]);
        }

        $theme->update(array_filter($request->all(), function ($value) {
            return $value !== null;
        }));

        return response()->json([
            'success' => true,
            'message' => 'Theme updated successfully',
            'data' => $theme
        ]);
    }

    /**
     * Get working hours
     */
    public function getWorkingHours(Request $request): JsonResponse
    {
        $siteId = $request->get('site_id');
        
        $query = WorkingHours::query();
        
        if ($siteId) {
            $query->where('site_id', $siteId);
        }
        
        $workingHours = $query->get();

        return response()->json([
            'success' => true,
            'data' => $workingHours
        ]);
    }

    /**
     * Update working hours
     */
    public function updateWorkingHours(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'site_id' => 'required|exists:sites,id',
            'working_hours' => 'required|array',
            'working_hours.*.day' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'working_hours.*.is_enabled' => 'required|boolean',
            'working_hours.*.start_time' => 'nullable|date_format:H:i',
            'working_hours.*.end_time' => 'nullable|date_format:H:i',
            'working_hours.*.timezone' => 'nullable|string|max:50'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $siteId = $request->site_id;

        // Delete existing working hours for this site
        WorkingHours::where('site_id', $siteId)->delete();

        // Create new working hours
        foreach ($request->working_hours as $hours) {
            WorkingHours::create([
                'site_id' => $siteId,
                'day' => $hours['day'],
                'is_enabled' => $hours['is_enabled'],
                'start_time' => $hours['start_time'],
                'end_time' => $hours['end_time'],
                'timezone' => $hours['timezone'] ?? 'UTC'
            ]);
        }

        // Clear cache
        Cache::forget("working_hours_{$siteId}");

        return response()->json([
            'success' => true,
            'message' => 'Working hours updated successfully'
        ]);
    }

    /**
     * Get languages
     */
    public function getLanguages(Request $request): JsonResponse
    {
        $languages = Language::where('is_active', true)->get();

        return response()->json([
            'success' => true,
            'data' => $languages
        ]);
    }

    /**
     * Get system settings
     */
    public function getSystemSettings(Request $request): JsonResponse
    {
        $settings = [
            'app_name' => config('app.name'),
            'app_version' => config('app.version', '1.0.0'),
            'timezone' => config('app.timezone'),
            'locale' => config('app.locale'),
            'max_file_size' => config('filesystems.max_file_size', 10240),
            'allowed_file_types' => config('filesystems.allowed_types', ['jpg', 'png', 'pdf', 'doc', 'docx']),
            'session_timeout' => config('session.lifetime'),
            'websocket_enabled' => config('websockets.enabled', true),
            'redis_enabled' => config('database.redis.enabled', true),
            'queue_enabled' => config('queue.enabled', true),
            'mail_enabled' => config('mail.enabled', true)
        ];

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }

    /**
     * Update system settings
     */
    public function updateSystemSettings(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'max_file_size' => 'nullable|integer|min:1024|max:51200',
            'allowed_file_types' => 'nullable|array',
            'session_timeout' => 'nullable|integer|min:5|max:1440',
            'websocket_enabled' => 'nullable|boolean',
            'redis_enabled' => 'nullable|boolean',
            'queue_enabled' => 'nullable|boolean',
            'mail_enabled' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Update configuration files or database settings
        // This would typically update .env file or database configuration table
        
        return response()->json([
            'success' => true,
            'message' => 'System settings updated successfully'
        ]);
    }

    /**
     * Clear cache
     */
    public function clearCache(Request $request): JsonResponse
    {
        $cacheType = $request->get('type', 'all'); // all, config, routes, views

        try {
            switch ($cacheType) {
                case 'config':
                    \Artisan::call('config:clear');
                    break;
                case 'routes':
                    \Artisan::call('route:clear');
                    break;
                case 'views':
                    \Artisan::call('view:clear');
                    break;
                case 'all':
                default:
                    \Artisan::call('cache:clear');
                    \Artisan::call('config:clear');
                    \Artisan::call('route:clear');
                    \Artisan::call('view:clear');
                    break;
            }

            return response()->json([
                'success' => true,
                'message' => 'Cache cleared successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage()
            ], 500);
        }
    }
}

# 故障排除指南

## 常见问题及解决方案

### 1. 安装和配置问题

#### 1.1 宝塔面板安装失败
**问题**: 宝塔面板安装过程中出错或无法访问

**解决方案**:
```bash
# CentOS 7 重新安装
yum install -y wget
wget -O install.sh http://download.bt.cn/install/install_6.0.sh
sh install.sh ed8484bec

# 检查防火墙
systemctl status firewalld
firewall-cmd --list-ports

# 开放宝塔端口
firewall-cmd --permanent --add-port=8888/tcp
firewall-cmd --reload

# 检查宝塔服务
/etc/init.d/bt status
/etc/init.d/bt restart
```

#### 1.2 PHP扩展安装失败
**问题**: 某些PHP扩展无法安装或加载失败

**解决方案**:
```bash
# 检查PHP版本和扩展
php -v
php -m | grep redis
php -m | grep gd

# 手动编译安装扩展（以redis为例）
cd /www/server/php/72/src/ext/redis
/www/server/php/72/bin/phpize
./configure --with-php-config=/www/server/php/72/bin/php-config
make && make install

# 在php.ini中添加扩展
echo "extension=redis.so" >> /www/server/php/72/etc/php.ini

# 重启PHP-FPM
systemctl restart php-fpm-72
```

#### 1.3 Composer安装依赖失败
**问题**: composer install 过程中出现错误

**解决方案**:
```bash
# 更新Composer到最新版本
composer self-update

# 使用国内镜像
composer config -g repo.packagist composer https://mirrors.aliyun.com/composer/

# 清除缓存重新安装
composer clear-cache
composer install --no-dev --optimize-autoloader

# 如果内存不足
php -d memory_limit=2G /usr/local/bin/composer install

# 跳过平台检查（如果PHP版本检查失败）
composer install --ignore-platform-reqs
```

### 2. 数据库问题

#### 2.1 数据库连接失败
**问题**: Laravel无法连接到MySQL数据库

**解决方案**:
```bash
# 检查MySQL服务状态
systemctl status mysqld
systemctl start mysqld

# 检查MySQL端口
netstat -tulpn | grep 3306

# 测试数据库连接
mysql -h 127.0.0.1 -u chat_web -p chat_web

# 检查用户权限
mysql -u root -p
SHOW GRANTS FOR 'chat_web'@'localhost';
GRANT ALL PRIVILEGES ON chat_web.* TO 'chat_web'@'localhost';
FLUSH PRIVILEGES;

# 检查.env配置
cat .env | grep DB_
```

#### 2.2 数据库迁移失败
**问题**: php artisan migrate 执行失败

**解决方案**:
```bash
# 检查数据库连接
php artisan tinker
DB::connection()->getPdo();

# 查看迁移状态
php artisan migrate:status

# 重置迁移（谨慎使用）
php artisan migrate:reset
php artisan migrate

# 单独运行失败的迁移
php artisan migrate --path=/database/migrations/2024_01_01_000000_create_users_table.php

# 检查MySQL错误日志
tail -f /var/log/mysqld.log
```

#### 2.3 字符集问题
**问题**: 中文字符显示乱码或插入失败

**解决方案**:
```sql
-- 检查数据库字符集
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';

-- 修改数据库字符集
ALTER DATABASE chat_web CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修改表字符集
ALTER TABLE table_name CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

在MySQL配置文件中添加：
```ini
[mysqld]
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
```

### 3. Redis问题

#### 3.1 Redis连接失败
**问题**: Laravel无法连接到Redis

**解决方案**:
```bash
# 检查Redis服务状态
systemctl status redis
systemctl start redis

# 检查Redis端口
netstat -tulpn | grep 6379

# 测试Redis连接
redis-cli ping
redis-cli -a your_password ping

# 检查Redis配置
cat /etc/redis.conf | grep -E "(bind|port|requirepass)"

# 检查PHP Redis扩展
php -m | grep redis
```

#### 3.2 Redis内存不足
**问题**: Redis内存使用过高或内存不足

**解决方案**:
```bash
# 查看Redis内存使用
redis-cli info memory

# 清理Redis缓存
redis-cli flushall

# 优化Redis配置
vim /etc/redis.conf
```

```redis
# 设置最大内存
maxmemory 1gb

# 设置内存淘汰策略
maxmemory-policy allkeys-lru

# 启用内存压缩
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
```

### 4. WebSocket问题

#### 4.1 WebSocket服务无法启动
**问题**: WebSocket服务启动失败或异常退出

**解决方案**:
```bash
# 检查端口占用
netstat -tulpn | grep 8080
lsof -i :8080

# 杀死占用端口的进程
kill -9 $(lsof -t -i:8080)

# 手动启动WebSocket服务
cd /www/wwwroot/your-domain.com
php artisan websocket:serve

# 检查服务日志
journalctl -u chat-websocket -f

# 检查防火墙
firewall-cmd --list-ports
firewall-cmd --permanent --add-port=8080/tcp
firewall-cmd --reload
```

#### 4.2 WebSocket连接被拒绝
**问题**: 前端无法连接到WebSocket服务器

**解决方案**:
```bash
# 检查Nginx WebSocket代理配置
vim /www/server/nginx/conf/vhost/your-domain.com.conf
```

确保包含WebSocket代理配置：
```nginx
location /ws {
    proxy_pass http://127.0.0.1:8080;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

```bash
# 重启Nginx
systemctl restart nginx

# 测试WebSocket连接
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Key: test" -H "Sec-WebSocket-Version: 13" http://your-domain.com:8080/
```

### 5. 文件权限问题

#### 5.1 存储目录不可写
**问题**: 文件上传失败或日志无法写入

**解决方案**:
```bash
# 检查目录权限
ls -la storage/
ls -la bootstrap/cache/

# 重新设置权限
cd /www/wwwroot/your-domain.com
chown -R www:www .
find . -type d -exec chmod 755 {} \;
find . -type f -exec chmod 644 {} \;
chmod -R 775 storage
chmod -R 775 bootstrap/cache

# 检查SELinux状态（CentOS）
getenforce
setenforce 0  # 临时关闭
```

#### 5.2 符号链接创建失败
**问题**: storage:link 命令失败

**解决方案**:
```bash
# 删除已存在的链接
rm -f public/storage

# 重新创建符号链接
php artisan storage:link

# 手动创建符号链接
ln -s /www/wwwroot/your-domain.com/storage/app/public /www/wwwroot/your-domain.com/public/storage

# 检查链接是否正确
ls -la public/storage
```

### 6. 性能问题

#### 6.1 页面加载缓慢
**问题**: 网站响应速度慢

**解决方案**:
```bash
# 启用OPcache
vim /www/server/php/72/etc/php.ini
```

```ini
opcache.enable=1
opcache.memory_consumption=256
opcache.interned_strings_buffer=16
opcache.max_accelerated_files=10000
opcache.revalidate_freq=2
opcache.fast_shutdown=1
```

```bash
# 优化Laravel缓存
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 优化Composer自动加载
composer dump-autoload --optimize

# 检查慢查询
tail -f /var/log/mysql/slow.log
```

#### 6.2 内存使用过高
**问题**: 服务器内存不足

**解决方案**:
```bash
# 查看内存使用
free -h
ps aux --sort=-%mem | head

# 优化PHP-FPM配置
vim /www/server/php/72/etc/php-fpm.conf
```

```ini
pm = dynamic
pm.max_children = 20
pm.start_servers = 5
pm.min_spare_servers = 2
pm.max_spare_servers = 10
pm.max_requests = 500
```

```bash
# 重启PHP-FPM
systemctl restart php-fpm-72

# 添加swap空间（临时解决）
dd if=/dev/zero of=/swapfile bs=1024 count=1048576
mkswap /swapfile
swapon /swapfile
```

### 7. SSL证书问题

#### 7.1 SSL证书申请失败
**问题**: Let's Encrypt证书申请失败

**解决方案**:
```bash
# 检查域名解析
nslookup your-domain.com
ping your-domain.com

# 检查80端口是否开放
curl -I http://your-domain.com

# 手动申请证书
certbot certonly --webroot -w /www/wwwroot/your-domain.com/public -d your-domain.com

# 检查证书状态
certbot certificates
```

#### 7.2 SSL证书过期
**问题**: HTTPS访问提示证书过期

**解决方案**:
```bash
# 检查证书有效期
openssl x509 -in /path/to/cert.pem -text -noout | grep "Not After"

# 续期证书
certbot renew

# 设置自动续期
echo "0 2 * * * certbot renew --quiet" | crontab -

# 重启Nginx
systemctl restart nginx
```

### 8. 邮件发送问题

#### 8.1 邮件发送失败
**问题**: 系统无法发送邮件通知

**解决方案**:
```bash
# 检查邮件配置
cat .env | grep MAIL_

# 测试邮件发送
php artisan tinker
Mail::raw('Test email', function($message) {
    $message->to('<EMAIL>')->subject('Test');
});

# 查看邮件队列
php artisan queue:failed

# 重试失败的邮件
php artisan queue:retry all
```

检查SMTP配置：
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls
```

### 9. 队列问题

#### 9.1 队列任务不执行
**问题**: 队列中的任务不被处理

**解决方案**:
```bash
# 检查队列服务状态
systemctl status chat-queue

# 手动启动队列处理器
php artisan queue:work

# 查看队列状态
php artisan queue:monitor

# 重启队列服务
systemctl restart chat-queue

# 查看失败的任务
php artisan queue:failed
php artisan queue:retry all
```

### 10. 日志和调试

#### 10.1 启用调试模式
```env
APP_DEBUG=true
LOG_LEVEL=debug
```

#### 10.2 查看关键日志
```bash
# Laravel应用日志
tail -f storage/logs/laravel.log

# Nginx访问日志
tail -f /www/wwwlogs/your-domain.com.log

# Nginx错误日志
tail -f /www/wwwlogs/your-domain.com.error.log

# MySQL错误日志
tail -f /var/log/mysqld.log

# Redis日志
tail -f /var/log/redis/redis.log

# 系统日志
tail -f /var/log/messages
```

#### 10.3 性能分析
```bash
# 安装性能分析工具
yum install -y htop iotop nethogs

# 查看系统资源
htop
iotop
nethogs

# 查看MySQL进程
mysqladmin -u root -p processlist

# 分析慢查询
mysqldumpslow /var/log/mysql/slow.log
```

---

## 紧急恢复程序

### 1. 数据库恢复
```bash
# 从备份恢复数据库
mysql -u root -p chat_web < /www/backup/database/chat_20240101.sql
```

### 2. 文件恢复
```bash
# 从备份恢复文件
tar -xzf /www/backup/files/chat_files_20240101.tar.gz -C /
```

### 3. 服务重启
```bash
# 重启所有相关服务
systemctl restart nginx
systemctl restart mysqld
systemctl restart redis
systemctl restart php-fpm-72
systemctl restart chat-websocket
systemctl restart chat-queue
```

### 4. 清除所有缓存
```bash
cd /www/wwwroot/your-domain.com
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
redis-cli flushall
```

---

**遇到问题时，请按照此指南逐步排查，大多数问题都能得到解决！** 🔧

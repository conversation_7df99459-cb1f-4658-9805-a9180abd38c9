<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WebhookLog extends Model
{
    use HasFactory;

    protected $table = 'webhook_logs';

    protected $fillable = [
        'type',
        'payload',
        'success',
        'response_message',
        'response_data',
        'processed_at'
    ];

    protected $casts = [
        'success' => 'boolean',
        'processed_at' => 'datetime'
    ];

    /**
     * Get the parsed payload
     */
    public function getParsedPayloadAttribute()
    {
        return json_decode($this->payload, true);
    }

    /**
     * Get the parsed response data
     */
    public function getParsedResponseDataAttribute()
    {
        return $this->response_data ? json_decode($this->response_data, true) : null;
    }

    /**
     * Scope for successful webhooks
     */
    public function scopeSuccessful($query)
    {
        return $query->where('success', true);
    }

    /**
     * Scope for failed webhooks
     */
    public function scopeFailed($query)
    {
        return $query->where('success', false);
    }

    /**
     * Scope for specific webhook type
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }
}

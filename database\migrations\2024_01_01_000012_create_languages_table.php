<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLanguagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('languages', function (Blueprint $table) {
            $table->id();
            $table->string('code', 10)->unique(); // en, es, fr, de, etc.
            $table->string('name'); // English, Spanish, French, German
            $table->string('native_name'); // English, Español, Français, Deutsch
            $table->string('flag')->nullable(); // Flag emoji or image path
            $table->boolean('is_active')->default(true);
            $table->boolean('is_rtl')->default(false); // Right-to-left languages
            $table->integer('sort_order')->default(0);
            $table->timestamps();
            
            $table->index(['is_active', 'sort_order']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('languages');
    }
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Notifications\SystemNotification;
use App\Notifications\ChatNotification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Notifications\DatabaseNotification;

class NotificationController extends Controller
{
    /**
     * Get user notifications
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->user();
        $perPage = $request->get('per_page', 20);
        $unreadOnly = $request->boolean('unread_only', false);

        $query = $user->notifications();

        if ($unreadOnly) {
            $query->whereNull('read_at');
        }

        $notifications = $query->orderBy('created_at', 'desc')
            ->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => [
                'notifications' => $notifications->items(),
                'pagination' => [
                    'current_page' => $notifications->currentPage(),
                    'last_page' => $notifications->lastPage(),
                    'per_page' => $notifications->perPage(),
                    'total' => $notifications->total()
                ],
                'unread_count' => $user->unreadNotifications()->count()
            ]
        ]);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Request $request, string $notificationId): JsonResponse
    {
        $user = $request->user();
        $notification = $user->notifications()->where('id', $notificationId)->first();

        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'Notification not found'
            ], 404);
        }

        $notification->markAsRead();

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read'
        ]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead(Request $request): JsonResponse
    {
        $user = $request->user();
        $user->unreadNotifications->markAsRead();

        return response()->json([
            'success' => true,
            'message' => 'All notifications marked as read'
        ]);
    }

    /**
     * Delete notification
     */
    public function delete(Request $request, string $notificationId): JsonResponse
    {
        $user = $request->user();
        $notification = $user->notifications()->where('id', $notificationId)->first();

        if (!$notification) {
            return response()->json([
                'success' => false,
                'message' => 'Notification not found'
            ], 404);
        }

        $notification->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notification deleted'
        ]);
    }

    /**
     * Clear all notifications
     */
    public function clearAll(Request $request): JsonResponse
    {
        $user = $request->user();
        $user->notifications()->delete();

        return response()->json([
            'success' => true,
            'message' => 'All notifications cleared'
        ]);
    }

    /**
     * Send notification to user
     */
    public function sendToUser(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'type' => 'required|in:system,chat,alert,info',
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:1000',
            'data' => 'nullable|array',
            'priority' => 'nullable|in:low,medium,high,urgent'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::find($request->user_id);
        
        $notificationData = [
            'title' => $request->title,
            'message' => $request->message,
            'type' => $request->type,
            'priority' => $request->priority ?? 'medium',
            'data' => $request->data ?? [],
            'sender_id' => $request->user()->id
        ];

        if ($request->type === 'chat') {
            $user->notify(new ChatNotification($notificationData));
        } else {
            $user->notify(new SystemNotification($notificationData));
        }

        return response()->json([
            'success' => true,
            'message' => 'Notification sent successfully'
        ]);
    }

    /**
     * Send notification to multiple users
     */
    public function sendToMultiple(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'type' => 'required|in:system,chat,alert,info',
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:1000',
            'data' => 'nullable|array',
            'priority' => 'nullable|in:low,medium,high,urgent'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $users = User::whereIn('id', $request->user_ids)->get();
        
        $notificationData = [
            'title' => $request->title,
            'message' => $request->message,
            'type' => $request->type,
            'priority' => $request->priority ?? 'medium',
            'data' => $request->data ?? [],
            'sender_id' => $request->user()->id
        ];

        $sentCount = 0;
        foreach ($users as $user) {
            try {
                if ($request->type === 'chat') {
                    $user->notify(new ChatNotification($notificationData));
                } else {
                    $user->notify(new SystemNotification($notificationData));
                }
                $sentCount++;
            } catch (\Exception $e) {
                \Log::error('Failed to send notification to user ' . $user->id . ': ' . $e->getMessage());
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Notification sent to {$sentCount} users",
            'data' => [
                'sent_count' => $sentCount,
                'total_recipients' => count($request->user_ids)
            ]
        ]);
    }

    /**
     * Send broadcast notification
     */
    public function sendBroadcast(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:system,alert,info',
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:1000',
            'data' => 'nullable|array',
            'priority' => 'nullable|in:low,medium,high,urgent',
            'roles' => 'nullable|array',
            'roles.*' => 'in:admin,supervisor,agent'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $query = User::where('is_active', true);

        if ($request->has('roles')) {
            $query->whereIn('role', $request->roles);
        }

        $users = $query->get();
        
        $notificationData = [
            'title' => $request->title,
            'message' => $request->message,
            'type' => $request->type,
            'priority' => $request->priority ?? 'medium',
            'data' => $request->data ?? [],
            'sender_id' => $request->user()->id,
            'is_broadcast' => true
        ];

        $sentCount = 0;
        foreach ($users as $user) {
            try {
                $user->notify(new SystemNotification($notificationData));
                $sentCount++;
            } catch (\Exception $e) {
                \Log::error('Failed to send broadcast notification to user ' . $user->id . ': ' . $e->getMessage());
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Broadcast notification sent to {$sentCount} users",
            'data' => [
                'sent_count' => $sentCount
            ]
        ]);
    }

    /**
     * Get notification settings
     */
    public function getSettings(Request $request): JsonResponse
    {
        $user = $request->user();
        
        $settings = [
            'email_notifications' => $user->email_notifications ?? true,
            'push_notifications' => $user->push_notifications ?? true,
            'sound_notifications' => $user->sound_notifications ?? true,
            'desktop_notifications' => $user->desktop_notifications ?? true,
            'notification_types' => [
                'new_chat' => $user->notify_new_chat ?? true,
                'chat_transfer' => $user->notify_chat_transfer ?? true,
                'system_alerts' => $user->notify_system_alerts ?? true,
                'mentions' => $user->notify_mentions ?? true
            ]
        ];

        return response()->json([
            'success' => true,
            'data' => $settings
        ]);
    }

    /**
     * Update notification settings
     */
    public function updateSettings(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'email_notifications' => 'nullable|boolean',
            'push_notifications' => 'nullable|boolean',
            'sound_notifications' => 'nullable|boolean',
            'desktop_notifications' => 'nullable|boolean',
            'notify_new_chat' => 'nullable|boolean',
            'notify_chat_transfer' => 'nullable|boolean',
            'notify_system_alerts' => 'nullable|boolean',
            'notify_mentions' => 'nullable|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = $request->user();
        $user->update(array_filter($request->all(), function ($value) {
            return $value !== null;
        }));

        return response()->json([
            'success' => true,
            'message' => 'Notification settings updated successfully'
        ]);
    }

    /**
     * Get notification statistics
     */
    public function getStatistics(Request $request): JsonResponse
    {
        $user = $request->user();
        $period = $request->get('period', '7d');
        $startDate = $this->getStartDateForPeriod($period);

        $stats = [
            'total_notifications' => $user->notifications()->count(),
            'unread_notifications' => $user->unreadNotifications()->count(),
            'recent_notifications' => $user->notifications()
                ->where('created_at', '>=', $startDate)
                ->count(),
            'notifications_by_type' => $user->notifications()
                ->where('created_at', '>=', $startDate)
                ->get()
                ->groupBy('type')
                ->map(function ($notifications) {
                    return $notifications->count();
                }),
            'daily_notifications' => $user->notifications()
                ->where('created_at', '>=', $startDate)
                ->get()
                ->groupBy(function ($notification) {
                    return $notification->created_at->format('Y-m-d');
                })
                ->map(function ($notifications) {
                    return $notifications->count();
                })
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get start date for period
     */
    private function getStartDateForPeriod(string $period): \Carbon\Carbon
    {
        switch ($period) {
            case '1d':
                return \Carbon\Carbon::today();
            case '7d':
                return \Carbon\Carbon::now()->subDays(7);
            case '30d':
                return \Carbon\Carbon::now()->subDays(30);
            default:
                return \Carbon\Carbon::now()->subDays(7);
        }
    }
}

<?php

namespace App\Console\Commands;

use App\Services\InternationalizationService;
use App\Models\Language;
use App\Models\Site;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ManageTranslations extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'translations:manage 
                            {action : Action to perform (export|import|sync|stats|create-language)}
                            {--site= : Site ID}
                            {--language= : Language code}
                            {--file= : File path for import/export}
                            {--format=json : Export format (json|php)}
                            {--overwrite : Overwrite existing translations on import}';

    /**
     * The console command description.
     */
    protected $description = 'Manage translations for the chat system';

    protected $i18nService;

    public function __construct(InternationalizationService $i18nService)
    {
        parent::__construct();
        $this->i18nService = $i18nService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'export':
                return $this->exportTranslations();
            case 'import':
                return $this->importTranslations();
            case 'sync':
                return $this->syncTranslations();
            case 'stats':
                return $this->showStats();
            case 'create-language':
                return $this->createLanguage();
            default:
                $this->error("Unknown action: {$action}");
                return 1;
        }
    }

    /**
     * Export translations
     */
    protected function exportTranslations()
    {
        $siteId = $this->option('site');
        $languageCode = $this->option('language');
        $filePath = $this->option('file');
        $format = $this->option('format');

        if (!$siteId || !$languageCode) {
            $this->error('Site ID and language code are required for export');
            return 1;
        }

        try {
            $exportData = $this->i18nService->exportTranslations($siteId, $languageCode, $format);
            
            if ($filePath) {
                File::put($filePath, $exportData);
                $this->info("Translations exported to: {$filePath}");
            } else {
                $this->line($exportData);
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("Export failed: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Import translations
     */
    protected function importTranslations()
    {
        $siteId = $this->option('site');
        $languageCode = $this->option('language');
        $filePath = $this->option('file');
        $overwrite = $this->option('overwrite');

        if (!$siteId || !$languageCode || !$filePath) {
            $this->error('Site ID, language code, and file path are required for import');
            return 1;
        }

        if (!File::exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        try {
            $data = File::get($filePath);
            $result = $this->i18nService->importTranslations($siteId, $languageCode, $data, $overwrite);

            $this->info("Import completed:");
            $this->line("  Created: {$result['created']}");
            $this->line("  Updated: {$result['updated']}");
            $this->line("  Total: {$result['total']}");

            return 0;

        } catch (\Exception $e) {
            $this->error("Import failed: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Sync translations across languages
     */
    protected function syncTranslations()
    {
        $siteId = $this->option('site');

        if (!$siteId) {
            $this->error('Site ID is required for sync');
            return 1;
        }

        try {
            $languages = $this->i18nService->getLanguagesForSite($siteId, true);
            $defaultLanguage = $this->i18nService->getDefaultLanguage($siteId);

            if (!$defaultLanguage) {
                $this->error('No default language found for site');
                return 1;
            }

            $this->info("Syncing translations for site {$siteId}");
            $this->info("Default language: {$defaultLanguage->name} ({$defaultLanguage->code})");

            // Get all translations from default language
            $defaultTranslations = $this->i18nService->getAllTranslations($siteId, $defaultLanguage->code);
            
            $syncedCount = 0;
            $bar = $this->output->createProgressBar(count($languages) - 1); // Exclude default language

            foreach ($languages as $language) {
                if ($language->code === $defaultLanguage->code) {
                    continue; // Skip default language
                }

                $existingTranslations = $this->i18nService->getAllTranslations($siteId, $language->code);
                $missingKeys = array_diff_key($defaultTranslations, $existingTranslations);

                if (!empty($missingKeys)) {
                    // Create placeholder translations for missing keys
                    $placeholderTranslations = [];
                    foreach ($missingKeys as $key => $value) {
                        $placeholderTranslations[$key] = "[TRANSLATE] " . $value;
                    }

                    $this->i18nService->bulkSetTranslations($siteId, $language->code, $placeholderTranslations, false);
                    $syncedCount += count($missingKeys);
                }

                $bar->advance();
            }

            $bar->finish();
            $this->newLine();
            $this->info("Sync completed. Added {$syncedCount} placeholder translations.");

            return 0;

        } catch (\Exception $e) {
            $this->error("Sync failed: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Show translation statistics
     */
    protected function showStats()
    {
        $siteId = $this->option('site');
        $languageCode = $this->option('language');

        if (!$siteId) {
            $this->error('Site ID is required for stats');
            return 1;
        }

        try {
            $stats = $this->i18nService->getTranslationStats($siteId, $languageCode);

            if ($languageCode) {
                // Single language stats
                $this->info("Translation Statistics for {$stats['language']}:");
                $this->table(
                    ['Metric', 'Value'],
                    [
                        ['Total Translations', $stats['total_translations']],
                        ['Approved Translations', $stats['approved_translations']],
                        ['Pending Translations', $stats['pending_translations']],
                        ['System Translations', $stats['system_translations']],
                        ['Custom Translations', $stats['custom_translations']],
                        ['Completion Percentage', $stats['completion_percentage'] . '%'],
                    ]
                );
            } else {
                // All languages stats
                $this->info("Translation Statistics for Site {$siteId}:");
                $headers = ['Language', 'Total', 'Approved', 'Pending', 'System', 'Custom', 'Completion %'];
                $rows = [];

                foreach ($stats as $langStats) {
                    $rows[] = [
                        $langStats['language'],
                        $langStats['total_translations'],
                        $langStats['approved_translations'],
                        $langStats['pending_translations'],
                        $langStats['system_translations'],
                        $langStats['custom_translations'],
                        $langStats['completion_percentage'] . '%',
                    ];
                }

                $this->table($headers, $rows);
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("Stats failed: " . $e->getMessage());
            return 1;
        }
    }

    /**
     * Create a new language
     */
    protected function createLanguage()
    {
        $siteId = $this->option('site');
        $languageCode = $this->option('language');

        if (!$siteId) {
            $this->error('Site ID is required');
            return 1;
        }

        if (!$languageCode) {
            $languageCode = $this->ask('Language code (e.g., en, es, fr)');
        }

        $availableLanguages = Language::getAvailableLanguages();
        
        if (!isset($availableLanguages[$languageCode])) {
            $this->error("Language code '{$languageCode}' is not supported");
            $this->info('Available languages: ' . implode(', ', array_keys($availableLanguages)));
            return 1;
        }

        try {
            $languageData = $availableLanguages[$languageCode];
            $languageData['code'] = $languageCode;
            $languageData['is_active'] = $this->confirm('Activate this language?', true);
            $languageData['is_default'] = $this->confirm('Set as default language?', false);

            $language = $this->i18nService->createLanguage($siteId, $languageData);

            $this->info("Language created successfully:");
            $this->line("  ID: {$language->language_id}");
            $this->line("  Code: {$language->code}");
            $this->line("  Name: {$language->name}");
            $this->line("  Native Name: {$language->native_name}");
            $this->line("  Active: " . ($language->is_active ? 'Yes' : 'No'));
            $this->line("  Default: " . ($language->is_default ? 'Yes' : 'No'));

            return 0;

        } catch (\Exception $e) {
            $this->error("Language creation failed: " . $e->getMessage());
            return 1;
        }
    }
}

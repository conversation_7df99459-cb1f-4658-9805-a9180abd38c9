<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class CustomComponent extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'st_custom_components';

    protected $fillable = [
        'component_id',
        'site_id',
        'name',
        'type',
        'description',
        'is_active',
        'position',
        'order_index',
        'settings',
        'styles',
        'content',
        'conditions',
        'metadata',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'order_index' => 'integer',
        'settings' => 'array',
        'styles' => 'array',
        'conditions' => 'array',
        'metadata' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($component) {
            if (empty($component->component_id)) {
                $component->component_id = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the site that owns the component
     */
    public function site()
    {
        return $this->belongsTo(Site::class);
    }

    /**
     * Scope to get active components
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get components by position
     */
    public function scopeByPosition($query, $position)
    {
        return $query->where('position', $position);
    }

    /**
     * Scope to get components by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Get available component types
     */
    public static function getAvailableTypes()
    {
        return [
            'banner' => [
                'name' => 'Banner',
                'description' => 'Display promotional banners or announcements',
                'positions' => ['header', 'footer', 'chat_header', 'chat_footer'],
                'settings' => [
                    'text' => 'string',
                    'link_url' => 'string',
                    'background_color' => 'color',
                    'text_color' => 'color',
                    'show_close_button' => 'boolean',
                    'auto_hide_delay' => 'integer',
                ],
            ],
            'custom_html' => [
                'name' => 'Custom HTML',
                'description' => 'Insert custom HTML content',
                'positions' => ['header', 'footer', 'chat_header', 'chat_footer', 'sidebar'],
                'settings' => [
                    'html_content' => 'html',
                    'css_classes' => 'string',
                    'inline_styles' => 'string',
                ],
            ],
            'contact_form' => [
                'name' => 'Contact Form',
                'description' => 'Pre-chat contact form',
                'positions' => ['chat_header', 'pre_chat'],
                'settings' => [
                    'required_fields' => 'array',
                    'optional_fields' => 'array',
                    'submit_button_text' => 'string',
                    'success_message' => 'string',
                ],
            ],
            'rating_widget' => [
                'name' => 'Rating Widget',
                'description' => 'Customer satisfaction rating',
                'positions' => ['chat_footer', 'post_chat'],
                'settings' => [
                    'rating_type' => 'select', // stars, thumbs, numbers
                    'max_rating' => 'integer',
                    'show_comment_field' => 'boolean',
                    'required_rating' => 'boolean',
                ],
            ],
            'quick_replies' => [
                'name' => 'Quick Replies',
                'description' => 'Predefined quick reply buttons',
                'positions' => ['chat_input', 'chat_footer'],
                'settings' => [
                    'replies' => 'array',
                    'max_visible' => 'integer',
                    'show_more_button' => 'boolean',
                ],
            ],
            'file_upload' => [
                'name' => 'File Upload',
                'description' => 'File upload widget',
                'positions' => ['chat_input', 'chat_footer'],
                'settings' => [
                    'allowed_types' => 'array',
                    'max_file_size' => 'integer',
                    'max_files' => 'integer',
                    'upload_button_text' => 'string',
                ],
            ],
            'typing_indicator' => [
                'name' => 'Typing Indicator',
                'description' => 'Show when agent is typing',
                'positions' => ['chat_messages'],
                'settings' => [
                    'animation_type' => 'select', // dots, bars, pulse
                    'text' => 'string',
                    'show_avatar' => 'boolean',
                ],
            ],
            'social_links' => [
                'name' => 'Social Links',
                'description' => 'Social media links',
                'positions' => ['header', 'footer', 'chat_footer'],
                'settings' => [
                    'links' => 'array',
                    'icon_style' => 'select', // filled, outlined, colored
                    'size' => 'select', // small, medium, large
                ],
            ],
        ];
    }

    /**
     * Get available positions
     */
    public static function getAvailablePositions()
    {
        return [
            'header' => 'Page Header',
            'footer' => 'Page Footer',
            'sidebar' => 'Sidebar',
            'chat_header' => 'Chat Header',
            'chat_footer' => 'Chat Footer',
            'chat_input' => 'Chat Input Area',
            'chat_messages' => 'Chat Messages Area',
            'pre_chat' => 'Pre-chat Screen',
            'post_chat' => 'Post-chat Screen',
            'offline' => 'Offline Screen',
        ];
    }

    /**
     * Check if component should be displayed based on conditions
     */
    public function shouldDisplay($context = [])
    {
        if (!$this->is_active) {
            return false;
        }

        if (empty($this->conditions)) {
            return true;
        }

        foreach ($this->conditions as $condition) {
            if (!$this->evaluateCondition($condition, $context)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Evaluate a single condition
     */
    protected function evaluateCondition($condition, $context)
    {
        $field = $condition['field'] ?? '';
        $operator = $condition['operator'] ?? '=';
        $value = $condition['value'] ?? '';

        $contextValue = data_get($context, $field);

        switch ($operator) {
            case '=':
            case 'equals':
                return $contextValue == $value;
            case '!=':
            case 'not_equals':
                return $contextValue != $value;
            case '>':
            case 'greater_than':
                return $contextValue > $value;
            case '<':
            case 'less_than':
                return $contextValue < $value;
            case '>=':
            case 'greater_than_or_equal':
                return $contextValue >= $value;
            case '<=':
            case 'less_than_or_equal':
                return $contextValue <= $value;
            case 'contains':
                return str_contains($contextValue, $value);
            case 'not_contains':
                return !str_contains($contextValue, $value);
            case 'starts_with':
                return str_starts_with($contextValue, $value);
            case 'ends_with':
                return str_ends_with($contextValue, $value);
            case 'in':
                return in_array($contextValue, (array) $value);
            case 'not_in':
                return !in_array($contextValue, (array) $value);
            case 'regex':
                return preg_match($value, $contextValue);
            default:
                return true;
        }
    }

    /**
     * Render component HTML
     */
    public function render($context = [])
    {
        if (!$this->shouldDisplay($context)) {
            return '';
        }

        $viewName = "components.{$this->type}";
        
        if (!view()->exists($viewName)) {
            $viewName = 'components.default';
        }

        return view($viewName, [
            'component' => $this,
            'settings' => $this->settings ?? [],
            'styles' => $this->styles ?? [],
            'content' => $this->content,
            'context' => $context,
        ])->render();
    }

    /**
     * Generate component CSS
     */
    public function generateCss()
    {
        $css = '';
        $selector = ".custom-component-{$this->component_id}";

        if ($this->styles) {
            $css .= "{$selector} {\n";
            
            foreach ($this->styles as $property => $value) {
                $cssProperty = str_replace('_', '-', $property);
                $css .= "  {$cssProperty}: {$value};\n";
            }
            
            $css .= "}\n";
        }

        return $css;
    }

    /**
     * Clone component
     */
    public function cloneComponent($newName = null)
    {
        $clone = $this->replicate();
        $clone->component_id = (string) Str::uuid();
        $clone->name = $newName ?? $this->name . ' (Copy)';
        $clone->is_active = false;
        $clone->save();

        return $clone;
    }
}

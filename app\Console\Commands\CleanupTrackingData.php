<?php

namespace App\Console\Commands;

use App\Models\VisitorTracking;
use App\Models\MouseTracking;
use App\Models\ClickEvent;
use App\Models\FormEvent;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class CleanupTrackingData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cleanup:tracking-data 
                            {--days=90 : Number of days to keep tracking data}
                            {--batch-size=1000 : Number of records to process in each batch}
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean up old visitor tracking data to maintain database performance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $batchSize = (int) $this->option('batch-size');
        $dryRun = $this->option('dry-run');
        
        $cutoffDate = Carbon::now()->subDays($days);
        
        $this->info("Cleaning up tracking data older than {$days} days (before {$cutoffDate->toDateString()})");
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No data will be actually deleted');
        }
        
        // Clean up visitor tracking data
        $this->cleanupVisitorTracking($cutoffDate, $batchSize, $dryRun);
        
        // Clean up mouse tracking data
        $this->cleanupMouseTracking($cutoffDate, $batchSize, $dryRun);
        
        // Clean up click events
        $this->cleanupClickEvents($cutoffDate, $batchSize, $dryRun);
        
        // Clean up form events
        $this->cleanupFormEvents($cutoffDate, $batchSize, $dryRun);
        
        $this->info('Tracking data cleanup completed!');
    }

    /**
     * Clean up visitor tracking data
     */
    protected function cleanupVisitorTracking(Carbon $cutoffDate, int $batchSize, bool $dryRun)
    {
        $this->info('Cleaning up visitor tracking data...');
        
        $query = VisitorTracking::where('created_at', '<', $cutoffDate);
        $totalCount = $query->count();
        
        if ($totalCount === 0) {
            $this->info('No visitor tracking data to clean up.');
            return;
        }
        
        $this->info("Found {$totalCount} visitor tracking records to clean up.");
        
        if ($dryRun) {
            return;
        }
        
        $deletedCount = 0;
        $progressBar = $this->output->createProgressBar($totalCount);
        $progressBar->start();
        
        do {
            $records = VisitorTracking::where('created_at', '<', $cutoffDate)
                ->limit($batchSize)
                ->get();
            
            if ($records->isEmpty()) {
                break;
            }
            
            $batchIds = $records->pluck('id')->toArray();
            
            // Delete related records first
            MouseTracking::whereIn('visitor_tracking_id', $batchIds)->delete();
            ClickEvent::whereIn('visitor_tracking_id', $batchIds)->delete();
            FormEvent::whereIn('visitor_tracking_id', $batchIds)->delete();
            
            // Delete visitor tracking records
            $deleted = VisitorTracking::whereIn('id', $batchIds)->delete();
            $deletedCount += $deleted;
            
            $progressBar->advance($deleted);
            
        } while ($records->count() === $batchSize);
        
        $progressBar->finish();
        $this->newLine();
        $this->info("Deleted {$deletedCount} visitor tracking records.");
    }

    /**
     * Clean up mouse tracking data
     */
    protected function cleanupMouseTracking(Carbon $cutoffDate, int $batchSize, bool $dryRun)
    {
        $this->info('Cleaning up mouse tracking data...');
        
        $query = MouseTracking::where('created_at', '<', $cutoffDate);
        $totalCount = $query->count();
        
        if ($totalCount === 0) {
            $this->info('No mouse tracking data to clean up.');
            return;
        }
        
        $this->info("Found {$totalCount} mouse tracking records to clean up.");
        
        if ($dryRun) {
            return;
        }
        
        $deletedCount = 0;
        $progressBar = $this->output->createProgressBar($totalCount);
        $progressBar->start();
        
        do {
            $deleted = MouseTracking::where('created_at', '<', $cutoffDate)
                ->limit($batchSize)
                ->delete();
            
            $deletedCount += $deleted;
            $progressBar->advance($deleted);
            
        } while ($deleted === $batchSize);
        
        $progressBar->finish();
        $this->newLine();
        $this->info("Deleted {$deletedCount} mouse tracking records.");
    }

    /**
     * Clean up click events
     */
    protected function cleanupClickEvents(Carbon $cutoffDate, int $batchSize, bool $dryRun)
    {
        $this->info('Cleaning up click events...');
        
        $query = ClickEvent::where('created_at', '<', $cutoffDate);
        $totalCount = $query->count();
        
        if ($totalCount === 0) {
            $this->info('No click events to clean up.');
            return;
        }
        
        $this->info("Found {$totalCount} click event records to clean up.");
        
        if ($dryRun) {
            return;
        }
        
        $deletedCount = 0;
        $progressBar = $this->output->createProgressBar($totalCount);
        $progressBar->start();
        
        do {
            $deleted = ClickEvent::where('created_at', '<', $cutoffDate)
                ->limit($batchSize)
                ->delete();
            
            $deletedCount += $deleted;
            $progressBar->advance($deleted);
            
        } while ($deleted === $batchSize);
        
        $progressBar->finish();
        $this->newLine();
        $this->info("Deleted {$deletedCount} click event records.");
    }

    /**
     * Clean up form events
     */
    protected function cleanupFormEvents(Carbon $cutoffDate, int $batchSize, bool $dryRun)
    {
        $this->info('Cleaning up form events...');
        
        $query = FormEvent::where('created_at', '<', $cutoffDate);
        $totalCount = $query->count();
        
        if ($totalCount === 0) {
            $this->info('No form events to clean up.');
            return;
        }
        
        $this->info("Found {$totalCount} form event records to clean up.");
        
        if ($dryRun) {
            return;
        }
        
        $deletedCount = 0;
        $progressBar = $this->output->createProgressBar($totalCount);
        $progressBar->start();
        
        do {
            $deleted = FormEvent::where('created_at', '<', $cutoffDate)
                ->limit($batchSize)
                ->delete();
            
            $deletedCount += $deleted;
            $progressBar->advance($deleted);
            
        } while ($deleted === $batchSize);
        
        $progressBar->finish();
        $this->newLine();
        $this->info("Deleted {$deletedCount} form event records.");
    }

    /**
     * Get database size information
     */
    protected function getDatabaseSizeInfo()
    {
        try {
            $tables = [
                'st_visitor_trackings',
                'st_mouse_trackings', 
                'st_click_events',
                'st_form_events'
            ];
            
            $this->info('Database size information:');
            
            foreach ($tables as $table) {
                $count = DB::table($table)->count();
                $size = DB::select("
                    SELECT 
                        ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE() 
                    AND table_name = ?
                ", [$table]);
                
                $sizeMb = $size[0]->size_mb ?? 0;
                $this->info("  {$table}: {$count} records, {$sizeMb} MB");
            }
            
        } catch (\Exception $e) {
            $this->warn('Could not retrieve database size information: ' . $e->getMessage());
        }
    }
}

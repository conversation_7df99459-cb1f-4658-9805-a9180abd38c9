<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Messages\BroadcastMessage;
use Illuminate\Notifications\Notification;

class ChatNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $data;

    /**
     * Create a new notification instance.
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * Get the notification's delivery channels.
     */
    public function via($notifiable): array
    {
        $channels = ['database', 'broadcast'];

        // Add email channel for important chat notifications
        if (($notifiable->email_notifications ?? true) && 
            in_array($this->data['priority'] ?? 'medium', ['high', 'urgent'])) {
            $channels[] = 'mail';
        }

        return $channels;
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject($this->data['title'])
            ->greeting('Hello ' . $notifiable->name . '!')
            ->line($this->data['message'])
            ->when(isset($this->data['session_url']), function ($mail) {
                return $mail->action('View Chat Session', $this->data['session_url']);
            })
            ->line('Please respond as soon as possible.');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray($notifiable): array
    {
        return [
            'id' => $this->id,
            'type' => 'chat',
            'title' => $this->data['title'],
            'message' => $this->data['message'],
            'priority' => $this->data['priority'] ?? 'medium',
            'session_id' => $this->data['session_id'] ?? null,
            'visitor_name' => $this->data['visitor_name'] ?? null,
            'data' => $this->data['data'] ?? [],
            'sender_id' => $this->data['sender_id'] ?? null,
            'created_at' => now()->toISOString()
        ];
    }

    /**
     * Get the broadcastable representation of the notification.
     */
    public function toBroadcast($notifiable): BroadcastMessage
    {
        return new BroadcastMessage([
            'id' => $this->id,
            'type' => 'chat',
            'title' => $this->data['title'],
            'message' => $this->data['message'],
            'priority' => $this->data['priority'] ?? 'medium',
            'session_id' => $this->data['session_id'] ?? null,
            'visitor_name' => $this->data['visitor_name'] ?? null,
            'data' => $this->data['data'] ?? [],
            'created_at' => now()->toISOString()
        ]);
    }

    /**
     * Get the channels the event should broadcast on.
     */
    public function broadcastOn(): array
    {
        return ['user.' . $this->notifiable->id];
    }

    /**
     * The event's broadcast name.
     */
    public function broadcastAs(): string
    {
        return 'notification.chat';
    }
}

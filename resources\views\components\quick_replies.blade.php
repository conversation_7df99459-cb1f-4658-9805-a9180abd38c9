<div class="custom-component custom-component-{{ $component->component_id }} quick-replies-component" 
     data-component-id="{{ $component->component_id }}"
     data-component-type="{{ $component->type }}">
    
    <div class="quick-replies-container">
        @php
            $replies = $settings['replies'] ?? [];
            $maxVisible = $settings['max_visible'] ?? 3;
            $showMoreButton = $settings['show_more_button'] ?? true;
            $visibleReplies = array_slice($replies, 0, $maxVisible);
            $hiddenReplies = array_slice($replies, $maxVisible);
        @endphp
        
        <div class="quick-replies-visible">
            @foreach($visibleReplies as $index => $reply)
            <button class="quick-reply-button" 
                    data-reply="{{ $reply }}"
                    onclick="selectQuickReply('{{ $component->component_id }}', '{{ addslashes($reply) }}')">
                {{ $reply }}
            </button>
            @endforeach
        </div>
        
        @if($showMoreButton && count($hiddenReplies) > 0)
        <div class="quick-replies-hidden" style="display: none;">
            @foreach($hiddenReplies as $index => $reply)
            <button class="quick-reply-button" 
                    data-reply="{{ $reply }}"
                    onclick="selectQuickReply('{{ $component->component_id }}', '{{ addslashes($reply) }}')">
                {{ $reply }}
            </button>
            @endforeach
        </div>
        
        <button class="quick-replies-toggle" 
                onclick="toggleQuickReplies('{{ $component->component_id }}')">
            <span class="toggle-text-more">Show More</span>
            <span class="toggle-text-less" style="display: none;">Show Less</span>
        </button>
        @endif
    </div>
</div>

<style>
.custom-component-{{ $component->component_id }} {
    padding: 12px;
    @if($styles)
        @foreach($styles as $property => $value)
        {{ str_replace('_', '-', $property) }}: {{ $value }};
        @endforeach
    @endif
}

.custom-component-{{ $component->component_id }} .quick-replies-container {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.custom-component-{{ $component->component_id }} .quick-replies-visible,
.custom-component-{{ $component->component_id }} .quick-replies-hidden {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.custom-component-{{ $component->component_id }} .quick-reply-button {
    background: #F3F4F6;
    border: 1px solid #E5E7EB;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 14px;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.custom-component-{{ $component->component_id }} .quick-reply-button:hover {
    background: #E5E7EB;
    border-color: #D1D5DB;
}

.custom-component-{{ $component->component_id }} .quick-reply-button:active {
    background: #3B82F6;
    color: white;
    border-color: #3B82F6;
}

.custom-component-{{ $component->component_id }} .quick-replies-toggle {
    background: none;
    border: none;
    color: #3B82F6;
    font-size: 12px;
    cursor: pointer;
    padding: 4px 8px;
    text-decoration: underline;
    align-self: flex-start;
}

.custom-component-{{ $component->component_id }} .quick-replies-toggle:hover {
    color: #2563EB;
}
</style>

<script>
function selectQuickReply(componentId, reply) {
    // Find the chat input field and set the reply text
    const chatInput = document.querySelector('.chat-input, input[type="text"]');
    if (chatInput) {
        chatInput.value = reply;
        chatInput.focus();
    }
    
    // Trigger custom event for integration
    const event = new CustomEvent('quickReplySelected', {
        detail: { componentId, reply }
    });
    document.dispatchEvent(event);
}

function toggleQuickReplies(componentId) {
    const component = document.querySelector('.custom-component-' + componentId);
    const hiddenReplies = component.querySelector('.quick-replies-hidden');
    const toggleButton = component.querySelector('.quick-replies-toggle');
    const moreText = toggleButton.querySelector('.toggle-text-more');
    const lessText = toggleButton.querySelector('.toggle-text-less');
    
    if (hiddenReplies.style.display === 'none') {
        hiddenReplies.style.display = 'flex';
        moreText.style.display = 'none';
        lessText.style.display = 'inline';
    } else {
        hiddenReplies.style.display = 'none';
        moreText.style.display = 'inline';
        lessText.style.display = 'none';
    }
}
</script>

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\WebhookLog;
use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\VisitorBehavior;
use App\Events\WebhookReceived;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class WebhookController extends Controller
{
    /**
     * Handle chat message webhook
     */
    public function handleChatMessage(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event' => 'required|string',
            'session_id' => 'required|string',
            'message' => 'required|array',
            'timestamp' => 'required|date',
            'signature' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->logAndRespond('chat_message', $request->all(), false, 'Validation failed', $validator->errors());
        }

        // Verify webhook signature if provided
        if (!$this->verifySignature($request)) {
            return $this->logAndRespond('chat_message', $request->all(), false, 'Invalid signature');
        }

        try {
            $session = ChatSession::where('session_id', $request->session_id)->first();
            
            if (!$session) {
                return $this->logAndRespond('chat_message', $request->all(), false, 'Session not found');
            }

            $messageData = $request->message;
            
            // Create message from webhook data
            $message = ChatMessage::create([
                'session_id' => $session->id,
                'message_id' => $messageData['id'] ?? Str::uuid(),
                'sender_type' => $messageData['sender_type'] ?? 'external',
                'sender_name' => $messageData['sender_name'] ?? 'External System',
                'message' => $messageData['content'] ?? $messageData['message'],
                'file_url' => $messageData['file_url'] ?? null,
                'file_name' => $messageData['file_name'] ?? null,
                'file_size' => $messageData['file_size'] ?? null,
                'sent_at' => $request->timestamp,
                'is_internal' => $messageData['is_internal'] ?? false
            ]);

            // Broadcast the message
            broadcast(new \App\Events\MessageSent($message, $session));

            return $this->logAndRespond('chat_message', $request->all(), true, 'Message processed successfully', [
                'message_id' => $message->message_id
            ]);

        } catch (\Exception $e) {
            Log::error('Webhook chat message error: ' . $e->getMessage());
            return $this->logAndRespond('chat_message', $request->all(), false, 'Processing error: ' . $e->getMessage());
        }
    }

    /**
     * Handle chat session webhook
     */
    public function handleChatSession(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'event' => 'required|in:session_started,session_ended,session_transferred,agent_joined,agent_left',
            'session_id' => 'required|string',
            'data' => 'required|array',
            'timestamp' => 'required|date',
            'signature' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->logAndRespond('chat_session', $request->all(), false, 'Validation failed', $validator->errors());
        }

        if (!$this->verifySignature($request)) {
            return $this->logAndRespond('chat_session', $request->all(), false, 'Invalid signature');
        }

        try {
            $session = ChatSession::where('session_id', $request->session_id)->first();
            
            if (!$session) {
                return $this->logAndRespond('chat_session', $request->all(), false, 'Session not found');
            }

            $eventData = $request->data;
            $event = $request->event;

            switch ($event) {
                case 'session_started':
                    $this->handleSessionStarted($session, $eventData);
                    break;
                    
                case 'session_ended':
                    $this->handleSessionEnded($session, $eventData);
                    break;
                    
                case 'session_transferred':
                    $this->handleSessionTransferred($session, $eventData);
                    break;
                    
                case 'agent_joined':
                    $this->handleAgentJoined($session, $eventData);
                    break;
                    
                case 'agent_left':
                    $this->handleAgentLeft($session, $eventData);
                    break;
            }

            return $this->logAndRespond('chat_session', $request->all(), true, 'Session event processed successfully');

        } catch (\Exception $e) {
            Log::error('Webhook chat session error: ' . $e->getMessage());
            return $this->logAndRespond('chat_session', $request->all(), false, 'Processing error: ' . $e->getMessage());
        }
    }

    /**
     * Handle visitor behavior webhook
     */
    public function handleVisitorBehavior(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'visitor_id' => 'required|string',
            'behaviors' => 'required|array',
            'behaviors.*.event_type' => 'required|string',
            'behaviors.*.page_url' => 'required|url',
            'behaviors.*.timestamp' => 'required|date',
            'signature' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->logAndRespond('visitor_behavior', $request->all(), false, 'Validation failed', $validator->errors());
        }

        if (!$this->verifySignature($request)) {
            return $this->logAndRespond('visitor_behavior', $request->all(), false, 'Invalid signature');
        }

        try {
            $visitor = \App\Models\Visitor::where('visitor_id', $request->visitor_id)->first();
            
            if (!$visitor) {
                return $this->logAndRespond('visitor_behavior', $request->all(), false, 'Visitor not found');
            }

            $processedCount = 0;
            
            foreach ($request->behaviors as $behaviorData) {
                VisitorBehavior::create([
                    'visitor_id' => $visitor->id,
                    'event_type' => $behaviorData['event_type'],
                    'page_url' => $behaviorData['page_url'],
                    'element_selector' => $behaviorData['element_selector'] ?? null,
                    'element_text' => $behaviorData['element_text'] ?? null,
                    'coordinates_x' => $behaviorData['coordinates']['x'] ?? null,
                    'coordinates_y' => $behaviorData['coordinates']['y'] ?? null,
                    'scroll_depth' => $behaviorData['scroll_depth'] ?? null,
                    'time_spent' => $behaviorData['time_spent'] ?? null,
                    'form_field' => $behaviorData['form_field'] ?? null,
                    'additional_data' => isset($behaviorData['additional_data']) ? json_encode($behaviorData['additional_data']) : null,
                    'created_at' => $behaviorData['timestamp']
                ]);
                
                $processedCount++;
            }

            return $this->logAndRespond('visitor_behavior', $request->all(), true, 'Visitor behaviors processed successfully', [
                'processed_count' => $processedCount
            ]);

        } catch (\Exception $e) {
            Log::error('Webhook visitor behavior error: ' . $e->getMessage());
            return $this->logAndRespond('visitor_behavior', $request->all(), false, 'Processing error: ' . $e->getMessage());
        }
    }

    /**
     * Handle system notification webhook
     */
    public function handleSystemNotification(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|string',
            'title' => 'required|string',
            'message' => 'required|string',
            'priority' => 'nullable|in:low,medium,high,urgent',
            'recipients' => 'nullable|array',
            'data' => 'nullable|array',
            'signature' => 'nullable|string'
        ]);

        if ($validator->fails()) {
            return $this->logAndRespond('system_notification', $request->all(), false, 'Validation failed', $validator->errors());
        }

        if (!$this->verifySignature($request)) {
            return $this->logAndRespond('system_notification', $request->all(), false, 'Invalid signature');
        }

        try {
            // Create system notification
            $notification = \App\Models\SystemNotification::create([
                'type' => $request->type,
                'title' => $request->title,
                'message' => $request->message,
                'priority' => $request->priority ?? 'medium',
                'data' => $request->data ? json_encode($request->data) : null,
                'created_at' => now()
            ]);

            // Send to specific recipients or broadcast to all admins
            if ($request->recipients) {
                foreach ($request->recipients as $userId) {
                    $user = \App\Models\User::find($userId);
                    if ($user) {
                        $user->notify(new \App\Notifications\SystemNotification($notification));
                    }
                }
            } else {
                // Broadcast to all admins and supervisors
                $admins = \App\Models\User::whereIn('role', ['admin', 'supervisor'])
                    ->where('is_active', true)
                    ->get();
                    
                foreach ($admins as $admin) {
                    $admin->notify(new \App\Notifications\SystemNotification($notification));
                }
            }

            return $this->logAndRespond('system_notification', $request->all(), true, 'System notification processed successfully', [
                'notification_id' => $notification->id
            ]);

        } catch (\Exception $e) {
            Log::error('Webhook system notification error: ' . $e->getMessage());
            return $this->logAndRespond('system_notification', $request->all(), false, 'Processing error: ' . $e->getMessage());
        }
    }

    /**
     * Verify webhook signature
     */
    private function verifySignature(Request $request): bool
    {
        $signature = $request->header('X-Webhook-Signature') ?? $request->signature;
        
        if (!$signature) {
            return true; // No signature required
        }

        $webhookSecret = config('app.webhook_secret');
        if (!$webhookSecret) {
            return true; // No secret configured
        }

        $payload = $request->getContent();
        $expectedSignature = 'sha256=' . hash_hmac('sha256', $payload, $webhookSecret);

        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Log webhook and return response
     */
    private function logAndRespond(string $type, array $payload, bool $success, string $message, $data = null): JsonResponse
    {
        // Log webhook
        WebhookLog::create([
            'type' => $type,
            'payload' => json_encode($payload),
            'success' => $success,
            'response_message' => $message,
            'response_data' => $data ? json_encode($data) : null,
            'processed_at' => now()
        ]);

        // Broadcast webhook received event
        broadcast(new WebhookReceived($type, $success, $message));

        $response = [
            'success' => $success,
            'message' => $message
        ];

        if ($data) {
            $response['data'] = $data;
        }

        return response()->json($response, $success ? 200 : 400);
    }

    /**
     * Handle session started event
     */
    private function handleSessionStarted(ChatSession $session, array $data): void
    {
        $session->update([
            'status' => 'active',
            'started_at' => $data['started_at'] ?? now()
        ]);

        broadcast(new \App\Events\SessionStarted($session));
    }

    /**
     * Handle session ended event
     */
    private function handleSessionEnded(ChatSession $session, array $data): void
    {
        $session->update([
            'status' => 'ended',
            'ended_at' => $data['ended_at'] ?? now(),
            'ended_by' => $data['ended_by'] ?? 'external'
        ]);

        broadcast(new \App\Events\SessionEnded($session));
    }

    /**
     * Handle session transferred event
     */
    private function handleSessionTransferred(ChatSession $session, array $data): void
    {
        $session->update([
            'agent_id' => $data['new_agent_id'] ?? null,
            'agent_name' => $data['new_agent_name'] ?? null,
            'transferred_at' => $data['transferred_at'] ?? now(),
            'transfer_reason' => $data['reason'] ?? null
        ]);

        broadcast(new \App\Events\SessionTransferred($session));
    }

    /**
     * Handle agent joined event
     */
    private function handleAgentJoined(ChatSession $session, array $data): void
    {
        $session->update([
            'agent_id' => $data['agent_id'] ?? null,
            'agent_name' => $data['agent_name'] ?? null,
            'status' => 'active'
        ]);

        // Create system message
        ChatMessage::create([
            'session_id' => $session->id,
            'message_id' => Str::uuid(),
            'sender_type' => 'system',
            'sender_name' => 'System',
            'message' => ($data['agent_name'] ?? 'Agent') . ' has joined the chat',
            'sent_at' => now()
        ]);
    }

    /**
     * Handle agent left event
     */
    private function handleAgentLeft(ChatSession $session, array $data): void
    {
        // Create system message
        ChatMessage::create([
            'session_id' => $session->id,
            'message_id' => Str::uuid(),
            'sender_type' => 'system',
            'sender_name' => 'System',
            'message' => ($data['agent_name'] ?? 'Agent') . ' has left the chat',
            'sent_at' => now()
        ]);
    }
}

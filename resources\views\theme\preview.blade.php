<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Preview - {{ $theme->name }}</title>
    <style>
        :root {
            @foreach($colorScheme as $key => $value)
            --color-{{ $key }}: {{ $value }};
            @endforeach
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: {{ $typography['font_family'] ?? 'Inter, system-ui, sans-serif' }};
            font-size: {{ $typography['font_size_base'] ?? '14px' }};
            line-height: {{ $typography['line_height'] ?? '1.5' }};
            background-color: var(--color-background);
            color: var(--color-text-primary);
            padding: 20px;
        }

        .preview-container {
            max-width: 800px;
            margin: 0 auto;
            background: var(--color-surface);
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .preview-header {
            text-align: center;
            margin-bottom: 32px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--color-border);
        }

        .preview-title {
            font-size: {{ $typography['font_size_heading'] ?? '18px' }};
            font-weight: {{ $typography['font_weight_bold'] ?? '600' }};
            color: var(--color-text-primary);
            margin-bottom: 8px;
        }

        .preview-description {
            color: var(--color-text-secondary);
            font-size: {{ $typography['font_size_small'] ?? '12px' }};
        }

        .chat-widget-preview {
            position: relative;
            width: {{ $layoutSettings['chat_width'] ?? '350px' }};
            height: {{ $layoutSettings['chat_height'] ?? '500px' }};
            margin: 0 auto;
            border-radius: {{ $layoutSettings['border_radius'] ?? '12px' }};
            box-shadow: {{ $layoutSettings['shadow'] ?? '0 10px 25px rgba(0, 0, 0, 0.1)' }};
            background: var(--color-background);
            overflow: hidden;
            border: 1px solid var(--color-border);
        }

        .chat-header {
            height: {{ $layoutSettings['header_height'] ?? '60px' }};
            background: var(--color-primary);
            color: white;
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-weight: {{ $typography['font_weight_medium'] ?? '500' }};
        }

        .chat-messages {
            flex: 1;
            padding: {{ $layoutSettings['padding'] ?? '16px' }};
            background: var(--color-background);
            overflow-y: auto;
            height: calc(100% - {{ $layoutSettings['header_height'] ?? '60px' }} - {{ $layoutSettings['footer_height'] ?? '50px' }});
        }

        .message {
            margin: {{ $layoutSettings['message_spacing'] ?? '8px' }} 0;
            display: flex;
        }

        .message.visitor {
            justify-content: flex-end;
        }

        .message.agent {
            justify-content: flex-start;
        }

        .message-bubble {
            max-width: {{ $chatBubbleStyles['visitor']['max_width'] ?? '80%' }};
            padding: {{ $chatBubbleStyles['visitor']['padding'] ?? '12px 16px' }};
            word-wrap: break-word;
        }

        .message.visitor .message-bubble {
            background: {{ $chatBubbleStyles['visitor']['background'] ?? 'var(--color-primary)' }};
            color: {{ $chatBubbleStyles['visitor']['color'] ?? '#FFFFFF' }};
            border-radius: {{ $chatBubbleStyles['visitor']['border_radius'] ?? '18px 18px 4px 18px' }};
        }

        .message.agent .message-bubble {
            background: {{ $chatBubbleStyles['agent']['background'] ?? '#F3F4F6' }};
            color: {{ $chatBubbleStyles['agent']['color'] ?? '#111827' }};
            border-radius: {{ $chatBubbleStyles['agent']['border_radius'] ?? '18px 18px 18px 4px' }};
        }

        .chat-footer {
            height: {{ $layoutSettings['footer_height'] ?? '50px' }};
            background: var(--color-surface);
            border-top: 1px solid var(--color-border);
            display: flex;
            align-items: center;
            padding: 0 16px;
            gap: 8px;
        }

        .chat-input {
            flex: 1;
            border: 1px solid var(--color-border);
            border-radius: 20px;
            padding: 8px 16px;
            font-size: {{ $typography['font_size_base'] ?? '14px' }};
            background: var(--color-background);
            color: var(--color-text-primary);
        }

        .send-button {
            background: {{ $buttonStyles['primary']['background'] ?? 'var(--color-primary)' }};
            color: {{ $buttonStyles['primary']['color'] ?? '#FFFFFF' }};
            border: {{ $buttonStyles['primary']['border'] ?? 'none' }};
            border-radius: {{ $buttonStyles['primary']['border_radius'] ?? '6px' }};
            padding: {{ $buttonStyles['primary']['padding'] ?? '8px 16px' }};
            font-weight: {{ $buttonStyles['primary']['font_weight'] ?? '500' }};
            cursor: pointer;
            font-size: {{ $typography['font_size_base'] ?? '14px' }};
        }

        .send-button:hover {
            background: {{ $buttonStyles['primary']['hover_background'] ?? 'var(--color-primary)' }};
            opacity: 0.9;
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 16px;
            margin-top: 32px;
        }

        .color-item {
            text-align: center;
        }

        .color-swatch {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            margin: 0 auto 8px;
            border: 2px solid var(--color-border);
        }

        .color-name {
            font-size: {{ $typography['font_size_small'] ?? '12px' }};
            color: var(--color-text-secondary);
            text-transform: capitalize;
        }

        .color-value {
            font-size: {{ $typography['font_size_small'] ?? '12px' }};
            color: var(--color-text-primary);
            font-family: monospace;
            margin-top: 4px;
        }

        .typography-sample {
            margin-top: 32px;
            padding: 24px;
            background: var(--color-surface);
            border-radius: 8px;
            border: 1px solid var(--color-border);
        }

        .typography-sample h1 {
            font-size: {{ $typography['font_size_heading'] ?? '18px' }};
            font-weight: {{ $typography['font_weight_bold'] ?? '600' }};
            margin-bottom: 16px;
            color: var(--color-text-primary);
        }

        .typography-sample p {
            font-size: {{ $typography['font_size_base'] ?? '14px' }};
            font-weight: {{ $typography['font_weight_normal'] ?? '400' }};
            color: var(--color-text-secondary);
            margin-bottom: 12px;
        }

        .typography-sample .small-text {
            font-size: {{ $typography['font_size_small'] ?? '12px' }};
            color: var(--color-text-secondary);
        }

        @media (max-width: 768px) {
            .chat-widget-preview {
                width: 100%;
                max-width: 400px;
            }
            
            .color-palette {
                grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-header">
            <h1 class="preview-title">{{ $theme->name }}</h1>
            <p class="preview-description">{{ $theme->description ?? 'Theme Preview' }}</p>
        </div>

        <div class="chat-widget-preview">
            <div class="chat-header">
                <span>Customer Support</span>
            </div>
            
            <div class="chat-messages">
                <div class="message agent">
                    <div class="message-bubble">
                        Hello! How can I help you today?
                    </div>
                </div>
                
                <div class="message visitor">
                    <div class="message-bubble">
                        Hi, I have a question about your services.
                    </div>
                </div>
                
                <div class="message agent">
                    <div class="message-bubble">
                        I'd be happy to help! What would you like to know?
                    </div>
                </div>
            </div>
            
            <div class="chat-footer">
                <input type="text" class="chat-input" placeholder="Type your message...">
                <button class="send-button">Send</button>
            </div>
        </div>

        <div class="color-palette">
            @foreach($colorScheme as $name => $value)
            <div class="color-item">
                <div class="color-swatch" style="background-color: {{ $value }};"></div>
                <div class="color-name">{{ str_replace('_', ' ', $name) }}</div>
                <div class="color-value">{{ $value }}</div>
            </div>
            @endforeach
        </div>

        <div class="typography-sample">
            <h1>Typography Sample</h1>
            <p>This is a sample paragraph showing the default text styling. The font family is {{ $typography['font_family'] ?? 'Inter, system-ui, sans-serif' }} with a base size of {{ $typography['font_size_base'] ?? '14px' }}.</p>
            <p class="small-text">This is smaller text used for secondary information and captions.</p>
        </div>
    </div>
</body>
</html>

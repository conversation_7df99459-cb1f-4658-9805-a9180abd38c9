/**
 * WebSocket Client for Chat System
 */
class ChatWebSocket {
    constructor(options = {}) {
        this.url = options.url || `ws://${window.location.hostname}:8080`;
        this.reconnectInterval = options.reconnectInterval || 5000;
        this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
        this.heartbeatInterval = options.heartbeatInterval || 30000;
        
        this.ws = null;
        this.reconnectAttempts = 0;
        this.isConnected = false;
        this.heartbeatTimer = null;
        this.messageQueue = [];
        this.eventHandlers = {};
        
        this.sessionId = null;
        this.siteId = null;
        this.userId = null;
        this.userType = null; // 'visitor' or 'agent'
        
        this.init();
    }

    init() {
        this.connect();
    }

    connect() {
        try {
            this.ws = new WebSocket(this.url);
            
            this.ws.onopen = (event) => {
                console.log('WebSocket connected');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.startHeartbeat();
                this.processMessageQueue();
                this.emit('connected', event);
            };

            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };

            this.ws.onclose = (event) => {
                console.log('WebSocket disconnected');
                this.isConnected = false;
                this.stopHeartbeat();
                this.emit('disconnected', event);
                
                if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnect();
                }
            };

            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.emit('error', error);
            };

        } catch (error) {
            console.error('Error creating WebSocket connection:', error);
            this.reconnect();
        }
    }

    reconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            this.emit('max_reconnect_attempts_reached');
            return;
        }

        this.reconnectAttempts++;
        console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        
        setTimeout(() => {
            this.connect();
        }, this.reconnectInterval);
    }

    send(data) {
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(data));
        } else {
            // Queue message for later sending
            this.messageQueue.push(data);
        }
    }

    processMessageQueue() {
        while (this.messageQueue.length > 0) {
            const message = this.messageQueue.shift();
            this.send(message);
        }
    }

    handleMessage(data) {
        console.log('Received message:', data);
        
        switch (data.type) {
            case 'connection':
                this.emit('connection', data);
                break;
                
            case 'auth_success':
                this.emit('auth_success', data);
                break;
                
            case 'visitor_joined':
                this.emit('visitor_joined', data);
                break;
                
            case 'agent_joined':
                this.emit('agent_joined', data);
                break;
                
            case 'new_message':
                this.emit('new_message', data);
                break;
                
            case 'typing_start':
                this.emit('typing_start', data);
                break;
                
            case 'typing_stop':
                this.emit('typing_stop', data);
                break;
                
            case 'agent_assigned':
                this.emit('agent_assigned', data);
                break;
                
            case 'session_assigned':
                this.emit('session_assigned', data);
                break;
                
            case 'agent_status_changed':
                this.emit('agent_status_changed', data);
                break;
                
            case 'new_chat_session':
                this.emit('new_chat_session', data);
                break;
                
            case 'heartbeat_response':
                // Handle heartbeat response
                break;
                
            case 'error':
                console.error('WebSocket error:', data.message);
                this.emit('error', data);
                break;
                
            default:
                console.warn('Unknown message type:', data.type);
                this.emit('unknown_message', data);
        }
    }

    // Authentication
    authenticate(token, userId = null) {
        this.userId = userId;
        this.send({
            type: 'auth',
            token: token,
            user_id: userId
        });
    }

    // Join as visitor
    joinAsVisitor(sessionId, siteId, visitorId) {
        this.sessionId = sessionId;
        this.siteId = siteId;
        this.userType = 'visitor';
        
        this.send({
            type: 'visitor_join',
            session_id: sessionId,
            site_id: siteId,
            visitor_id: visitorId
        });
    }

    // Join as agent
    joinAsAgent(userId, siteId) {
        this.userId = userId;
        this.siteId = siteId;
        this.userType = 'agent';
        
        this.send({
            type: 'agent_join',
            user_id: userId,
            site_id: siteId
        });
    }

    // Send chat message
    sendMessage(message, messageType = 'text', metadata = null) {
        this.send({
            type: 'chat_message',
            session_id: this.sessionId,
            message: message,
            message_type: messageType,
            sender_type: this.userType,
            user_id: this.userId,
            metadata: metadata
        });
    }

    // Typing indicators
    startTyping() {
        this.send({
            type: 'typing_start',
            session_id: this.sessionId,
            user_id: this.userId,
            sender_type: this.userType
        });
    }

    stopTyping() {
        this.send({
            type: 'typing_stop',
            session_id: this.sessionId,
            user_id: this.userId,
            sender_type: this.userType
        });
    }

    // Agent status
    updateAgentStatus(status) {
        if (this.userType === 'agent') {
            this.send({
                type: 'agent_status',
                user_id: this.userId,
                site_id: this.siteId,
                status: status
            });
        }
    }

    // Session management
    transferSession(toUserId, reason = null) {
        if (this.userType === 'agent') {
            this.send({
                type: 'session_transfer',
                session_id: this.sessionId,
                from_user_id: this.userId,
                to_user_id: toUserId,
                reason: reason
            });
        }
    }

    closeSession(reason = null) {
        this.send({
            type: 'session_close',
            session_id: this.sessionId,
            user_id: this.userId,
            reason: reason
        });
    }

    // Heartbeat
    startHeartbeat() {
        this.heartbeatTimer = setInterval(() => {
            if (this.isConnected) {
                this.send({
                    type: 'heartbeat',
                    timestamp: new Date().toISOString()
                });
            }
        }, this.heartbeatInterval);
    }

    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    // Event handling
    on(event, handler) {
        if (!this.eventHandlers[event]) {
            this.eventHandlers[event] = [];
        }
        this.eventHandlers[event].push(handler);
    }

    off(event, handler) {
        if (this.eventHandlers[event]) {
            const index = this.eventHandlers[event].indexOf(handler);
            if (index > -1) {
                this.eventHandlers[event].splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].forEach(handler => {
                try {
                    handler(data);
                } catch (error) {
                    console.error('Error in event handler:', error);
                }
            });
        }
    }

    // Cleanup
    disconnect() {
        this.stopHeartbeat();
        if (this.ws) {
            this.ws.close();
        }
    }
}

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatWebSocket;
}

// Make available globally
window.ChatWebSocket = ChatWebSocket;

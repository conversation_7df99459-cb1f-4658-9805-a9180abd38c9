<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class MonitorPerformance extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'monitor:performance 
                            {--alert-threshold=80 : CPU/Memory threshold for alerts}
                            {--log-results : Log results to performance log}';

    /**
     * The console command description.
     */
    protected $description = 'Monitor system performance and generate alerts if thresholds are exceeded';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $alertThreshold = (int) $this->option('alert-threshold');
        $logResults = $this->option('log-results');

        $this->info('Starting performance monitoring...');

        $metrics = [
            'timestamp' => now()->toISOString(),
            'database' => $this->checkDatabasePerformance(),
            'cache' => $this->checkCachePerformance(),
            'storage' => $this->checkStorageUsage(),
            'memory' => $this->checkMemoryUsage(),
            'queue' => $this->checkQueueStatus(),
            'websocket' => $this->checkWebSocketStatus()
        ];

        $this->displayMetrics($metrics);

        // Check for alerts
        $alerts = $this->checkAlerts($metrics, $alertThreshold);
        if (!empty($alerts)) {
            $this->displayAlerts($alerts);
        }

        // Log results if requested
        if ($logResults) {
            Log::channel('performance')->info('Performance metrics', $metrics);
        }

        // Store metrics in cache for dashboard
        Cache::put('performance_metrics', $metrics, 300); // 5 minutes

        $this->info('Performance monitoring completed.');
    }

    /**
     * Check database performance
     */
    protected function checkDatabasePerformance(): array
    {
        $start = microtime(true);
        
        try {
            // Test database connection
            DB::connection()->getPdo();
            $connectionTime = (microtime(true) - $start) * 1000;

            // Check slow queries
            $slowQueries = $this->getSlowQueries();

            // Check table sizes
            $tableSizes = $this->getTableSizes();

            // Check active connections
            $activeConnections = $this->getActiveConnections();

            return [
                'status' => 'healthy',
                'connection_time_ms' => round($connectionTime, 2),
                'slow_queries' => $slowQueries,
                'table_sizes' => $tableSizes,
                'active_connections' => $activeConnections
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
                'connection_time_ms' => null
            ];
        }
    }

    /**
     * Check cache performance
     */
    protected function checkCachePerformance(): array
    {
        $start = microtime(true);
        
        try {
            // Test cache operations
            $testKey = 'performance_test_' . time();
            Cache::put($testKey, 'test_value', 60);
            $value = Cache::get($testKey);
            Cache::forget($testKey);
            
            $operationTime = (microtime(true) - $start) * 1000;

            // Get cache statistics (Redis specific)
            $stats = $this->getCacheStats();

            return [
                'status' => $value === 'test_value' ? 'healthy' : 'error',
                'operation_time_ms' => round($operationTime, 2),
                'stats' => $stats
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Check storage usage
     */
    protected function checkStorageUsage(): array
    {
        try {
            $publicDisk = Storage::disk('public');
            $privateDisk = Storage::disk('private');

            return [
                'status' => 'healthy',
                'public_files' => count($publicDisk->allFiles()),
                'private_files' => count($privateDisk->allFiles()),
                'public_size_mb' => $this->getDirectorySize(storage_path('app/public')),
                'private_size_mb' => $this->getDirectorySize(storage_path('app/private'))
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Check memory usage
     */
    protected function checkMemoryUsage(): array
    {
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));

        $usagePercent = $memoryLimit > 0 ? ($memoryUsage / $memoryLimit) * 100 : 0;

        return [
            'current_mb' => round($memoryUsage / 1024 / 1024, 2),
            'peak_mb' => round($memoryPeak / 1024 / 1024, 2),
            'limit_mb' => round($memoryLimit / 1024 / 1024, 2),
            'usage_percent' => round($usagePercent, 2)
        ];
    }

    /**
     * Check queue status
     */
    protected function checkQueueStatus(): array
    {
        try {
            // This would depend on your queue driver
            // For database queue, check jobs table
            $pendingJobs = DB::table('jobs')->count();
            $failedJobs = DB::table('failed_jobs')->count();

            return [
                'status' => 'healthy',
                'pending_jobs' => $pendingJobs,
                'failed_jobs' => $failedJobs
            ];

        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Check WebSocket status
     */
    protected function checkWebSocketStatus(): array
    {
        // This would check if WebSocket server is running
        // Implementation depends on your WebSocket setup
        return [
            'status' => 'unknown',
            'message' => 'WebSocket monitoring not implemented'
        ];
    }

    /**
     * Display metrics in a formatted table
     */
    protected function displayMetrics(array $metrics): void
    {
        $this->info('=== Performance Metrics ===');
        
        $this->table(
            ['Component', 'Status', 'Details'],
            [
                ['Database', $metrics['database']['status'], 
                 'Connection: ' . ($metrics['database']['connection_time_ms'] ?? 'N/A') . 'ms'],
                ['Cache', $metrics['cache']['status'], 
                 'Operation: ' . ($metrics['cache']['operation_time_ms'] ?? 'N/A') . 'ms'],
                ['Storage', $metrics['storage']['status'], 
                 'Public: ' . ($metrics['storage']['public_files'] ?? 'N/A') . ' files'],
                ['Memory', 'info', 
                 $metrics['memory']['current_mb'] . 'MB (' . $metrics['memory']['usage_percent'] . '%)'],
                ['Queue', $metrics['queue']['status'], 
                 'Pending: ' . ($metrics['queue']['pending_jobs'] ?? 'N/A')],
                ['WebSocket', $metrics['websocket']['status'], 
                 $metrics['websocket']['message'] ?? '']
            ]
        );
    }

    /**
     * Check for performance alerts
     */
    protected function checkAlerts(array $metrics, int $threshold): array
    {
        $alerts = [];

        // Memory usage alert
        if ($metrics['memory']['usage_percent'] > $threshold) {
            $alerts[] = [
                'type' => 'memory',
                'severity' => 'warning',
                'message' => "Memory usage is {$metrics['memory']['usage_percent']}% (threshold: {$threshold}%)"
            ];
        }

        // Database connection time alert
        if (isset($metrics['database']['connection_time_ms']) && 
            $metrics['database']['connection_time_ms'] > 1000) {
            $alerts[] = [
                'type' => 'database',
                'severity' => 'warning',
                'message' => "Database connection time is {$metrics['database']['connection_time_ms']}ms"
            ];
        }

        // Failed jobs alert
        if (isset($metrics['queue']['failed_jobs']) && $metrics['queue']['failed_jobs'] > 10) {
            $alerts[] = [
                'type' => 'queue',
                'severity' => 'error',
                'message' => "High number of failed jobs: {$metrics['queue']['failed_jobs']}"
            ];
        }

        return $alerts;
    }

    /**
     * Display alerts
     */
    protected function displayAlerts(array $alerts): void
    {
        $this->warn('=== Performance Alerts ===');
        
        foreach ($alerts as $alert) {
            $color = $alert['severity'] === 'error' ? 'error' : 'warn';
            $this->$color("[{$alert['severity']}] {$alert['type']}: {$alert['message']}");
        }
    }

    /**
     * Get slow queries (MySQL specific)
     */
    protected function getSlowQueries(): int
    {
        try {
            $result = DB::select("SHOW GLOBAL STATUS LIKE 'Slow_queries'");
            return isset($result[0]) ? (int) $result[0]->Value : 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get table sizes
     */
    protected function getTableSizes(): array
    {
        try {
            $tables = DB::select("
                SELECT table_name, 
                       ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                FROM information_schema.TABLES 
                WHERE table_schema = DATABASE()
                ORDER BY size_mb DESC
                LIMIT 5
            ");
            
            return collect($tables)->mapWithKeys(function ($table) {
                return [$table->table_name => $table->size_mb];
            })->toArray();
            
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get active database connections
     */
    protected function getActiveConnections(): int
    {
        try {
            $result = DB::select("SHOW STATUS LIKE 'Threads_connected'");
            return isset($result[0]) ? (int) $result[0]->Value : 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get cache statistics
     */
    protected function getCacheStats(): array
    {
        try {
            // This would be Redis-specific
            return [
                'connected_clients' => 'N/A',
                'used_memory' => 'N/A',
                'keyspace_hits' => 'N/A'
            ];
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * Get directory size in MB
     */
    protected function getDirectorySize(string $path): float
    {
        if (!is_dir($path)) {
            return 0;
        }

        $size = 0;
        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($path, \RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            $size += $file->getSize();
        }

        return round($size / 1024 / 1024, 2);
    }

    /**
     * Parse memory limit string to bytes
     */
    protected function parseMemoryLimit(string $limit): int
    {
        if ($limit === '-1') {
            return 0; // Unlimited
        }

        $unit = strtolower(substr($limit, -1));
        $value = (int) substr($limit, 0, -1);

        switch ($unit) {
            case 'g':
                return $value * 1024 * 1024 * 1024;
            case 'm':
                return $value * 1024 * 1024;
            case 'k':
                return $value * 1024;
            default:
                return (int) $limit;
        }
    }
}

<?php

namespace App\Http\Controllers;

use App\Models\VisitorTracking;
use App\Models\MouseTracking;
use App\Models\ClickEvent;
use App\Models\FormEvent;
use App\Models\Visitor;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;

class TrackingController extends Controller
{
    /**
     * Track page view
     */
    public function trackPageView(Request $request, $siteId): JsonResponse
    {
        try {
            $request->validate([
                'visitor_id' => 'required|string',
                'session_id' => 'required|string',
                'page_url' => 'required|url',
                'page_title' => 'nullable|string',
                'referrer' => 'nullable|url',
                'utm_source' => 'nullable|string',
                'utm_medium' => 'nullable|string',
                'utm_campaign' => 'nullable|string',
                'utm_term' => 'nullable|string',
                'utm_content' => 'nullable|string',
                'device_info' => 'nullable|array',
                'browser_info' => 'nullable|array',
                'screen_resolution' => 'nullable|array',
                'viewport_size' => 'nullable|array',
                'user_agent' => 'nullable|string',
                'language' => 'nullable|string',
            ]);

            // Get or create visitor
            $visitor = Visitor::firstOrCreate(
                ['visitor_id' => $request->visitor_id],
                [
                    'site_id' => $siteId,
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->user_agent ?? $request->header('User-Agent'),
                    'first_visit' => now(),
                ]
            );

            // Update visitor location if not set
            if (!$visitor->country || !$visitor->city) {
                // Here you would integrate with a GeoIP service
                // For now, we'll use placeholder values
                $visitor->update([
                    'country' => $request->country ?? 'Unknown',
                    'city' => $request->city ?? 'Unknown',
                ]);
            }

            // Create or update visitor tracking
            $tracking = VisitorTracking::updateOrCreate(
                [
                    'site_id' => $siteId,
                    'visitor_id' => $visitor->id,
                    'session_id' => $request->session_id,
                ],
                array_merge($request->only([
                    'page_url', 'page_title', 'referrer', 'utm_source', 'utm_medium',
                    'utm_campaign', 'utm_term', 'utm_content', 'device_info',
                    'browser_info', 'screen_resolution', 'viewport_size', 'user_agent',
                    'language'
                ]), [
                    'ip_address' => $request->ip(),
                    'country' => $visitor->country,
                    'city' => $visitor->city,
                ])
            );

            // Increment page views
            $tracking->incrementPageViews();

            return response()->json([
                'success' => true,
                'data' => [
                    'tracking_id' => $tracking->tracking_id,
                    'visitor_id' => $visitor->visitor_id,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to track page view', [
                'site_id' => $siteId,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to track page view',
            ], 500);
        }
    }

    /**
     * Track mouse movements and generate heatmap data
     */
    public function trackMouseMovement(Request $request, $siteId): JsonResponse
    {
        try {
            $request->validate([
                'visitor_id' => 'required|string',
                'session_id' => 'required|string',
                'page_url' => 'required|url',
                'mouse_data' => 'required|array',
                'mouse_data.*.x' => 'required|integer',
                'mouse_data.*.y' => 'required|integer',
                'mouse_data.*.timestamp' => 'required|integer',
                'mouse_data.*.type' => 'required|in:move,click',
                'viewport_size' => 'nullable|array',
                'duration' => 'nullable|integer',
            ]);

            $visitor = Visitor::where('visitor_id', $request->visitor_id)->first();
            if (!$visitor) {
                return response()->json([
                    'success' => false,
                    'message' => 'Visitor not found',
                ], 404);
            }

            $visitorTracking = VisitorTracking::where('site_id', $siteId)
                ->where('visitor_id', $visitor->id)
                ->where('session_id', $request->session_id)
                ->first();

            $mouseTracking = MouseTracking::updateOrCreate(
                [
                    'site_id' => $siteId,
                    'visitor_id' => $visitor->id,
                    'visitor_tracking_id' => $visitorTracking?->id,
                    'session_id' => $request->session_id,
                    'page_url' => $request->page_url,
                ],
                [
                    'mouse_data' => $request->mouse_data,
                    'viewport_size' => $request->viewport_size,
                    'duration' => $request->duration,
                ]
            );

            // Generate heatmap data
            $mouseTracking->generateHeatmapData();

            return response()->json([
                'success' => true,
                'data' => [
                    'tracking_id' => $mouseTracking->tracking_id,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to track mouse movement', [
                'site_id' => $siteId,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to track mouse movement',
            ], 500);
        }
    }

    /**
     * Track click events
     */
    public function trackClick(Request $request, $siteId): JsonResponse
    {
        try {
            $request->validate([
                'visitor_id' => 'required|string',
                'session_id' => 'required|string',
                'page_url' => 'required|url',
                'element_type' => 'required|string',
                'element_id' => 'nullable|string',
                'element_class' => 'nullable|string',
                'element_text' => 'nullable|string',
                'element_selector' => 'nullable|string',
                'click_x' => 'required|integer',
                'click_y' => 'required|integer',
                'viewport_x' => 'nullable|integer',
                'viewport_y' => 'nullable|integer',
                'button' => 'nullable|integer|in:0,1,2',
                'modifier_keys' => 'nullable|array',
                'timestamp' => 'required|integer',
                'page_load_time' => 'nullable|integer',
            ]);

            $visitor = Visitor::where('visitor_id', $request->visitor_id)->first();
            if (!$visitor) {
                return response()->json([
                    'success' => false,
                    'message' => 'Visitor not found',
                ], 404);
            }

            $visitorTracking = VisitorTracking::where('site_id', $siteId)
                ->where('visitor_id', $visitor->id)
                ->where('session_id', $request->session_id)
                ->first();

            $clickEvent = ClickEvent::create(array_merge($request->all(), [
                'site_id' => $siteId,
                'visitor_id' => $visitor->id,
                'visitor_tracking_id' => $visitorTracking?->id,
            ]));

            // Update visitor tracking click count
            if ($visitorTracking) {
                $visitorTracking->incrementClicks();
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'event_id' => $clickEvent->event_id,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to track click event', [
                'site_id' => $siteId,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to track click event',
            ], 500);
        }
    }

    /**
     * Track form interactions
     */
    public function trackFormEvent(Request $request, $siteId): JsonResponse
    {
        try {
            $request->validate([
                'visitor_id' => 'required|string',
                'session_id' => 'required|string',
                'page_url' => 'required|url',
                'form_id' => 'nullable|string',
                'form_name' => 'nullable|string',
                'form_action' => 'nullable|string',
                'form_method' => 'nullable|string',
                'field_name' => 'required|string',
                'field_type' => 'required|string',
                'field_label' => 'nullable|string',
                'event_type' => 'required|in:focus,blur,change,submit',
                'field_value' => 'nullable|string',
                'previous_value' => 'nullable|string',
                'validation_errors' => 'nullable|array',
                'timestamp' => 'required|integer',
                'time_spent' => 'nullable|integer',
                'keystroke_count' => 'nullable|integer',
                'focus_count' => 'nullable|integer',
            ]);

            $visitor = Visitor::where('visitor_id', $request->visitor_id)->first();
            if (!$visitor) {
                return response()->json([
                    'success' => false,
                    'message' => 'Visitor not found',
                ], 404);
            }

            $visitorTracking = VisitorTracking::where('site_id', $siteId)
                ->where('visitor_id', $visitor->id)
                ->where('session_id', $request->session_id)
                ->first();

            $formEvent = FormEvent::create(array_merge($request->all(), [
                'site_id' => $siteId,
                'visitor_id' => $visitor->id,
                'visitor_tracking_id' => $visitorTracking?->id,
            ]));

            // Update visitor tracking form interaction count
            if ($visitorTracking) {
                $visitorTracking->incrementFormInteractions();
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'event_id' => $formEvent->event_id,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to track form event', [
                'site_id' => $siteId,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to track form event',
            ], 500);
        }
    }

    /**
     * Track scroll behavior
     */
    public function trackScroll(Request $request, $siteId): JsonResponse
    {
        try {
            $request->validate([
                'visitor_id' => 'required|string',
                'session_id' => 'required|string',
                'page_url' => 'required|url',
                'scroll_depth' => 'required|integer|min:0|max:100',
                'max_scroll' => 'nullable|integer',
                'scroll_events' => 'nullable|array',
            ]);

            $visitor = Visitor::where('visitor_id', $request->visitor_id)->first();
            if (!$visitor) {
                return response()->json([
                    'success' => false,
                    'message' => 'Visitor not found',
                ], 404);
            }

            $visitorTracking = VisitorTracking::where('site_id', $siteId)
                ->where('visitor_id', $visitor->id)
                ->where('session_id', $request->session_id)
                ->first();

            if ($visitorTracking) {
                $visitorTracking->updateScrollDepth($request->scroll_depth);
            }

            // Update mouse tracking with scroll data if exists
            if ($request->scroll_events) {
                $mouseTracking = MouseTracking::where('site_id', $siteId)
                    ->where('visitor_id', $visitor->id)
                    ->where('session_id', $request->session_id)
                    ->where('page_url', $request->page_url)
                    ->first();

                if ($mouseTracking) {
                    foreach ($request->scroll_events as $scrollEvent) {
                        $mouseTracking->addScroll(
                            $scrollEvent['scroll_top'] ?? 0,
                            $scrollEvent['scroll_left'] ?? 0,
                            $scrollEvent['timestamp'] ?? time() * 1000
                        );
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'scroll_depth' => $request->scroll_depth,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to track scroll behavior', [
                'site_id' => $siteId,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to track scroll behavior',
            ], 500);
        }
    }

    /**
     * End visitor session
     */
    public function endSession(Request $request, $siteId): JsonResponse
    {
        try {
            $request->validate([
                'visitor_id' => 'required|string',
                'session_id' => 'required|string',
                'exit_page' => 'nullable|url',
                'session_duration' => 'nullable|integer',
            ]);

            $visitor = Visitor::where('visitor_id', $request->visitor_id)->first();
            if (!$visitor) {
                return response()->json([
                    'success' => false,
                    'message' => 'Visitor not found',
                ], 404);
            }

            $visitorTracking = VisitorTracking::where('site_id', $siteId)
                ->where('visitor_id', $visitor->id)
                ->where('session_id', $request->session_id)
                ->first();

            if ($visitorTracking) {
                $visitorTracking->update([
                    'left_at' => now(),
                    'exit_page' => $request->exit_page,
                ]);

                // Update duration if provided or calculate from timestamps
                if ($request->session_duration) {
                    $visitorTracking->update(['visit_duration' => $request->session_duration]);
                } else {
                    $visitorTracking->updateDuration();
                }

                // Check if it's a bounce (single page view with short duration)
                if ($visitorTracking->page_views <= 1 && $visitorTracking->visit_duration < 30) {
                    $visitorTracking->markAsBounce();
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Session ended successfully',
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to end session', [
                'site_id' => $siteId,
                'error' => $e->getMessage(),
                'request_data' => $request->all(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to end session',
            ], 500);
        }
    }
}

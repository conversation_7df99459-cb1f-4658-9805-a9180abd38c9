@echo off
chcp 65001 >nul
title 客服系统部署脚本
color 0A

echo.
echo ========================================
echo    客服系统一键部署脚本
echo ========================================
echo.

:: 获取当前脚本所在目录作为项目目录
set PROJECT_DIR=%~dp0
set PROJECT_DIR=%PROJECT_DIR:~0,-1%

echo 项目目录: %PROJECT_DIR%
echo.

:: 检查PHP路径
set PHP_FOUND=0
if exist "D:\BtSoft\php\72\php.exe" (
    set PHP_PATH=D:\BtSoft\php\72\php.exe
    set PHP_FOUND=1
    echo ✅ 找到 PHP 7.2
)
if exist "D:\BtSoft\php\73\php.exe" (
    set PHP_PATH=D:\BtSoft\php\73\php.exe
    set PHP_FOUND=1
    echo ✅ 找到 PHP 7.3
)
if exist "D:\BtSoft\php\74\php.exe" (
    set PHP_PATH=D:\BtSoft\php\74\php.exe
    set PHP_FOUND=1
    echo ✅ 找到 PHP 7.4
)

if %PHP_FOUND%==0 (
    echo ❌ 未找到PHP，请先安装宝塔面板和PHP
    pause
    exit /b 1
)

:: 检查Composer
composer --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Composer未安装，正在尝试下载...
    
    :: 尝试下载composer.phar到当前目录
    curl -sS https://getcomposer.org/installer | "%PHP_PATH%"
    if exist composer.phar (
        echo ✅ Composer下载成功
        set COMPOSER_CMD="%PHP_PATH%" composer.phar
    ) else (
        echo ❌ Composer下载失败，请手动安装
        echo 访问: https://getcomposer.org/download/
        pause
        exit /b 1
    )
) else (
    echo ✅ Composer已安装
    set COMPOSER_CMD=composer
)

echo.
echo [1/6] 配置Composer...
%COMPOSER_CMD% config -g secure-http false
%COMPOSER_CMD% config -g repo.packagist composer http://packagist.phpcomposer.com
echo ✅ Composer配置完成

echo.
echo [2/6] 安装依赖...
%COMPOSER_CMD% install --no-dev --optimize-autoloader
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败，尝试清理后重新安装...
    if exist vendor rmdir /s /q vendor
    if exist composer.lock del composer.lock
    %COMPOSER_CMD% install --no-dev --optimize-autoloader --ignore-platform-reqs
    if %errorlevel% neq 0 (
        echo ❌ 依赖安装仍然失败
        pause
        exit /b 1
    )
)
echo ✅ 依赖安装完成

echo.
echo [3/6] 配置Laravel...
if not exist .env (
    if exist .env.example (
        copy .env.example .env
        echo ✅ 环境文件已创建
    ) else (
        echo ❌ .env.example文件不存在
        pause
        exit /b 1
    )
)

"%PHP_PATH%" artisan key:generate --force
if %errorlevel% neq 0 (
    echo ❌ 密钥生成失败
    pause
    exit /b 1
)
echo ✅ Laravel配置完成

echo.
echo [4/6] 配置数据库连接...
echo 请输入数据库信息：
set /p DB_NAME="数据库名 (默认: aichat_jagship_com): "
if "%DB_NAME%"=="" set DB_NAME=aichat_jagship_com

set /p DB_USER="数据库用户名 (默认: %DB_NAME%): "
if "%DB_USER%"=="" set DB_USER=%DB_NAME%

set /p DB_PASS="数据库密码: "
if "%DB_PASS%"=="" (
    echo ❌ 数据库密码不能为空
    pause
    exit /b 1
)

:: 更新.env文件
powershell -Command "(Get-Content .env) -replace 'DB_DATABASE=.*', 'DB_DATABASE=%DB_NAME%' | Set-Content .env"
powershell -Command "(Get-Content .env) -replace 'DB_USERNAME=.*', 'DB_USERNAME=%DB_USER%' | Set-Content .env"
powershell -Command "(Get-Content .env) -replace 'DB_PASSWORD=.*', 'DB_PASSWORD=%DB_PASS%' | Set-Content .env"
powershell -Command "(Get-Content .env) -replace 'APP_URL=.*', 'APP_URL=http://aichat.jagship.com' | Set-Content .env"

echo ✅ 数据库配置完成

echo.
echo [5/6] 运行数据库迁移...
"%PHP_PATH%" artisan migrate --force
if %errorlevel% neq 0 (
    echo ⚠️ 数据库迁移失败，请检查数据库连接
    echo 您可以稍后手动运行: php artisan migrate
) else (
    echo ✅ 数据库迁移完成
)

echo.
echo [6/6] 创建存储链接...
"%PHP_PATH%" artisan storage:link
echo ✅ 存储链接创建完成

echo.
echo ========================================
echo    🎉 部署完成！
echo ========================================
echo.
echo 项目路径: %PROJECT_DIR%
echo 网站根目录应设置为: %PROJECT_DIR%\public
echo.
echo 下一步：
echo 1. 在宝塔面板中创建网站，根目录指向 public 文件夹
echo 2. 配置PHP扩展和禁用函数
echo 3. 访问网站测试
echo.
pause

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('st_custom_components', function (Blueprint $table) {
            $table->id();
            $table->string('component_id')->unique();
            $table->unsignedBigInteger('site_id');
            $table->string('name');
            $table->string('type'); // banner, custom_html, contact_form, rating_widget, etc.
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('position'); // header, footer, chat_header, chat_footer, etc.
            $table->integer('order_index')->default(0);
            
            // Component settings (type-specific configuration)
            $table->json('settings')->nullable();
            
            // Component styles (CSS properties)
            $table->json('styles')->nullable();
            
            // Component content (HTML, text, etc.)
            $table->longText('content')->nullable();
            
            // Display conditions
            $table->json('conditions')->nullable();
            
            // Additional metadata
            $table->json('metadata')->nullable();
            
            $table->timestamps();
            $table->softDeletes();
            
            // Indexes
            $table->index(['site_id', 'position', 'is_active']);
            $table->index(['site_id', 'type']);
            $table->index(['position', 'order_index']);
            $table->index('component_id');
            
            // Foreign key constraint
            $table->foreign('site_id')->references('id')->on('st_sites')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('st_custom_components');
    }
};

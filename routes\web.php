<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// Authentication Routes
Route::get('/login', 'Auth\LoginController@showLoginForm')->name('login');
Route::post('/login', 'Auth\LoginController@login');
Route::post('/logout', 'Auth\LoginController@logout')->name('logout');

// Dashboard Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/dashboard', 'DashboardController@index')->name('dashboard');
    
    // Chat Management
    Route::prefix('chat')->group(function () {
        Route::get('/', 'ChatController@index')->name('chat.index');
        Route::get('/sessions', 'ChatController@sessions')->name('chat.sessions');
        Route::post('/send', 'ChatController@send')->name('chat.send');
        Route::post('/upload', 'ChatController@upload')->name('chat.upload');
    });
    
    // Site Management
    Route::prefix('sites')->group(function () {
        Route::get('/', 'SiteController@index')->name('sites.index');
        Route::post('/', 'SiteController@store')->name('sites.store');
        Route::get('/{site}', 'SiteController@show')->name('sites.show');
        Route::put('/{site}', 'SiteController@update')->name('sites.update');
        Route::delete('/{site}', 'SiteController@destroy')->name('sites.destroy');
    });
    
    // Analytics Routes
    Route::prefix('analytics')->group(function () {
        Route::get('/', 'AnalyticsController@index')->name('analytics.index');
        Route::get('/sessions', 'AnalyticsController@sessions')->name('analytics.sessions');
        Route::get('/visitors', 'AnalyticsController@visitors')->name('analytics.visitors');
        Route::get('/export', 'AnalyticsController@export')->name('analytics.export');
    });
    
    // Settings Routes
    Route::prefix('settings')->group(function () {
        Route::get('/', 'SettingsController@index')->name('settings.index');
        Route::post('/working-hours', 'SettingsController@updateWorkingHours')->name('settings.working-hours');
        Route::post('/appearance', 'SettingsController@updateAppearance')->name('settings.appearance');
        Route::post('/languages', 'SettingsController@updateLanguages')->name('settings.languages');
    });
});

// Public Chat Widget Routes
Route::prefix('widget')->group(function () {
    Route::get('/chat/{site}', 'Widget\ChatController@show')->name('widget.chat');
    Route::post('/chat/{site}/message', 'Widget\ChatController@sendMessage')->name('widget.message');
    Route::post('/chat/{site}/upload', 'Widget\ChatController@uploadFile')->name('widget.upload');
    Route::get('/chat/{site}/status', 'Widget\ChatController@getStatus')->name('widget.status');
});

// Public Message/Contact Form Routes
Route::prefix('contact')->group(function () {
    Route::post('/{site}', 'ContactController@store')->name('contact.store');
    Route::get('/{site}/form', 'ContactController@showForm')->name('contact.form');
});

// JavaScript Widget Route
Route::get('/js/widget.js', 'Widget\WidgetController@script')->name('widget.script');

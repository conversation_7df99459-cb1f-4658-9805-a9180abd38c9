import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'
import { useAuthStore } from './auth.js'
import { useNotificationStore } from './notifications.js'

export const useChatStore = defineStore('chat', () => {
  // State
  const activeSessions = ref([])
  const waitingSessions = ref([])
  const currentSession = ref(null)
  const messages = ref([])
  const isLoading = ref(false)
  const typingUsers = ref([])

  // Getters
  const totalActiveSessions = computed(() => activeSessions.value.length)
  const totalWaitingSessions = computed(() => waitingSessions.value.length)
  const hasActiveSessions = computed(() => totalActiveSessions.value > 0)
  const hasWaitingSessions = computed(() => totalWaitingSessions.value > 0)

  // Actions
  const fetchWorkspaceOverview = async () => {
    try {
      isLoading.value = true
      const response = await axios.get('/admin/workspace/overview')
      
      if (response.data.success) {
        const data = response.data.data
        activeSessions.value = data.active_sessions || []
        waitingSessions.value = data.waiting_sessions || []
        return data
      }
    } catch (error) {
      console.error('Failed to fetch workspace overview:', error)
      const notificationStore = useNotificationStore()
      notificationStore.error('Error', 'Failed to load workspace data')
    } finally {
      isLoading.value = false
    }
  }

  const acceptChat = async (sessionId) => {
    try {
      const response = await axios.post(`/admin/workspace/chats/${sessionId}/accept`)
      
      if (response.data.success) {
        // Move session from waiting to active
        const sessionIndex = waitingSessions.value.findIndex(s => s.session_id === sessionId)
        if (sessionIndex > -1) {
          const session = waitingSessions.value.splice(sessionIndex, 1)[0]
          session.status = 'active'
          activeSessions.value.push(session)
        }
        
        const notificationStore = useNotificationStore()
        notificationStore.success('Chat Accepted', 'You have successfully accepted the chat')
        
        return response.data.data
      }
    } catch (error) {
      console.error('Failed to accept chat:', error)
      const notificationStore = useNotificationStore()
      notificationStore.error('Error', 'Failed to accept chat')
    }
  }

  const transferChat = async (sessionId, targetAgentId, reason = '') => {
    try {
      const response = await axios.post(`/admin/workspace/chats/${sessionId}/transfer`, {
        target_agent_id: targetAgentId,
        reason
      })
      
      if (response.data.success) {
        // Remove session from active sessions
        const sessionIndex = activeSessions.value.findIndex(s => s.session_id === sessionId)
        if (sessionIndex > -1) {
          activeSessions.value.splice(sessionIndex, 1)
        }
        
        const notificationStore = useNotificationStore()
        notificationStore.success('Chat Transferred', 'Chat has been transferred successfully')
        
        return response.data.data
      }
    } catch (error) {
      console.error('Failed to transfer chat:', error)
      const notificationStore = useNotificationStore()
      notificationStore.error('Error', 'Failed to transfer chat')
    }
  }

  const endChat = async (sessionId, reason = '', internalNote = '') => {
    try {
      const response = await axios.post(`/admin/workspace/chats/${sessionId}/end`, {
        reason,
        internal_note: internalNote
      })
      
      if (response.data.success) {
        // Remove session from active sessions
        const sessionIndex = activeSessions.value.findIndex(s => s.session_id === sessionId)
        if (sessionIndex > -1) {
          activeSessions.value.splice(sessionIndex, 1)
        }
        
        // Clear current session if it was the ended one
        if (currentSession.value?.session_id === sessionId) {
          currentSession.value = null
          messages.value = []
        }
        
        const notificationStore = useNotificationStore()
        notificationStore.success('Chat Ended', 'Chat session has been ended')
        
        return response.data.data
      }
    } catch (error) {
      console.error('Failed to end chat:', error)
      const notificationStore = useNotificationStore()
      notificationStore.error('Error', 'Failed to end chat')
    }
  }

  const loadChatSession = async (sessionId) => {
    try {
      isLoading.value = true
      const response = await axios.get(`/admin/workspace/chats/${sessionId}`)
      
      if (response.data.success) {
        currentSession.value = response.data.data.session
        messages.value = response.data.data.messages || []
        return response.data.data
      }
    } catch (error) {
      console.error('Failed to load chat session:', error)
      const notificationStore = useNotificationStore()
      notificationStore.error('Error', 'Failed to load chat session')
    } finally {
      isLoading.value = false
    }
  }

  const sendMessage = async (sessionId, content, messageType = 'text') => {
    try {
      const authStore = useAuthStore()
      
      // Optimistically add message to UI
      const tempMessage = {
        id: Date.now(),
        content,
        message_type: messageType,
        sender_type: 'agent',
        sender_id: authStore.user.id,
        sender_name: authStore.user.name,
        created_at: new Date().toISOString(),
        sending: true
      }
      
      messages.value.push(tempMessage)
      
      const response = await axios.post(`/chat/${sessionId}/messages`, {
        content,
        message_type: messageType
      })
      
      if (response.data.success) {
        // Replace temp message with real message
        const tempIndex = messages.value.findIndex(m => m.id === tempMessage.id)
        if (tempIndex > -1) {
          messages.value[tempIndex] = response.data.data
        }
        
        // Send via WebSocket for real-time delivery
        authStore.sendWebSocketMessage({
          type: 'new_message',
          session_id: sessionId,
          message: response.data.data
        })
        
        return response.data.data
      }
    } catch (error) {
      console.error('Failed to send message:', error)
      
      // Remove temp message on error
      const tempIndex = messages.value.findIndex(m => m.id === tempMessage.id)
      if (tempIndex > -1) {
        messages.value.splice(tempIndex, 1)
      }
      
      const notificationStore = useNotificationStore()
      notificationStore.error('Error', 'Failed to send message')
    }
  }

  const addInternalNote = async (sessionId, note) => {
    try {
      const response = await axios.post(`/admin/workspace/chats/${sessionId}/notes`, {
        note
      })
      
      if (response.data.success) {
        const notificationStore = useNotificationStore()
        notificationStore.success('Note Added', 'Internal note has been added')
        return response.data.data
      }
    } catch (error) {
      console.error('Failed to add internal note:', error)
      const notificationStore = useNotificationStore()
      notificationStore.error('Error', 'Failed to add internal note')
    }
  }

  const updateTypingStatus = (sessionId, isTyping) => {
    const authStore = useAuthStore()
    
    authStore.sendWebSocketMessage({
      type: 'typing_indicator',
      session_id: sessionId,
      sender_type: 'agent',
      sender_id: authStore.user.id,
      is_typing: isTyping
    })
  }

  const handleNewMessage = (messageData) => {
    // Add message to current session if it matches
    if (currentSession.value?.session_id === messageData.session_id) {
      const existingMessage = messages.value.find(m => m.id === messageData.message_id)
      if (!existingMessage) {
        messages.value.push(messageData)
      }
    }
    
    // Update session's last message in active sessions
    const sessionIndex = activeSessions.value.findIndex(s => s.session_id === messageData.session_id)
    if (sessionIndex > -1) {
      activeSessions.value[sessionIndex].last_message = messageData.content
      activeSessions.value[sessionIndex].last_message_at = messageData.created_at
    }
  }

  const handleTypingIndicator = (data) => {
    if (data.session_id === currentSession.value?.session_id) {
      const userId = `${data.sender_type}_${data.sender_id}`
      
      if (data.is_typing) {
        if (!typingUsers.value.includes(userId)) {
          typingUsers.value.push(userId)
        }
      } else {
        const index = typingUsers.value.indexOf(userId)
        if (index > -1) {
          typingUsers.value.splice(index, 1)
        }
      }
    }
  }

  const clearCurrentSession = () => {
    currentSession.value = null
    messages.value = []
    typingUsers.value = []
  }

  return {
    // State
    activeSessions,
    waitingSessions,
    currentSession,
    messages,
    isLoading,
    typingUsers,
    
    // Getters
    totalActiveSessions,
    totalWaitingSessions,
    hasActiveSessions,
    hasWaitingSessions,
    
    // Actions
    fetchWorkspaceOverview,
    acceptChat,
    transferChat,
    endChat,
    loadChatSession,
    sendMessage,
    addInternalNote,
    updateTypingStatus,
    handleNewMessage,
    handleTypingIndicator,
    clearCurrentSession
  }
})

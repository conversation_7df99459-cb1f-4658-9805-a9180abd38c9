<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSitesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sites', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('domain');
            $table->string('site_key')->unique(); // Unique identifier for widget integration
            $table->text('description')->nullable();
            $table->string('logo')->nullable();
            $table->json('allowed_domains')->nullable(); // For CORS
            $table->boolean('is_active')->default(true);
            
            // Appearance Settings
            $table->json('theme_settings')->nullable(); // Colors, fonts, etc.
            $table->json('widget_settings')->nullable(); // Position, size, etc.
            $table->json('branding_settings')->nullable(); // Logo, company name, etc.
            
            // Functional Settings
            $table->json('working_hours')->nullable(); // Working hours configuration
            $table->json('auto_messages')->nullable(); // Welcome messages, offline messages
            $table->json('file_upload_settings')->nullable(); // Allowed file types, max size
            $table->boolean('visitor_tracking_enabled')->default(true);
            $table->boolean('gdpr_enabled')->default(true);
            
            // Multi-language Support
            $table->string('default_language')->default('en');
            $table->json('supported_languages')->nullable();
            
            $table->timestamps();
            
            $table->index('domain');
            $table->index('site_key');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sites');
    }
}

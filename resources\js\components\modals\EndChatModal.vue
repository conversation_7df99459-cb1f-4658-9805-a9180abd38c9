<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('close')"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <form @submit.prevent="endChat">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                <XMarkIcon class="h-6 w-6 text-red-600" />
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                  End Chat Session
                </h3>
                <div class="mt-4 space-y-4">
                  <p class="text-sm text-gray-500">
                    Are you sure you want to end this chat session? This action cannot be undone.
                  </p>

                  <!-- Session info -->
                  <div class="bg-gray-50 p-3 rounded-md">
                    <div class="flex items-center space-x-3">
                      <img :src="session?.visitor_avatar || '/images/default-avatar.png'" 
                           :alt="session?.visitor_name" 
                           class="h-8 w-8 rounded-full">
                      <div>
                        <p class="text-sm font-medium text-gray-900">
                          {{ session?.visitor_name || 'Anonymous' }}
                        </p>
                        <p class="text-xs text-gray-500">
                          {{ session?.subject || 'No subject' }}
                        </p>
                        <p class="text-xs text-gray-500">
                          Duration: {{ formatDuration(session?.created_at) }}
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- End reason -->
                  <div>
                    <label for="reason" class="block text-sm font-medium text-gray-700">
                      Reason for Ending Chat
                    </label>
                    <select
                      id="reason"
                      v-model="form.reason"
                      required
                      class="mt-1 form-select"
                      :class="{ 'border-red-300': errors.reason }"
                    >
                      <option value="">Select a reason</option>
                      <option value="resolved">Issue resolved</option>
                      <option value="visitor_left">Visitor left</option>
                      <option value="no_response">No response from visitor</option>
                      <option value="inappropriate">Inappropriate behavior</option>
                      <option value="technical_issue">Technical issue</option>
                      <option value="escalated">Escalated to supervisor</option>
                      <option value="shift_end">End of shift</option>
                      <option value="other">Other</option>
                    </select>
                    <p v-if="errors.reason" class="mt-1 text-sm text-red-600">{{ errors.reason }}</p>
                  </div>

                  <!-- Custom reason -->
                  <div v-if="form.reason === 'other'">
                    <label for="custom_reason" class="block text-sm font-medium text-gray-700">
                      Custom Reason
                    </label>
                    <textarea
                      id="custom_reason"
                      v-model="form.customReason"
                      rows="2"
                      required
                      class="mt-1 form-textarea"
                      placeholder="Please specify the reason..."
                    ></textarea>
                  </div>

                  <!-- Closing message -->
                  <div>
                    <label for="closing_message" class="block text-sm font-medium text-gray-700">
                      Closing Message to Visitor (optional)
                    </label>
                    <textarea
                      id="closing_message"
                      v-model="form.closingMessage"
                      rows="2"
                      class="mt-1 form-textarea"
                      placeholder="Thank you for contacting us. Have a great day!"
                    ></textarea>
                  </div>

                  <!-- Internal note -->
                  <div>
                    <label for="internal_note" class="block text-sm font-medium text-gray-700">
                      Internal Note (for records)
                    </label>
                    <textarea
                      id="internal_note"
                      v-model="form.internalNote"
                      rows="3"
                      class="mt-1 form-textarea"
                      placeholder="Summary of the conversation, resolution provided, or any follow-up needed..."
                    ></textarea>
                  </div>

                  <!-- Resolution status -->
                  <div>
                    <label for="resolution_status" class="block text-sm font-medium text-gray-700">
                      Resolution Status
                    </label>
                    <select
                      id="resolution_status"
                      v-model="form.resolutionStatus"
                      class="mt-1 form-select"
                    >
                      <option value="resolved">Resolved</option>
                      <option value="partially_resolved">Partially Resolved</option>
                      <option value="unresolved">Unresolved</option>
                      <option value="escalated">Escalated</option>
                      <option value="no_resolution_needed">No Resolution Needed</option>
                    </select>
                  </div>

                  <!-- Satisfaction rating request -->
                  <div class="flex items-start">
                    <div class="flex items-center h-5">
                      <input
                        id="request_rating"
                        v-model="form.requestRating"
                        type="checkbox"
                        class="form-checkbox h-4 w-4 text-blue-600"
                      />
                    </div>
                    <div class="ml-3 text-sm">
                      <label for="request_rating" class="text-gray-700">
                        Send satisfaction survey to visitor
                      </label>
                      <p class="text-gray-500 text-xs">
                        The visitor will receive an email with a satisfaction survey
                      </p>
                    </div>
                  </div>

                  <!-- Follow-up required -->
                  <div class="flex items-start">
                    <div class="flex items-center h-5">
                      <input
                        id="follow_up_required"
                        v-model="form.followUpRequired"
                        type="checkbox"
                        class="form-checkbox h-4 w-4 text-blue-600"
                      />
                    </div>
                    <div class="ml-3 text-sm">
                      <label for="follow_up_required" class="text-gray-700">
                        Follow-up required
                      </label>
                      <p class="text-gray-500 text-xs">
                        Mark this session for follow-up action
                      </p>
                    </div>
                  </div>

                  <!-- Follow-up details -->
                  <div v-if="form.followUpRequired">
                    <label for="follow_up_date" class="block text-sm font-medium text-gray-700">
                      Follow-up Date
                    </label>
                    <input
                      id="follow_up_date"
                      v-model="form.followUpDate"
                      type="datetime-local"
                      class="mt-1 form-input"
                    />
                    <label for="follow_up_note" class="block text-sm font-medium text-gray-700 mt-2">
                      Follow-up Note
                    </label>
                    <textarea
                      id="follow_up_note"
                      v-model="form.followUpNote"
                      rows="2"
                      class="mt-1 form-textarea"
                      placeholder="What needs to be followed up on?"
                    ></textarea>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="submit"
              :disabled="isLoading"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="isLoading" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Ending Chat...
              </span>
              <span v-else>End Chat</span>
            </button>
            <button
              @click="$emit('close')"
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'

export default {
  name: 'EndChatModal',
  components: {
    XMarkIcon
  },
  props: {
    session: {
      type: Object,
      required: true
    }
  },
  emits: ['close', 'end'],
  setup(props, { emit }) {
    const isLoading = ref(false)
    const errors = reactive({})

    const form = reactive({
      reason: '',
      customReason: '',
      closingMessage: 'Thank you for contacting us. Have a great day!',
      internalNote: '',
      resolutionStatus: 'resolved',
      requestRating: true,
      followUpRequired: false,
      followUpDate: '',
      followUpNote: ''
    })

    const formatDuration = (startTime) => {
      if (!startTime) return '0 minutes'
      
      const start = new Date(startTime)
      const now = new Date()
      const diffMs = now - start
      const diffMins = Math.floor(diffMs / 60000)
      
      if (diffMins < 1) return 'Less than 1 minute'
      if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''}`
      
      const hours = Math.floor(diffMins / 60)
      const mins = diffMins % 60
      return `${hours} hour${hours > 1 ? 's' : ''} ${mins > 0 ? `${mins} minute${mins > 1 ? 's' : ''}` : ''}`
    }

    const validateForm = () => {
      Object.keys(errors).forEach(key => delete errors[key])

      if (!form.reason) {
        errors.reason = 'Please select a reason for ending the chat'
      }

      if (form.reason === 'other' && !form.customReason.trim()) {
        errors.customReason = 'Please specify the custom reason'
      }

      return Object.keys(errors).length === 0
    }

    const endChat = async () => {
      if (!validateForm()) {
        return
      }

      try {
        isLoading.value = true

        const endData = {
          sessionId: props.session.session_id,
          reason: form.reason === 'other' ? form.customReason : form.reason,
          closingMessage: form.closingMessage,
          internalNote: form.internalNote,
          resolutionStatus: form.resolutionStatus,
          requestRating: form.requestRating,
          followUpRequired: form.followUpRequired,
          followUpDate: form.followUpDate,
          followUpNote: form.followUpNote
        }

        emit('end', endData)
      } catch (error) {
        console.error('End chat failed:', error)
      } finally {
        isLoading.value = false
      }
    }

    return {
      isLoading,
      errors,
      form,
      formatDuration,
      endChat
    }
  }
}
</script>

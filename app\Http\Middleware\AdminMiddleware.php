<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, ...$roles)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Authentication required',
            ], 401);
        }

        $user = Auth::user();

        // Check if user account is active
        if (!$user->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Account is deactivated',
            ], 403);
        }

        // If no specific roles are required, allow any authenticated user
        if (empty($roles)) {
            return $next($request);
        }

        // Check if user has required role
        $userRole = $user->role;
        $allowedRoles = empty($roles) ? ['admin', 'supervisor', 'agent'] : $roles;

        if (!in_array($userRole, $allowedRoles)) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient permissions',
            ], 403);
        }

        // Update last activity
        $user->update(['last_activity_at' => now()]);

        return $next($request);
    }
}

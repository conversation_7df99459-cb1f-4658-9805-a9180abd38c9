<?php

namespace Database\Factories;

use App\Models\Visitor;
use App\Models\Site;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class VisitorFactory extends Factory
{
    protected $model = Visitor::class;

    public function definition()
    {
        return [
            'visitor_id' => Str::uuid(),
            'site_id' => Site::factory(),
            'ip_address' => $this->faker->ipv4(),
            'user_agent' => $this->faker->userAgent(),
            'country' => $this->faker->countryCode(),
            'city' => $this->faker->city(),
            'region' => $this->faker->state(),
            'timezone' => $this->faker->timezone(),
            'language' => $this->faker->randomElement(['en', 'zh', 'es', 'fr', 'de']),
            'referrer' => $this->faker->url(),
            'landing_page' => $this->faker->url(),
            'current_page' => $this->faker->url(),
            'device_type' => $this->faker->randomElement(['desktop', 'mobile', 'tablet']),
            'browser' => $this->faker->randomElement(['Chrome', 'Firefox', 'Safari', 'Edge']),
            'os' => $this->faker->randomElement(['Windows', 'macOS', 'Linux', 'iOS', 'Android']),
            'screen_resolution' => $this->faker->randomElement(['1920x1080', '1366x768', '1440x900', '375x667']),
            'is_returning' => $this->faker->boolean(30), // 30% chance of being returning visitor
            'session_count' => $this->faker->numberBetween(1, 10),
            'total_page_views' => $this->faker->numberBetween(1, 50),
            'total_time_spent' => $this->faker->numberBetween(60, 3600), // 1 minute to 1 hour
            'last_activity_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            'first_visit_at' => $this->faker->dateTimeBetween('-1 month', '-1 day'),
            'utm_source' => $this->faker->optional()->randomElement(['google', 'facebook', 'twitter', 'direct']),
            'utm_medium' => $this->faker->optional()->randomElement(['organic', 'cpc', 'social', 'email']),
            'utm_campaign' => $this->faker->optional()->words(3, true),
            'utm_term' => $this->faker->optional()->words(2, true),
            'utm_content' => $this->faker->optional()->words(2, true),
            'custom_data' => json_encode([
                'interests' => $this->faker->words(3),
                'preferences' => [
                    'theme' => $this->faker->randomElement(['light', 'dark']),
                    'language' => $this->faker->randomElement(['en', 'zh', 'es']),
                ],
            ]),
        ];
    }

    public function returning()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_returning' => true,
                'session_count' => $this->faker->numberBetween(2, 20),
                'first_visit_at' => $this->faker->dateTimeBetween('-3 months', '-1 week'),
            ];
        });
    }

    public function newVisitor()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_returning' => false,
                'session_count' => 1,
                'first_visit_at' => now(),
                'last_activity_at' => now(),
            ];
        });
    }

    public function mobile()
    {
        return $this->state(function (array $attributes) {
            return [
                'device_type' => 'mobile',
                'screen_resolution' => $this->faker->randomElement(['375x667', '414x896', '360x640']),
                'os' => $this->faker->randomElement(['iOS', 'Android']),
                'browser' => $this->faker->randomElement(['Chrome', 'Safari', 'Samsung Internet']),
            ];
        });
    }

    public function desktop()
    {
        return $this->state(function (array $attributes) {
            return [
                'device_type' => 'desktop',
                'screen_resolution' => $this->faker->randomElement(['1920x1080', '1366x768', '1440x900', '2560x1440']),
                'os' => $this->faker->randomElement(['Windows', 'macOS', 'Linux']),
                'browser' => $this->faker->randomElement(['Chrome', 'Firefox', 'Safari', 'Edge']),
            ];
        });
    }

    public function withUtmParameters()
    {
        return $this->state(function (array $attributes) {
            return [
                'utm_source' => $this->faker->randomElement(['google', 'facebook', 'twitter', 'linkedin']),
                'utm_medium' => $this->faker->randomElement(['organic', 'cpc', 'social', 'email']),
                'utm_campaign' => $this->faker->words(3, true),
                'utm_term' => $this->faker->words(2, true),
                'utm_content' => $this->faker->words(2, true),
            ];
        });
    }
}

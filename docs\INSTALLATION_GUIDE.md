# 系统安装部署指南

## 系统要求

### 服务器环境要求

#### 基础环境
- **操作系统**: CentOS 7+ / Ubuntu 18.04+ / Windows Server 2016+
- **Web服务器**: Nginx 1.20+ 或 Apache 2.4+
- **PHP**: 7.2 - 8.1
- **数据库**: MySQL 5.7+ 或 MariaDB 10.3+
- **缓存**: Redis 5.0+
- **内存**: 最低 2GB，推荐 4GB+
- **存储**: 最低 10GB，推荐 50GB+

#### PHP扩展要求
```
php-fpm
php-mysql
php-redis
php-gd
php-curl
php-mbstring
php-xml
php-zip
php-json
php-openssl
php-fileinfo
php-tokenizer
```

## 安装步骤

### 1. 环境准备

#### CentOS 7 环境安装
```bash
# 安装宝塔面板
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh

# 或手动安装环境
yum update -y
yum install -y nginx mysql57-server redis php72 php72-fpm
```

#### Ubuntu 环境安装
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装基础软件
sudo apt install -y nginx mysql-server redis-server php7.2-fpm php7.2-mysql php7.2-redis
```

### 2. 数据库配置

#### MySQL 配置
```bash
# 启动 MySQL
systemctl start mysqld
systemctl enable mysqld

# 设置 root 密码（CentOS）
mysql_secure_installation

# 创建数据库和用户
mysql -u root -p
```

```sql
CREATE DATABASE chat_web CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'chat_web'@'localhost' IDENTIFIED BY 'fcpHBiscJRBCznnw';
GRANT ALL PRIVILEGES ON chat_web.* TO 'chat_web'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

#### Redis 配置
```bash
# 启动 Redis
systemctl start redis
systemctl enable redis

# 配置 Redis（可选）
vim /etc/redis.conf
# 设置密码：requirepass your_password
# 设置最大内存：maxmemory 512mb
```

### 3. 项目部署

#### 下载项目文件
```bash
# 创建项目目录
mkdir -p /var/www/chat_web
cd /var/www/chat_web

# 如果有Git仓库
git clone https://github.com/your-repo/chat_web.git .

# 或者上传项目文件到此目录
```

#### 安装依赖
```bash
# 安装 Composer（如果未安装）
curl -sS https://getcomposer.org/installer | php
mv composer.phar /usr/local/bin/composer

# 安装 PHP 依赖
composer install --no-dev --optimize-autoloader

# 安装 Node.js 和 npm（用于前端构建）
curl -sL https://rpm.nodesource.com/setup_16.x | bash -
yum install -y nodejs

# 安装前端依赖
npm install
npm run production
```

#### 配置环境变量
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
vim .env
```

```env
APP_NAME="Customer Service System"
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://your-domain.com

LOG_CHANNEL=stack
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=chat_web
DB_USERNAME=chat_web
DB_PASSWORD=fcpHBiscJRBCznnw

BROADCAST_DRIVER=redis
CACHE_DRIVER=redis
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

WEBSOCKET_HOST=127.0.0.1
WEBSOCKET_PORT=8080
```

#### 生成应用密钥
```bash
php artisan key:generate
```

#### 运行数据库迁移
```bash
# 运行迁移
php artisan migrate

# 运行数据填充（可选）
php artisan db:seed
```

#### 设置文件权限
```bash
# 设置所有者
chown -R www-data:www-data /var/www/chat_web

# 设置目录权限
find /var/www/chat_web -type d -exec chmod 755 {} \;
find /var/www/chat_web -type f -exec chmod 644 {} \;

# 设置存储目录权限
chmod -R 775 /var/www/chat_web/storage
chmod -R 775 /var/www/chat_web/bootstrap/cache
```

### 4. Web服务器配置

#### Nginx 配置
创建配置文件 `/etc/nginx/sites-available/chat_web`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    root /var/www/chat_web/public;
    index index.php index.html;

    # SSL 配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # 文件上传大小限制
    client_max_body_size 10M;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php7.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # WebSocket 代理
    location /ws {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

启用站点：
```bash
ln -s /etc/nginx/sites-available/chat_web /etc/nginx/sites-enabled/
nginx -t
systemctl reload nginx
```

### 5. 启动服务

#### 启动 WebSocket 服务器
```bash
# 创建 systemd 服务文件
vim /etc/systemd/system/chat-websocket.service
```

```ini
[Unit]
Description=Chat WebSocket Server
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/var/www/chat_web
ExecStart=/usr/bin/php artisan websocket:serve
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

```bash
# 启动服务
systemctl daemon-reload
systemctl enable chat-websocket
systemctl start chat-websocket
```

#### 启动队列处理器
```bash
# 创建队列服务文件
vim /etc/systemd/system/chat-queue.service
```

```ini
[Unit]
Description=Chat Queue Worker
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/var/www/chat_web
ExecStart=/usr/bin/php artisan queue:work --sleep=3 --tries=3
Restart=always
RestartSec=5

[Install]
WantedBy=multi-user.target
```

```bash
systemctl enable chat-queue
systemctl start chat-queue
```

#### 设置定时任务
```bash
# 编辑 crontab
crontab -e -u www-data

# 添加以下内容
* * * * * cd /var/www/chat_web && php artisan schedule:run >> /dev/null 2>&1
```

### 6. 系统优化

#### Laravel 优化
```bash
# 缓存配置
php artisan config:cache

# 缓存路由
php artisan route:cache

# 缓存视图
php artisan view:cache

# 优化自动加载
composer dump-autoload --optimize
```

#### 系统监控
```bash
# 安装监控工具
yum install -y htop iotop nethogs

# 设置日志轮转
vim /etc/logrotate.d/chat_web
```

```
/var/www/chat_web/storage/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

## 验证安装

### 1. 检查服务状态
```bash
# 检查 Web 服务
systemctl status nginx

# 检查数据库
systemctl status mysqld

# 检查 Redis
systemctl status redis

# 检查 WebSocket 服务
systemctl status chat-websocket

# 检查队列服务
systemctl status chat-queue
```

### 2. 访问测试
1. 打开浏览器访问 `https://your-domain.com`
2. 检查是否能正常显示登录页面
3. 使用默认管理员账号登录（如果已创建）
4. 测试聊天功能是否正常

### 3. 性能测试
```bash
# 运行系统优化命令
php artisan system:optimize --all

# 检查性能监控
php artisan monitor:performance
```

## 故障排除

### 常见问题

#### 1. 500 错误
- 检查文件权限
- 查看错误日志：`tail -f storage/logs/laravel.log`
- 检查 `.env` 配置

#### 2. WebSocket 连接失败
- 检查防火墙设置
- 确认端口 8080 已开放
- 检查 WebSocket 服务状态

#### 3. 数据库连接失败
- 检查数据库服务状态
- 验证数据库连接信息
- 检查数据库用户权限

#### 4. Redis 连接失败
- 检查 Redis 服务状态
- 验证 Redis 配置
- 检查防火墙设置

### 日志文件位置
- Laravel 日志: `/var/www/chat_web/storage/logs/`
- Nginx 日志: `/var/log/nginx/`
- MySQL 日志: `/var/log/mysql/`
- Redis 日志: `/var/log/redis/`

## 安全建议

1. **定期更新系统和软件包**
2. **使用强密码和密钥**
3. **配置防火墙规则**
4. **启用 SSL/TLS 加密**
5. **定期备份数据**
6. **监控系统日志**
7. **限制文件上传类型和大小**

## 备份策略

### 数据库备份
```bash
# 创建备份脚本
vim /usr/local/bin/backup-database.sh
```

```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u chat_web -p'fcpHBiscJRBCznnw' chat_web > /backup/chat_web_$DATE.sql
gzip /backup/chat_web_$DATE.sql
find /backup -name "chat_web_*.sql.gz" -mtime +30 -delete
```

### 文件备份
```bash
# 备份上传文件和配置
tar -czf /backup/chat_web_files_$(date +%Y%m%d).tar.gz /var/www/chat_web/storage/app/public /var/www/chat_web/.env
```

---

*安装指南版本：v1.0*  
*最后更新：2024年1月*

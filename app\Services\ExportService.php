<?php

namespace App\Services;

use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\VisitorTracking;
use App\Models\Site;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class ExportService
{
    /**
     * Export data to CSV format
     */
    public function exportToCsv($data, $filename, $headers = [])
    {
        $csvContent = '';
        
        if (!empty($headers)) {
            $csvContent .= implode(',', array_map([$this, 'escapeCsvValue'], $headers)) . "\n";
        }
        
        foreach ($data as $row) {
            if (is_array($row) || is_object($row)) {
                $row = (array) $row;
                $csvContent .= implode(',', array_map([$this, 'escapeCsvValue'], $row)) . "\n";
            }
        }
        
        $filePath = 'exports/' . $filename;
        Storage::put($filePath, $csvContent);
        
        return [
            'file_path' => $filePath,
            'download_url' => Storage::url($filePath),
            'file_size' => Storage::size($filePath),
        ];
    }

    /**
     * Export data to JSON format
     */
    public function exportToJson($data, $filename)
    {
        $jsonContent = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        
        $filePath = 'exports/' . $filename;
        Storage::put($filePath, $jsonContent);
        
        return [
            'file_path' => $filePath,
            'download_url' => Storage::url($filePath),
            'file_size' => Storage::size($filePath),
        ];
    }

    /**
     * Export chat sessions data
     */
    public function exportChatSessions($siteId = null, $startDate = null, $endDate = null, $format = 'csv')
    {
        $query = ChatSession::with(['visitor', 'agent', 'site']);
        
        if ($siteId) {
            $query->where('site_id', $siteId);
        }
        
        if ($startDate && $endDate) {
            $query->whereBetween('started_at', [$startDate, $endDate]);
        }
        
        $sessions = $query->orderBy('started_at', 'desc')->get();
        
        $exportData = $sessions->map(function ($session) {
            return [
                'session_id' => $session->session_id,
                'site_name' => $session->site->name ?? 'Unknown',
                'visitor_name' => $session->visitor_name,
                'visitor_email' => $session->visitor_email,
                'agent_name' => $session->agent->name ?? 'Unassigned',
                'status' => $session->status,
                'started_at' => $session->started_at->toDateTimeString(),
                'ended_at' => $session->ended_at?->toDateTimeString(),
                'first_response_at' => $session->first_response_at?->toDateTimeString(),
                'duration_seconds' => $session->ended_at 
                    ? $session->started_at->diffInSeconds($session->ended_at) 
                    : null,
                'response_time_seconds' => $session->first_response_at 
                    ? $session->started_at->diffInSeconds($session->first_response_at) 
                    : null,
                'satisfaction_rating' => $session->satisfaction_rating,
                'satisfaction_comment' => $session->satisfaction_comment,
                'tags' => is_array($session->tags) ? implode(', ', $session->tags) : $session->tags,
                'created_at' => $session->created_at->toDateTimeString(),
            ];
        });
        
        $siteName = $siteId ? Site::find($siteId)?->name ?? 'site' : 'all-sites';
        $dateRange = $startDate && $endDate 
            ? Carbon::parse($startDate)->format('Y-m-d') . '_to_' . Carbon::parse($endDate)->format('Y-m-d')
            : 'all-time';
        $filename = "chat_sessions_{$siteName}_{$dateRange}." . $format;
        
        if ($format === 'csv') {
            $headers = [
                'Session ID', 'Site Name', 'Visitor Name', 'Visitor Email', 'Agent Name',
                'Status', 'Started At', 'Ended At', 'First Response At', 'Duration (seconds)',
                'Response Time (seconds)', 'Satisfaction Rating', 'Satisfaction Comment',
                'Tags', 'Created At'
            ];
            return $this->exportToCsv($exportData, $filename, $headers);
        } else {
            return $this->exportToJson($exportData, $filename);
        }
    }

    /**
     * Export chat messages data
     */
    public function exportChatMessages($sessionId = null, $siteId = null, $startDate = null, $endDate = null, $format = 'csv')
    {
        $query = ChatMessage::with(['session', 'session.site']);
        
        if ($sessionId) {
            $query->where('session_id', $sessionId);
        }
        
        if ($siteId) {
            $query->whereHas('session', function ($q) use ($siteId) {
                $q->where('site_id', $siteId);
            });
        }
        
        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        }
        
        $messages = $query->orderBy('created_at', 'desc')->get();
        
        $exportData = $messages->map(function ($message) {
            return [
                'message_id' => $message->message_id,
                'session_id' => $message->session->session_id,
                'site_name' => $message->session->site->name ?? 'Unknown',
                'sender_type' => $message->sender_type,
                'sender_name' => $message->sender_name,
                'message_type' => $message->message_type,
                'content' => $message->content,
                'file_url' => $message->file_url,
                'file_name' => $message->file_name,
                'file_size' => $message->file_size,
                'is_read' => $message->is_read ? 'Yes' : 'No',
                'read_at' => $message->read_at?->toDateTimeString(),
                'created_at' => $message->created_at->toDateTimeString(),
            ];
        });
        
        $siteName = $siteId ? Site::find($siteId)?->name ?? 'site' : 'all-sites';
        $dateRange = $startDate && $endDate 
            ? Carbon::parse($startDate)->format('Y-m-d') . '_to_' . Carbon::parse($endDate)->format('Y-m-d')
            : 'all-time';
        $filename = "chat_messages_{$siteName}_{$dateRange}." . $format;
        
        if ($format === 'csv') {
            $headers = [
                'Message ID', 'Session ID', 'Site Name', 'Sender Type', 'Sender Name',
                'Message Type', 'Content', 'File URL', 'File Name', 'File Size',
                'Is Read', 'Read At', 'Created At'
            ];
            return $this->exportToCsv($exportData, $filename, $headers);
        } else {
            return $this->exportToJson($exportData, $filename);
        }
    }

    /**
     * Export visitor analytics data
     */
    public function exportVisitorAnalytics($siteId = null, $startDate = null, $endDate = null, $format = 'csv')
    {
        $query = VisitorTracking::with(['visitor', 'site']);
        
        if ($siteId) {
            $query->where('site_id', $siteId);
        }
        
        if ($startDate && $endDate) {
            $query->whereBetween('visited_at', [$startDate, $endDate]);
        }
        
        $trackings = $query->orderBy('visited_at', 'desc')->get();
        
        $exportData = $trackings->map(function ($tracking) {
            return [
                'tracking_id' => $tracking->tracking_id,
                'site_name' => $tracking->site->name ?? 'Unknown',
                'visitor_id' => $tracking->visitor->visitor_id ?? 'Unknown',
                'session_id' => $tracking->session_id,
                'page_url' => $tracking->page_url,
                'page_title' => $tracking->page_title,
                'referrer' => $tracking->referrer,
                'utm_source' => $tracking->utm_source,
                'utm_medium' => $tracking->utm_medium,
                'utm_campaign' => $tracking->utm_campaign,
                'visit_duration' => $tracking->visit_duration,
                'page_views' => $tracking->page_views,
                'scroll_depth' => $tracking->scroll_depth,
                'clicks' => $tracking->clicks,
                'form_interactions' => $tracking->form_interactions,
                'bounce' => $tracking->bounce ? 'Yes' : 'No',
                'country' => $tracking->country,
                'city' => $tracking->city,
                'device_type' => $tracking->device_info['type'] ?? 'Unknown',
                'browser' => $tracking->browser_info['name'] ?? 'Unknown',
                'visited_at' => $tracking->visited_at?->toDateTimeString(),
                'left_at' => $tracking->left_at?->toDateTimeString(),
            ];
        });
        
        $siteName = $siteId ? Site::find($siteId)?->name ?? 'site' : 'all-sites';
        $dateRange = $startDate && $endDate 
            ? Carbon::parse($startDate)->format('Y-m-d') . '_to_' . Carbon::parse($endDate)->format('Y-m-d')
            : 'all-time';
        $filename = "visitor_analytics_{$siteName}_{$dateRange}." . $format;
        
        if ($format === 'csv') {
            $headers = [
                'Tracking ID', 'Site Name', 'Visitor ID', 'Session ID', 'Page URL', 'Page Title',
                'Referrer', 'UTM Source', 'UTM Medium', 'UTM Campaign', 'Visit Duration',
                'Page Views', 'Scroll Depth', 'Clicks', 'Form Interactions', 'Bounce',
                'Country', 'City', 'Device Type', 'Browser', 'Visited At', 'Left At'
            ];
            return $this->exportToCsv($exportData, $filename, $headers);
        } else {
            return $this->exportToJson($exportData, $filename);
        }
    }

    /**
     * Export comprehensive report
     */
    public function exportComprehensiveReport($siteId = null, $startDate = null, $endDate = null, $format = 'json')
    {
        $statisticsService = app(StatisticsService::class);
        $analyticsService = app(AnalyticsService::class);
        
        $report = [
            'report_info' => [
                'site_id' => $siteId,
                'site_name' => $siteId ? Site::find($siteId)?->name : 'All Sites',
                'start_date' => $startDate,
                'end_date' => $endDate,
                'generated_at' => now()->toISOString(),
            ],
            'dashboard_overview' => $statisticsService->getDashboardOverview($siteId, $startDate, $endDate),
            'session_statistics' => $statisticsService->getSessionStatistics($siteId, 'day', $startDate, $endDate),
            'agent_performance' => $statisticsService->getAgentPerformance($siteId, $startDate, $endDate),
            'response_time_distribution' => $statisticsService->getResponseTimeDistribution($siteId, $startDate, $endDate),
            'satisfaction_distribution' => $statisticsService->getSatisfactionDistribution($siteId, $startDate, $endDate),
            'message_volume' => $statisticsService->getMessageVolumeStats($siteId, 'day', $startDate, $endDate),
            'conversion_funnel' => $statisticsService->getVisitorConversionFunnel($siteId, $startDate, $endDate),
            'peak_hours' => $statisticsService->getPeakHoursAnalysis($siteId, $startDate, $endDate),
            'visitor_overview' => $analyticsService->getVisitorOverview($siteId, $startDate, $endDate),
            'device_stats' => $analyticsService->getDeviceStats($siteId, $startDate, $endDate),
            'geographic_stats' => $analyticsService->getGeographicStats($siteId, $startDate, $endDate),
            'engagement_metrics' => $analyticsService->getEngagementMetrics($siteId, $startDate, $endDate),
        ];
        
        $siteName = $siteId ? Site::find($siteId)?->name ?? 'site' : 'all-sites';
        $dateRange = $startDate && $endDate 
            ? Carbon::parse($startDate)->format('Y-m-d') . '_to_' . Carbon::parse($endDate)->format('Y-m-d')
            : 'all-time';
        $filename = "comprehensive_report_{$siteName}_{$dateRange}." . $format;
        
        return $this->exportToJson($report, $filename);
    }

    /**
     * Escape CSV values
     */
    protected function escapeCsvValue($value)
    {
        if ($value === null) {
            return '';
        }
        
        $value = (string) $value;
        
        // If value contains comma, quote, or newline, wrap in quotes and escape quotes
        if (strpos($value, ',') !== false || strpos($value, '"') !== false || strpos($value, "\n") !== false) {
            $value = '"' . str_replace('"', '""', $value) . '"';
        }
        
        return $value;
    }

    /**
     * Clean up old export files
     */
    public function cleanupOldExports($daysOld = 7)
    {
        $files = Storage::files('exports');
        $cutoffTime = now()->subDays($daysOld);
        
        $deletedCount = 0;
        
        foreach ($files as $file) {
            $lastModified = Carbon::createFromTimestamp(Storage::lastModified($file));
            
            if ($lastModified->lt($cutoffTime)) {
                Storage::delete($file);
                $deletedCount++;
            }
        }
        
        return $deletedCount;
    }
}

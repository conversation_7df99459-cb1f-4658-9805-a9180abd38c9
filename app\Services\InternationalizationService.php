<?php

namespace App\Services;

use App\Models\Language;
use App\Models\Translation;
use App\Models\Site;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class InternationalizationService
{
    protected $cachePrefix = 'i18n';
    protected $cacheTtl = 3600; // 1 hour

    /**
     * Get all languages for a site
     */
    public function getLanguagesForSite($siteId, $activeOnly = false)
    {
        $cacheKey = "{$this->cachePrefix}.languages.site.{$siteId}." . ($activeOnly ? 'active' : 'all');
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($siteId, $activeOnly) {
            $query = Language::where('site_id', $siteId);
            
            if ($activeOnly) {
                $query->active();
            }
            
            return $query->orderBy('is_default', 'desc')
                ->orderBy('name')
                ->get();
        });
    }

    /**
     * Get default language for a site
     */
    public function getDefaultLanguage($siteId)
    {
        $cacheKey = "{$this->cachePrefix}.default_language.site.{$siteId}";
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($siteId) {
            return Language::where('site_id', $siteId)
                ->default()
                ->active()
                ->first();
        });
    }

    /**
     * Create a new language for a site
     */
    public function createLanguage($siteId, array $data)
    {
        try {
            // Get available language data
            $availableLanguages = Language::getAvailableLanguages();
            $languageData = $availableLanguages[$data['code']] ?? [];

            // Merge with provided data
            $languageData = array_merge($languageData, $data);
            $languageData['site_id'] = $siteId;
            $languageData['language_id'] = (string) Str::uuid();

            // Create language
            $language = Language::create($languageData);

            // If this is the first language for the site, make it default
            $languageCount = Language::where('site_id', $siteId)->count();
            if ($languageCount === 1 || ($data['is_default'] ?? false)) {
                $language->setAsDefault();
            }

            // Create default system translations
            $this->createDefaultTranslations($language);

            // Clear cache
            $this->clearLanguageCache($siteId);

            return $language;

        } catch (\Exception $e) {
            Log::error('Failed to create language', [
                'site_id' => $siteId,
                'data' => $data,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Update language
     */
    public function updateLanguage($languageId, array $data)
    {
        try {
            $language = Language::where('language_id', $languageId)->firstOrFail();
            
            $language->update($data);

            // Handle default language change
            if ($data['is_default'] ?? false) {
                $language->setAsDefault();
            }

            // Clear cache
            $this->clearLanguageCache($language->site_id);

            return $language;

        } catch (\Exception $e) {
            Log::error('Failed to update language', [
                'language_id' => $languageId,
                'data' => $data,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Delete language
     */
    public function deleteLanguage($languageId)
    {
        try {
            $language = Language::where('language_id', $languageId)->firstOrFail();
            
            // Cannot delete default language
            if ($language->is_default) {
                throw new \Exception('Cannot delete default language');
            }

            $siteId = $language->site_id;
            
            // Delete translations
            Translation::where('language_id', $language->id)->delete();
            
            // Delete language
            $language->delete();

            // Clear cache
            $this->clearLanguageCache($siteId);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to delete language', [
                'language_id' => $languageId,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Get translation
     */
    public function getTranslation($siteId, $languageCode, $key, $default = null, array $replacements = [])
    {
        try {
            // Get language
            $language = $this->getLanguageByCode($siteId, $languageCode);
            if (!$language) {
                $language = $this->getDefaultLanguage($siteId);
            }

            if (!$language) {
                return $default ?? $key;
            }

            // Parse key
            $keyParts = Translation::parseKey($key);
            
            // Get translation from cache
            $cacheKey = "{$this->cachePrefix}.translation.{$language->id}.{$key}";
            
            $translation = Cache::remember($cacheKey, $this->cacheTtl, function () use ($language, $keyParts) {
                return Translation::where('language_id', $language->id)
                    ->where('namespace', $keyParts['namespace'])
                    ->where('group', $keyParts['group'])
                    ->where('key', $keyParts['key'])
                    ->approved()
                    ->first();
            });

            $value = $translation ? $translation->value : ($default ?? $key);

            // Apply replacements
            if (!empty($replacements)) {
                foreach ($replacements as $search => $replace) {
                    $value = str_replace(':' . $search, $replace, $value);
                }
            }

            return $value;

        } catch (\Exception $e) {
            Log::error('Failed to get translation', [
                'site_id' => $siteId,
                'language_code' => $languageCode,
                'key' => $key,
                'error' => $e->getMessage(),
            ]);
            return $default ?? $key;
        }
    }

    /**
     * Get all translations for a language
     */
    public function getAllTranslations($siteId, $languageCode, $namespace = null, $group = null)
    {
        try {
            $language = $this->getLanguageByCode($siteId, $languageCode);
            if (!$language) {
                return [];
            }

            $cacheKey = "{$this->cachePrefix}.all_translations.{$language->id}." . 
                       ($namespace ?? 'null') . '.' . ($group ?? 'null');
            
            return Cache::remember($cacheKey, $this->cacheTtl, function () use ($language, $namespace, $group) {
                $query = Translation::where('language_id', $language->id)->approved();
                
                if ($namespace !== null) {
                    $query->where('namespace', $namespace);
                }
                
                if ($group !== null) {
                    $query->where('group', $group);
                }
                
                $translations = $query->get();
                
                $result = [];
                foreach ($translations as $translation) {
                    $fullKey = $translation->getFullKey();
                    $result[$fullKey] = $translation->value;
                }
                
                return $result;
            });

        } catch (\Exception $e) {
            Log::error('Failed to get all translations', [
                'site_id' => $siteId,
                'language_code' => $languageCode,
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * Set translation
     */
    public function setTranslation($siteId, $languageCode, $key, $value, $context = null, $isSystem = false)
    {
        try {
            $language = $this->getLanguageByCode($siteId, $languageCode);
            if (!$language) {
                throw new \Exception("Language not found: {$languageCode}");
            }

            $keyParts = Translation::parseKey($key);

            // Find existing translation or create new
            $translation = Translation::where('language_id', $language->id)
                ->where('namespace', $keyParts['namespace'])
                ->where('group', $keyParts['group'])
                ->where('key', $keyParts['key'])
                ->first();

            if ($translation) {
                $translation->update([
                    'value' => $value,
                    'context' => $context,
                    'is_approved' => $isSystem, // Auto-approve system translations
                ]);
            } else {
                $translation = Translation::create([
                    'translation_id' => (string) Str::uuid(),
                    'language_id' => $language->id,
                    'namespace' => $keyParts['namespace'],
                    'group' => $keyParts['group'],
                    'key' => $keyParts['key'],
                    'value' => $value,
                    'context' => $context,
                    'is_system' => $isSystem,
                    'is_approved' => $isSystem,
                ]);
            }

            // Clear cache
            $this->clearTranslationCache($language->id, $key);

            return $translation;

        } catch (\Exception $e) {
            Log::error('Failed to set translation', [
                'site_id' => $siteId,
                'language_code' => $languageCode,
                'key' => $key,
                'value' => $value,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Bulk set translations
     */
    public function bulkSetTranslations($siteId, $languageCode, array $translations, $isSystem = false)
    {
        try {
            $language = $this->getLanguageByCode($siteId, $languageCode);
            if (!$language) {
                throw new \Exception("Language not found: {$languageCode}");
            }

            $created = 0;
            $updated = 0;

            foreach ($translations as $key => $value) {
                $keyParts = Translation::parseKey($key);

                $translation = Translation::where('language_id', $language->id)
                    ->where('namespace', $keyParts['namespace'])
                    ->where('group', $keyParts['group'])
                    ->where('key', $keyParts['key'])
                    ->first();

                if ($translation) {
                    $translation->update([
                        'value' => $value,
                        'is_approved' => $isSystem,
                    ]);
                    $updated++;
                } else {
                    Translation::create([
                        'translation_id' => (string) Str::uuid(),
                        'language_id' => $language->id,
                        'namespace' => $keyParts['namespace'],
                        'group' => $keyParts['group'],
                        'key' => $keyParts['key'],
                        'value' => $value,
                        'is_system' => $isSystem,
                        'is_approved' => $isSystem,
                    ]);
                    $created++;
                }
            }

            // Clear all translation cache for this language
            $this->clearAllTranslationCache($language->id);

            return [
                'created' => $created,
                'updated' => $updated,
                'total' => count($translations),
            ];

        } catch (\Exception $e) {
            Log::error('Failed to bulk set translations', [
                'site_id' => $siteId,
                'language_code' => $languageCode,
                'count' => count($translations),
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Get language by code
     */
    public function getLanguageByCode($siteId, $code)
    {
        $cacheKey = "{$this->cachePrefix}.language_by_code.{$siteId}.{$code}";
        
        return Cache::remember($cacheKey, $this->cacheTtl, function () use ($siteId, $code) {
            return Language::where('site_id', $siteId)
                ->where('code', $code)
                ->active()
                ->first();
        });
    }

    /**
     * Create default system translations for a language
     */
    protected function createDefaultTranslations(Language $language)
    {
        $defaultTranslations = Translation::getDefaultSystemTranslations();
        
        foreach ($defaultTranslations as $group => $translations) {
            foreach ($translations as $key => $value) {
                Translation::create([
                    'translation_id' => (string) Str::uuid(),
                    'language_id' => $language->id,
                    'namespace' => null,
                    'group' => $group,
                    'key' => $key,
                    'value' => $value,
                    'is_system' => true,
                    'is_approved' => true,
                ]);
            }
        }
    }

    /**
     * Clear language cache
     */
    protected function clearLanguageCache($siteId)
    {
        $patterns = [
            "{$this->cachePrefix}.languages.site.{$siteId}.*",
            "{$this->cachePrefix}.default_language.site.{$siteId}",
            "{$this->cachePrefix}.language_by_code.{$siteId}.*",
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    /**
     * Clear translation cache
     */
    protected function clearTranslationCache($languageId, $key)
    {
        $cacheKey = "{$this->cachePrefix}.translation.{$languageId}.{$key}";
        Cache::forget($cacheKey);
    }

    /**
     * Clear all translation cache for a language
     */
    protected function clearAllTranslationCache($languageId)
    {
        $pattern = "{$this->cachePrefix}.*{$languageId}.*";
        Cache::forget($pattern);
    }

    /**
     * Export translations
     */
    public function exportTranslations($siteId, $languageCode, $format = 'json')
    {
        $translations = $this->getAllTranslations($siteId, $languageCode);
        $language = $this->getLanguageByCode($siteId, $languageCode);

        $exportData = [
            'language' => [
                'code' => $language->code,
                'name' => $language->name,
                'native_name' => $language->native_name,
                'is_rtl' => $language->is_rtl,
            ],
            'translations' => $translations,
            'exported_at' => now()->toISOString(),
        ];

        switch ($format) {
            case 'json':
                return json_encode($exportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            case 'php':
                return "<?php\n\nreturn " . var_export($exportData, true) . ";\n";
            default:
                throw new \Exception("Unsupported export format: {$format}");
        }
    }

    /**
     * Import translations
     */
    public function importTranslations($siteId, $languageCode, $data, $overwrite = false)
    {
        if (is_string($data)) {
            $data = json_decode($data, true);
        }

        if (!isset($data['translations'])) {
            throw new \Exception('Invalid import data format');
        }

        $translations = $data['translations'];

        if ($overwrite) {
            return $this->bulkSetTranslations($siteId, $languageCode, $translations, false);
        } else {
            // Only import missing translations
            $existingKeys = array_keys($this->getAllTranslations($siteId, $languageCode));
            $newTranslations = array_diff_key($translations, array_flip($existingKeys));

            return $this->bulkSetTranslations($siteId, $languageCode, $newTranslations, false);
        }
    }

    /**
     * Auto-translate using external service (placeholder for future implementation)
     */
    public function autoTranslate($siteId, $sourceLanguageCode, $targetLanguageCode, $keys = null)
    {
        // This is a placeholder for future integration with translation services
        // like Google Translate API, Microsoft Translator, etc.

        throw new \Exception('Auto-translation feature not implemented yet');
    }

    /**
     * Get translation statistics
     */
    public function getTranslationStats($siteId, $languageCode = null)
    {
        try {
            if ($languageCode) {
                $language = $this->getLanguageByCode($siteId, $languageCode);
                if (!$language) {
                    throw new \Exception("Language not found: {$languageCode}");
                }

                $total = Translation::where('language_id', $language->id)->count();
                $approved = Translation::where('language_id', $language->id)->approved()->count();
                $system = Translation::where('language_id', $language->id)->system()->count();
                $custom = Translation::where('language_id', $language->id)->custom()->count();

                return [
                    'language' => $language->code,
                    'total_translations' => $total,
                    'approved_translations' => $approved,
                    'pending_translations' => $total - $approved,
                    'system_translations' => $system,
                    'custom_translations' => $custom,
                    'completion_percentage' => $total > 0 ? round(($approved / $total) * 100, 2) : 0,
                ];
            } else {
                // Get stats for all languages in the site
                $languages = $this->getLanguagesForSite($siteId, true);
                $stats = [];

                foreach ($languages as $language) {
                    $stats[] = $this->getTranslationStats($siteId, $language->code);
                }

                return $stats;
            }

        } catch (\Exception $e) {
            Log::error('Failed to get translation stats', [
                'site_id' => $siteId,
                'language_code' => $languageCode,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Models\Site;
use App\Models\User;
use App\Models\WorkingHours;
use App\Models\Theme;
use App\Models\Language;
use App\Services\ConfigurationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class SystemConfigController extends AdminController
{
    protected $configService;

    public function __construct(ConfigurationService $configService)
    {
        parent::__construct();
        $this->configService = $configService;
    }

    /**
     * Get site configuration
     */
    public function getSiteConfig(Request $request): JsonResponse
    {
        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'view');
            if ($error) return $error;

            $site = Site::with([
                'workingHours',
                'theme',
                'languages' => function($query) {
                    $query->where('is_active', true);
                }
            ])->find($siteId);

            if (!$site) {
                return $this->errorResponse('Site not found', 404);
            }

            return $this->successResponse($site);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get site config: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update site configuration
     */
    public function updateSiteConfig(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'domain' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'contact_email' => 'required|email|max:255',
            'timezone' => 'required|string|max:50',
            'language' => 'required|string|max:10',
            'currency' => 'required|string|max:10',
            'settings' => 'nullable|array',
        ]);

        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'manage');
            if ($error) return $error;

            $site = Site::find($siteId);
            if (!$site) {
                return $this->errorResponse('Site not found', 404);
            }

            $site->update([
                'name' => $request->name,
                'domain' => $request->domain,
                'description' => $request->description,
                'contact_email' => $request->contact_email,
                'timezone' => $request->timezone,
                'language' => $request->language,
                'currency' => $request->currency,
                'settings' => array_merge($site->settings ?? [], $request->settings ?? []),
            ]);

            return $this->successResponse($site, 'Site configuration updated successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to update site config: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get chat widget configuration
     */
    public function getChatWidgetConfig(Request $request): JsonResponse
    {
        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'view');
            if ($error) return $error;

            $config = $this->configService->getChatWidgetConfig($siteId);

            return $this->successResponse($config);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get chat widget config: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update chat widget configuration
     */
    public function updateChatWidgetConfig(Request $request): JsonResponse
    {
        $request->validate([
            'enabled' => 'required|boolean',
            'position' => 'required|string|in:bottom-right,bottom-left,top-right,top-left',
            'theme_color' => 'required|string|max:7',
            'welcome_message' => 'required|string|max:500',
            'offline_message' => 'required|string|max:500',
            'show_agent_avatar' => 'required|boolean',
            'show_typing_indicator' => 'required|boolean',
            'enable_file_upload' => 'required|boolean',
            'enable_emoji' => 'required|boolean',
            'enable_sound' => 'required|boolean',
            'auto_open' => 'required|boolean',
            'auto_open_delay' => 'required|integer|min:0|max:60',
            'max_file_size' => 'required|integer|min:1|max:50',
            'allowed_file_types' => 'required|array',
            'pre_chat_form' => 'required|array',
            'post_chat_survey' => 'required|array',
        ]);

        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'manage');
            if ($error) return $error;

            $config = $this->configService->updateChatWidgetConfig($siteId, $request->all());

            return $this->successResponse($config, 'Chat widget configuration updated successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to update chat widget config: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get working hours configuration
     */
    public function getWorkingHours(Request $request): JsonResponse
    {
        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'view');
            if ($error) return $error;

            $workingHours = WorkingHours::where('site_id', $siteId)->get();

            return $this->successResponse($workingHours);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get working hours: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update working hours configuration
     */
    public function updateWorkingHours(Request $request): JsonResponse
    {
        $request->validate([
            'working_hours' => 'required|array',
            'working_hours.*.day_of_week' => 'required|integer|min:0|max:6',
            'working_hours.*.is_enabled' => 'required|boolean',
            'working_hours.*.start_time' => 'required_if:working_hours.*.is_enabled,true|nullable|date_format:H:i',
            'working_hours.*.end_time' => 'required_if:working_hours.*.is_enabled,true|nullable|date_format:H:i',
            'working_hours.*.break_start' => 'nullable|date_format:H:i',
            'working_hours.*.break_end' => 'nullable|date_format:H:i',
        ]);

        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'manage');
            if ($error) return $error;

            // Delete existing working hours
            WorkingHours::where('site_id', $siteId)->delete();

            // Create new working hours
            foreach ($request->working_hours as $hours) {
                WorkingHours::create([
                    'site_id' => $siteId,
                    'day_of_week' => $hours['day_of_week'],
                    'is_enabled' => $hours['is_enabled'],
                    'start_time' => $hours['start_time'] ?? null,
                    'end_time' => $hours['end_time'] ?? null,
                    'break_start' => $hours['break_start'] ?? null,
                    'break_end' => $hours['break_end'] ?? null,
                ]);
            }

            $workingHours = WorkingHours::where('site_id', $siteId)->get();

            return $this->successResponse($workingHours, 'Working hours updated successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to update working hours: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get user management data
     */
    public function getUsers(Request $request): JsonResponse
    {
        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'view');
            if ($error) return $error;

            $perPage = $request->get('per_page', 15);
            $search = $request->get('search');
            $role = $request->get('role');
            $status = $request->get('status');

            $query = User::where('site_id', $siteId);

            if ($search) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%");
                });
            }

            if ($role) {
                $query->where('role', $role);
            }

            if ($status) {
                $query->where('status', $status);
            }

            $users = $query->orderBy('created_at', 'desc')->paginate($perPage);

            return $this->paginatedResponse($users);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get users: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Create new user
     */
    public function createUser(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:st_users,email',
            'password' => 'required|string|min:8',
            'role' => 'required|string|in:admin,supervisor,agent',
            'status' => 'required|string|in:online,offline,away,busy',
            'max_concurrent_chats' => 'nullable|integer|min:1|max:20',
            'skills' => 'nullable|array',
            'languages' => 'nullable|array',
        ]);

        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'manage');
            if ($error) return $error;

            $user = User::create([
                'user_id' => \Str::uuid(),
                'site_id' => $siteId,
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'role' => $request->role,
                'status' => $request->status,
                'max_concurrent_chats' => $request->max_concurrent_chats ?? 5,
                'skills' => $request->skills ?? [],
                'languages' => $request->languages ?? [],
                'is_active' => true,
            ]);

            return $this->successResponse($user, 'User created successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to create user: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Update user
     */
    public function updateUser(Request $request, $userId): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('st_users', 'email')->ignore($userId, 'user_id')],
            'role' => 'required|string|in:admin,supervisor,agent',
            'status' => 'required|string|in:online,offline,away,busy',
            'max_concurrent_chats' => 'nullable|integer|min:1|max:20',
            'skills' => 'nullable|array',
            'languages' => 'nullable|array',
            'is_active' => 'required|boolean',
        ]);

        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'manage');
            if ($error) return $error;

            $user = User::where('user_id', $userId)
                ->where('site_id', $siteId)
                ->first();

            if (!$user) {
                return $this->errorResponse('User not found', 404);
            }

            $user->update([
                'name' => $request->name,
                'email' => $request->email,
                'role' => $request->role,
                'status' => $request->status,
                'max_concurrent_chats' => $request->max_concurrent_chats ?? 5,
                'skills' => $request->skills ?? [],
                'languages' => $request->languages ?? [],
                'is_active' => $request->is_active,
            ]);

            return $this->successResponse($user, 'User updated successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to update user: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Delete user
     */
    public function deleteUser(Request $request, $userId): JsonResponse
    {
        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'manage');
            if ($error) return $error;

            $user = User::where('user_id', $userId)
                ->where('site_id', $siteId)
                ->first();

            if (!$user) {
                return $this->errorResponse('User not found', 404);
            }

            // Check if user has active sessions
            $activeSessions = \App\Models\ChatSession::where('agent_id', $user->id)
                ->where('status', 'active')
                ->count();

            if ($activeSessions > 0) {
                return $this->errorResponse('Cannot delete user with active chat sessions', 400);
            }

            $user->delete();

            return $this->successResponse(null, 'User deleted successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to delete user: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Upload site logo
     */
    public function uploadLogo(Request $request): JsonResponse
    {
        $request->validate([
            'logo' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);

        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'manage');
            if ($error) return $error;

            $site = Site::find($siteId);
            if (!$site) {
                return $this->errorResponse('Site not found', 404);
            }

            // Delete old logo if exists
            if ($site->logo && Storage::disk('public')->exists($site->logo)) {
                Storage::disk('public')->delete($site->logo);
            }

            // Store new logo
            $logoPath = $request->file('logo')->store('logos', 'public');

            $site->update(['logo' => $logoPath]);

            return $this->successResponse([
                'logo_url' => Storage::url($logoPath),
                'logo_path' => $logoPath,
            ], 'Logo uploaded successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to upload logo: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get system settings
     */
    public function getSystemSettings(Request $request): JsonResponse
    {
        try {
            $error = $this->validateSiteAccess($request, 'view');
            if ($error) return $error;

            $settings = [
                'app_name' => config('app.name'),
                'app_version' => '1.0.0',
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
                'database_version' => $this->getDatabaseVersion(),
                'redis_version' => $this->getRedisVersion(),
                'storage_info' => $this->getStorageInfo(),
                'cache_info' => $this->getCacheInfo(),
                'queue_info' => $this->getQueueInfo(),
            ];

            return $this->successResponse($settings);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get system settings: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get database version
     */
    private function getDatabaseVersion()
    {
        try {
            $result = \DB::select('SELECT VERSION() as version');
            return $result[0]->version ?? 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Get Redis version
     */
    private function getRedisVersion()
    {
        try {
            $info = \Redis::info();
            return $info['redis_version'] ?? 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Get storage information
     */
    private function getStorageInfo()
    {
        try {
            $path = storage_path();
            $totalSpace = disk_total_space($path);
            $freeSpace = disk_free_space($path);
            $usedSpace = $totalSpace - $freeSpace;

            return [
                'total_space' => $this->formatBytes($totalSpace),
                'used_space' => $this->formatBytes($usedSpace),
                'free_space' => $this->formatBytes($freeSpace),
                'usage_percentage' => round(($usedSpace / $totalSpace) * 100, 2),
            ];
        } catch (\Exception $e) {
            return ['error' => 'Unable to get storage info'];
        }
    }

    /**
     * Get cache information
     */
    private function getCacheInfo()
    {
        try {
            return [
                'driver' => config('cache.default'),
                'status' => 'Connected',
            ];
        } catch (\Exception $e) {
            return ['error' => 'Unable to get cache info'];
        }
    }

    /**
     * Get queue information
     */
    private function getQueueInfo()
    {
        try {
            $failedJobs = \DB::table('failed_jobs')->count();
            
            return [
                'driver' => config('queue.default'),
                'failed_jobs' => $failedJobs,
                'status' => $failedJobs === 0 ? 'Healthy' : 'Warning',
            ];
        } catch (\Exception $e) {
            return ['error' => 'Unable to get queue info'];
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

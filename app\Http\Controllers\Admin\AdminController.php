<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class AdminController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->middleware('auth:sanctum');
        $this->middleware('admin');
    }

    /**
     * Get current admin user info
     */
    protected function getCurrentAdmin()
    {
        return Auth::user();
    }

    /**
     * Get current site ID
     */
    protected function getCurrentSiteId(Request $request)
    {
        // Try to get site ID from various sources
        return $request->route('siteId') ?? 
               $request->get('site_id') ?? 
               $request->header('X-Site-ID') ?? 
               $this->getCurrentAdmin()->site_id ?? 
               null;
    }

    /**
     * Check if user has permission for site
     */
    protected function checkSitePermission($siteId, $permission = 'view')
    {
        $user = $this->getCurrentAdmin();
        
        // Super admin has access to all sites
        if ($user->role === 'super_admin') {
            return true;
        }

        // Check site-specific permissions
        if ($user->site_id && $user->site_id != $siteId) {
            return false;
        }

        // Check role-based permissions
        $rolePermissions = [
            'admin' => ['view', 'create', 'update', 'delete', 'manage'],
            'supervisor' => ['view', 'create', 'update', 'manage'],
            'agent' => ['view'],
        ];

        return in_array($permission, $rolePermissions[$user->role] ?? []);
    }

    /**
     * Standard success response
     */
    protected function successResponse($data = null, $message = null, $code = 200)
    {
        $response = ['success' => true];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        if ($message !== null) {
            $response['message'] = $message;
        }

        return response()->json($response, $code);
    }

    /**
     * Standard error response
     */
    protected function errorResponse($message, $code = 400, $errors = null)
    {
        $response = [
            'success' => false,
            'message' => $message,
        ];
        
        if ($errors !== null) {
            $response['errors'] = $errors;
        }

        return response()->json($response, $code);
    }

    /**
     * Paginated response
     */
    protected function paginatedResponse($paginator, $message = null)
    {
        return response()->json([
            'success' => true,
            'data' => $paginator->items(),
            'pagination' => [
                'current_page' => $paginator->currentPage(),
                'last_page' => $paginator->lastPage(),
                'per_page' => $paginator->perPage(),
                'total' => $paginator->total(),
                'from' => $paginator->firstItem(),
                'to' => $paginator->lastItem(),
                'has_more_pages' => $paginator->hasMorePages(),
            ],
            'message' => $message,
        ]);
    }

    /**
     * Validate site access
     */
    protected function validateSiteAccess(Request $request, $permission = 'view')
    {
        $siteId = $this->getCurrentSiteId($request);
        
        if (!$siteId) {
            return $this->errorResponse('Site ID is required', 400);
        }

        if (!$this->checkSitePermission($siteId, $permission)) {
            return $this->errorResponse('Insufficient permissions', 403);
        }

        return null; // No error
    }

    /**
     * Get dashboard overview data
     */
    public function getDashboardOverview(Request $request): JsonResponse
    {
        try {
            $siteId = $this->getCurrentSiteId($request);
            $error = $this->validateSiteAccess($request, 'view');
            if ($error) return $error;

            // Get date range
            $startDate = $request->get('start_date', now()->subDays(30)->toDateString());
            $endDate = $request->get('end_date', now()->toDateString());

            $overview = [
                'total_sessions' => \App\Models\ChatSession::where('site_id', $siteId)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count(),
                
                'active_sessions' => \App\Models\ChatSession::where('site_id', $siteId)
                    ->where('status', 'active')
                    ->count(),
                
                'total_messages' => \App\Models\ChatMessage::whereHas('session', function($query) use ($siteId) {
                        $query->where('site_id', $siteId);
                    })
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count(),
                
                'online_agents' => \App\Models\User::where('site_id', $siteId)
                    ->where('status', 'online')
                    ->where('role', 'agent')
                    ->count(),
                
                'total_agents' => \App\Models\User::where('site_id', $siteId)
                    ->where('role', 'agent')
                    ->count(),
                
                'avg_response_time' => \App\Models\ChatMessage::whereHas('session', function($query) use ($siteId) {
                        $query->where('site_id', $siteId);
                    })
                    ->where('sender_type', 'agent')
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->avg('response_time') ?? 0,
                
                'satisfaction_rating' => \App\Models\ChatSession::where('site_id', $siteId)
                    ->whereNotNull('satisfaction_rating')
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->avg('satisfaction_rating') ?? 0,
                
                'total_visitors' => \App\Models\VisitorSession::where('site_id', $siteId)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->distinct('visitor_id')
                    ->count(),
                
                'conversion_rate' => $this->calculateConversionRate($siteId, $startDate, $endDate),
                
                'peak_hours' => $this->getPeakHours($siteId, $startDate, $endDate),
            ];

            return $this->successResponse($overview);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get dashboard overview: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Calculate conversion rate
     */
    private function calculateConversionRate($siteId, $startDate, $endDate)
    {
        $totalVisitors = \App\Models\VisitorSession::where('site_id', $siteId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->distinct('visitor_id')
            ->count();

        $chatVisitors = \App\Models\ChatSession::where('site_id', $siteId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->distinct('visitor_id')
            ->count();

        return $totalVisitors > 0 ? round(($chatVisitors / $totalVisitors) * 100, 2) : 0;
    }

    /**
     * Get peak hours data
     */
    private function getPeakHours($siteId, $startDate, $endDate)
    {
        $hourlyData = \App\Models\ChatSession::where('site_id', $siteId)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->selectRaw('HOUR(created_at) as hour, COUNT(*) as count')
            ->groupBy('hour')
            ->orderBy('count', 'desc')
            ->limit(3)
            ->get();

        return $hourlyData->map(function($item) {
            return [
                'hour' => $item->hour . ':00',
                'sessions' => $item->count,
            ];
        });
    }

    /**
     * Get system status
     */
    public function getSystemStatus(Request $request): JsonResponse
    {
        try {
            $error = $this->validateSiteAccess($request, 'view');
            if ($error) return $error;

            $status = [
                'database' => $this->checkDatabaseConnection(),
                'redis' => $this->checkRedisConnection(),
                'websocket' => $this->checkWebSocketStatus(),
                'storage' => $this->checkStorageStatus(),
                'queue' => $this->checkQueueStatus(),
                'cache' => $this->checkCacheStatus(),
            ];

            $overallStatus = collect($status)->every(function($service) {
                return $service['status'] === 'healthy';
            }) ? 'healthy' : 'degraded';

            return $this->successResponse([
                'overall_status' => $overallStatus,
                'services' => $status,
                'checked_at' => now()->toISOString(),
            ]);

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get system status: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Check database connection
     */
    private function checkDatabaseConnection()
    {
        try {
            \DB::connection()->getPdo();
            return [
                'status' => 'healthy',
                'message' => 'Database connection is working',
                'response_time' => $this->measureResponseTime(function() {
                    \DB::select('SELECT 1');
                }),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Database connection failed: ' . $e->getMessage(),
                'response_time' => null,
            ];
        }
    }

    /**
     * Check Redis connection
     */
    private function checkRedisConnection()
    {
        try {
            \Redis::ping();
            return [
                'status' => 'healthy',
                'message' => 'Redis connection is working',
                'response_time' => $this->measureResponseTime(function() {
                    \Redis::ping();
                }),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Redis connection failed: ' . $e->getMessage(),
                'response_time' => null,
            ];
        }
    }

    /**
     * Check WebSocket status
     */
    private function checkWebSocketStatus()
    {
        // This is a placeholder - you would implement actual WebSocket health check
        return [
            'status' => 'healthy',
            'message' => 'WebSocket server is running',
            'response_time' => 50,
        ];
    }

    /**
     * Check storage status
     */
    private function checkStorageStatus()
    {
        try {
            $diskSpace = disk_free_space(storage_path());
            $totalSpace = disk_total_space(storage_path());
            $usedPercentage = round((($totalSpace - $diskSpace) / $totalSpace) * 100, 2);

            return [
                'status' => $usedPercentage < 90 ? 'healthy' : 'warning',
                'message' => "Disk usage: {$usedPercentage}%",
                'disk_usage' => $usedPercentage,
                'free_space' => $this->formatBytes($diskSpace),
                'total_space' => $this->formatBytes($totalSpace),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Storage check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check queue status
     */
    private function checkQueueStatus()
    {
        try {
            // Check if there are failed jobs
            $failedJobs = \DB::table('failed_jobs')->count();
            
            return [
                'status' => $failedJobs === 0 ? 'healthy' : 'warning',
                'message' => $failedJobs === 0 ? 'No failed jobs' : "{$failedJobs} failed jobs",
                'failed_jobs' => $failedJobs,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Queue check failed: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Check cache status
     */
    private function checkCacheStatus()
    {
        try {
            $testKey = 'health_check_' . time();
            \Cache::put($testKey, 'test', 60);
            $value = \Cache::get($testKey);
            \Cache::forget($testKey);

            return [
                'status' => $value === 'test' ? 'healthy' : 'unhealthy',
                'message' => $value === 'test' ? 'Cache is working' : 'Cache test failed',
                'response_time' => $this->measureResponseTime(function() use ($testKey) {
                    \Cache::put($testKey, 'test', 60);
                    \Cache::get($testKey);
                }),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'message' => 'Cache check failed: ' . $e->getMessage(),
                'response_time' => null,
            ];
        }
    }

    /**
     * Measure response time
     */
    private function measureResponseTime(callable $callback)
    {
        $start = microtime(true);
        $callback();
        $end = microtime(true);
        return round(($end - $start) * 1000, 2); // Convert to milliseconds
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

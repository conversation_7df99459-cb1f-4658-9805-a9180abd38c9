<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ContactMessage;
use App\Models\Site;
use App\Mail\ContactMessageReceived;
use App\Mail\ContactMessageReply;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class ContactController extends Controller
{
    /**
     * Store a new contact message
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'priority' => 'nullable|in:low,medium,high,urgent',
            'message' => 'required|string|max:5000',
            'site_id' => 'nullable|exists:sites,id',
            'files.*' => 'nullable|file|mimes:jpeg,png,gif,pdf,doc,docx,txt|max:10240',
            'consent' => 'required|accepted'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Get site or use default
        $site = null;
        if ($request->site_id) {
            $site = Site::find($request->site_id);
        } else {
            $site = Site::where('is_default', true)->first();
        }

        // Create contact message
        $contactMessage = ContactMessage::create([
            'site_id' => $site ? $site->id : null,
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'subject' => $request->subject,
            'priority' => $request->priority ?? 'medium',
            'message' => $request->message,
            'status' => 'new',
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'submitted_at' => now()
        ]);

        // Handle file attachments
        $attachments = [];
        if ($request->hasFile('files')) {
            foreach ($request->file('files') as $file) {
                $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs('contact_attachments', $filename, 'private');
                
                $attachments[] = [
                    'original_name' => $file->getClientOriginalName(),
                    'stored_name' => $filename,
                    'path' => $path,
                    'size' => $file->getSize(),
                    'mime_type' => $file->getMimeType()
                ];
            }
            
            $contactMessage->update([
                'attachments' => json_encode($attachments)
            ]);
        }

        // Send notification email to admin
        try {
            if ($site && $site->contact_email) {
                Mail::to($site->contact_email)->send(new ContactMessageReceived($contactMessage));
            }
        } catch (\Exception $e) {
            \Log::error('Failed to send contact notification email: ' . $e->getMessage());
        }

        return response()->json([
            'success' => true,
            'message' => 'Your message has been sent successfully. We will get back to you soon.',
            'data' => [
                'id' => $contactMessage->id,
                'reference_number' => $contactMessage->reference_number
            ]
        ]);
    }

    /**
     * Get contact messages list (admin)
     */
    public function index(Request $request): JsonResponse
    {
        $query = ContactMessage::with('site');

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->has('site_id')) {
            $query->where('site_id', $request->site_id);
        }

        if ($request->has('date_from')) {
            $query->where('submitted_at', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->where('submitted_at', '<=', $request->date_to);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('subject', 'like', "%{$search}%")
                  ->orWhere('reference_number', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'submitted_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 20);
        $messages = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => [
                'messages' => $messages->items(),
                'pagination' => [
                    'current_page' => $messages->currentPage(),
                    'last_page' => $messages->lastPage(),
                    'per_page' => $messages->perPage(),
                    'total' => $messages->total()
                ],
                'stats' => [
                    'total' => ContactMessage::count(),
                    'new' => ContactMessage::where('status', 'new')->count(),
                    'in_progress' => ContactMessage::where('status', 'in_progress')->count(),
                    'resolved' => ContactMessage::where('status', 'resolved')->count(),
                    'closed' => ContactMessage::where('status', 'closed')->count()
                ]
            ]
        ]);
    }

    /**
     * Get contact message details
     */
    public function show(Request $request, int $contactId): JsonResponse
    {
        $message = ContactMessage::with('site')->find($contactId);

        if (!$message) {
            return response()->json([
                'success' => false,
                'message' => 'Contact message not found'
            ], 404);
        }

        // Parse attachments
        $attachments = [];
        if ($message->attachments) {
            $attachments = json_decode($message->attachments, true);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'message' => $message,
                'attachments' => $attachments
            ]
        ]);
    }

    /**
     * Update contact message status
     */
    public function updateStatus(Request $request, int $contactId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:new,in_progress,resolved,closed',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $message = ContactMessage::find($contactId);

        if (!$message) {
            return response()->json([
                'success' => false,
                'message' => 'Contact message not found'
            ], 404);
        }

        $updateData = [
            'status' => $request->status,
            'updated_by' => $request->user()->id,
            'updated_at' => now()
        ];

        if ($request->notes) {
            $updateData['internal_notes'] = $request->notes;
        }

        if ($request->status === 'resolved') {
            $updateData['resolved_at'] = now();
        } elseif ($request->status === 'closed') {
            $updateData['closed_at'] = now();
        }

        $message->update($updateData);

        return response()->json([
            'success' => true,
            'message' => 'Status updated successfully',
            'data' => [
                'message' => $message->fresh()
            ]
        ]);
    }

    /**
     * Reply to contact message
     */
    public function reply(Request $request, int $contactId): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reply_message' => 'required|string|max:5000',
            'send_email' => 'boolean',
            'update_status' => 'nullable|in:in_progress,resolved,closed'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $message = ContactMessage::find($contactId);

        if (!$message) {
            return response()->json([
                'success' => false,
                'message' => 'Contact message not found'
            ], 404);
        }

        // Update message with reply
        $updateData = [
            'reply_message' => $request->reply_message,
            'replied_by' => $request->user()->id,
            'replied_at' => now()
        ];

        if ($request->update_status) {
            $updateData['status'] = $request->update_status;
            
            if ($request->update_status === 'resolved') {
                $updateData['resolved_at'] = now();
            } elseif ($request->update_status === 'closed') {
                $updateData['closed_at'] = now();
            }
        }

        $message->update($updateData);

        // Send email reply if requested
        if ($request->boolean('send_email', true)) {
            try {
                Mail::to($message->email)->send(new ContactMessageReply($message, $request->user()));
            } catch (\Exception $e) {
                \Log::error('Failed to send contact reply email: ' . $e->getMessage());
                
                return response()->json([
                    'success' => true,
                    'message' => 'Reply saved but email could not be sent',
                    'data' => [
                        'message' => $message->fresh()
                    ]
                ]);
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Reply sent successfully',
            'data' => [
                'message' => $message->fresh()
            ]
        ]);
    }

    /**
     * Delete contact message (admin only)
     */
    public function destroy(Request $request, int $contactId): JsonResponse
    {
        $message = ContactMessage::find($contactId);

        if (!$message) {
            return response()->json([
                'success' => false,
                'message' => 'Contact message not found'
            ], 404);
        }

        // Delete attachments from storage
        if ($message->attachments) {
            $attachments = json_decode($message->attachments, true);
            foreach ($attachments as $attachment) {
                if (Storage::disk('private')->exists($attachment['path'])) {
                    Storage::disk('private')->delete($attachment['path']);
                }
            }
        }

        $message->delete();

        return response()->json([
            'success' => true,
            'message' => 'Contact message deleted successfully'
        ]);
    }

    /**
     * Bulk update contact message status
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'message_ids' => 'required|array',
            'message_ids.*' => 'integer|exists:contact_messages,id',
            'status' => 'required|in:new,in_progress,resolved,closed',
            'notes' => 'nullable|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $updateData = [
            'status' => $request->status,
            'updated_by' => $request->user()->id,
            'updated_at' => now()
        ];

        if ($request->notes) {
            $updateData['internal_notes'] = $request->notes;
        }

        if ($request->status === 'resolved') {
            $updateData['resolved_at'] = now();
        } elseif ($request->status === 'closed') {
            $updateData['closed_at'] = now();
        }

        $updatedCount = ContactMessage::whereIn('id', $request->message_ids)
            ->update($updateData);

        return response()->json([
            'success' => true,
            'message' => "Successfully updated {$updatedCount} contact messages",
            'data' => [
                'updated_count' => $updatedCount
            ]
        ]);
    }
}

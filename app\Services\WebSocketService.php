<?php

namespace App\Services;

use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use App\Models\ChatSession;
use App\Models\User;

class WebSocketService
{
    /**
     * Broadcast message to specific session
     */
    public function broadcastToSession($sessionId, $message)
    {
        try {
            $channel = "session:{$sessionId}";
            Redis::publish($channel, json_encode($message));
            
            Log::info("Message broadcasted to session", [
                'session_id' => $sessionId,
                'message_type' => $message['type'] ?? 'unknown'
            ]);
            
        } catch (\Exception $e) {
            Log::error("Failed to broadcast message to session", [
                'session_id' => $sessionId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Broadcast message to all agents of a site
     */
    public function broadcastToSiteAgents($siteId, $message)
    {
        try {
            $channel = "site:{$siteId}:agents";
            Redis::publish($channel, json_encode($message));
            
            Log::info("Message broadcasted to site agents", [
                'site_id' => $siteId,
                'message_type' => $message['type'] ?? 'unknown'
            ]);
            
        } catch (\Exception $e) {
            Log::error("Failed to broadcast message to site agents", [
                'site_id' => $siteId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Broadcast message to specific user
     */
    public function broadcastToUser($userId, $message)
    {
        try {
            $channel = "user:{$userId}";
            Redis::publish($channel, json_encode($message));
            
            Log::info("Message broadcasted to user", [
                'user_id' => $userId,
                'message_type' => $message['type'] ?? 'unknown'
            ]);
            
        } catch (\Exception $e) {
            Log::error("Failed to broadcast message to user", [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get online agents for a site
     */
    public function getOnlineAgents($siteId)
    {
        try {
            $agentStatuses = Redis::hgetall("agent_status:{$siteId}");
            $onlineAgents = [];
            
            foreach ($agentStatuses as $userId => $statusJson) {
                $status = json_decode($statusJson, true);
                if ($status && $status['status'] === 'online') {
                    $onlineAgents[] = [
                        'user_id' => $userId,
                        'status' => $status['status'],
                        'last_seen' => $status['last_seen'],
                        'connection_id' => $status['connection_id'] ?? null
                    ];
                }
            }
            
            return $onlineAgents;
            
        } catch (\Exception $e) {
            Log::error("Failed to get online agents", [
                'site_id' => $siteId,
                'error' => $e->getMessage()
            ]);
            
            return [];
        }
    }

    /**
     * Update agent status
     */
    public function updateAgentStatus($siteId, $userId, $status, $connectionId = null)
    {
        try {
            $statusData = [
                'status' => $status,
                'last_seen' => now()->toISOString(),
                'connection_id' => $connectionId
            ];
            
            Redis::hset("agent_status:{$siteId}", $userId, json_encode($statusData));
            
            // Broadcast status change to other agents
            $this->broadcastToSiteAgents($siteId, [
                'type' => 'agent_status_changed',
                'user_id' => $userId,
                'status' => $status,
                'timestamp' => now()->toISOString()
            ]);
            
            Log::info("Agent status updated", [
                'site_id' => $siteId,
                'user_id' => $userId,
                'status' => $status
            ]);
            
        } catch (\Exception $e) {
            Log::error("Failed to update agent status", [
                'site_id' => $siteId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Notify about new chat session
     */
    public function notifyNewChatSession($session)
    {
        try {
            $message = [
                'type' => 'new_chat_session',
                'session_id' => $session->session_id,
                'visitor_id' => $session->visitor_id,
                'site_id' => $session->site_id,
                'initial_message' => $session->initial_message,
                'started_at' => $session->started_at->toISOString(),
                'visitor_info' => [
                    'name' => $session->visitor->name ?? 'Anonymous',
                    'email' => $session->visitor->email,
                    'location' => $session->visitor->city . ', ' . $session->visitor->country,
                    'current_page' => $session->initial_page_url
                ]
            ];
            
            $this->broadcastToSiteAgents($session->site_id, $message);
            
        } catch (\Exception $e) {
            Log::error("Failed to notify about new chat session", [
                'session_id' => $session->session_id ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Notify about session assignment
     */
    public function notifySessionAssignment($session, $agent)
    {
        try {
            // Notify the visitor
            $this->broadcastToSession($session->session_id, [
                'type' => 'agent_assigned',
                'session_id' => $session->session_id,
                'agent' => [
                    'id' => $agent->id,
                    'name' => $agent->name,
                    'avatar' => $agent->avatar
                ],
                'timestamp' => now()->toISOString()
            ]);
            
            // Notify other agents
            $this->broadcastToSiteAgents($session->site_id, [
                'type' => 'session_assigned',
                'session_id' => $session->session_id,
                'agent_id' => $agent->id,
                'agent_name' => $agent->name,
                'timestamp' => now()->toISOString()
            ]);
            
        } catch (\Exception $e) {
            Log::error("Failed to notify about session assignment", [
                'session_id' => $session->session_id ?? 'unknown',
                'agent_id' => $agent->id ?? 'unknown',
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Notify about typing status
     */
    public function notifyTypingStatus($sessionId, $userId, $isTyping, $senderType = 'agent')
    {
        try {
            $message = [
                'type' => $isTyping ? 'typing_start' : 'typing_stop',
                'session_id' => $sessionId,
                'user_id' => $userId,
                'sender_type' => $senderType,
                'timestamp' => now()->toISOString()
            ];
            
            $this->broadcastToSession($sessionId, $message);
            
        } catch (\Exception $e) {
            Log::error("Failed to notify about typing status", [
                'session_id' => $sessionId,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Clean up disconnected agents
     */
    public function cleanupDisconnectedAgents($siteId)
    {
        try {
            $agentStatuses = Redis::hgetall("agent_status:{$siteId}");
            $cutoffTime = now()->subMinutes(5); // Consider agents offline after 5 minutes

            foreach ($agentStatuses as $userId => $statusJson) {
                $status = json_decode($statusJson, true);
                if ($status && isset($status['last_seen'])) {
                    $lastSeen = \Carbon\Carbon::parse($status['last_seen']);
                    if ($lastSeen->lt($cutoffTime)) {
                        Redis::hdel("agent_status:{$siteId}", $userId);

                        // Notify about agent going offline
                        $this->broadcastToSiteAgents($siteId, [
                            'type' => 'agent_status_changed',
                            'user_id' => $userId,
                            'status' => 'offline',
                            'timestamp' => now()->toISOString()
                        ]);
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error("Failed to cleanup disconnected agents", [
                'site_id' => $siteId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Notify about agent status change
     */
    public function notifyAgentStatusChange($agent, $status)
    {
        try {
            $this->broadcastToUser($agent->id, [
                'type' => 'status_changed',
                'status' => $status,
                'timestamp' => now()->toISOString(),
            ]);

            // Notify supervisors and admins
            $siteIds = $agent->sites->pluck('id')->toArray();
            foreach ($siteIds as $siteId) {
                $this->broadcastToSiteAgents($siteId, [
                    'type' => 'agent_status_changed',
                    'agent' => [
                        'id' => $agent->id,
                        'name' => $agent->name,
                        'status' => $status,
                    ],
                ]);
            }

        } catch (\Exception $e) {
            Log::error("Failed to notify about agent status change", [
                'agent_id' => $agent->id,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Notify about new contact message
     */
    public function notifyNewContactMessage($contactMessage, $agent = null)
    {
        try {
            $data = [
                'type' => 'new_contact_message',
                'message' => $contactMessage->toApiArray(),
            ];

            if ($agent) {
                $this->broadcastToUser($agent->id, $data);
            } else {
                // Notify all online agents for the site
                $this->broadcastToSiteAgents($contactMessage->site_id, $data);
            }

        } catch (\Exception $e) {
            Log::error("Failed to notify about new contact message", [
                'message_id' => $contactMessage->message_id,
                'agent_id' => $agent ? $agent->id : null,
                'error' => $e->getMessage()
            ]);
        }
    }
}

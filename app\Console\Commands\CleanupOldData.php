<?php

namespace App\Console\Commands;

use App\Models\VisitorBehavior;
use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Models\ContactMessage;
use App\Models\WebhookLog;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class CleanupOldData extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'cleanup:old-data 
                            {--days=90 : Number of days to keep data}
                            {--type=all : Type of data to cleanup (all, behaviors, sessions, messages, contacts, webhooks, files)}
                            {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     */
    protected $description = 'Clean up old data to maintain database performance and comply with data retention policies';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $days = (int) $this->option('days');
        $type = $this->option('type');
        $dryRun = $this->option('dry-run');
        
        $cutoffDate = Carbon::now()->subDays($days);
        
        $this->info("Starting data cleanup for data older than {$days} days (before {$cutoffDate->toDateString()})");
        
        if ($dryRun) {
            $this->warn('DRY RUN MODE - No data will actually be deleted');
        }

        $totalDeleted = 0;

        if ($type === 'all' || $type === 'behaviors') {
            $totalDeleted += $this->cleanupVisitorBehaviors($cutoffDate, $dryRun);
        }

        if ($type === 'all' || $type === 'sessions') {
            $totalDeleted += $this->cleanupOldSessions($cutoffDate, $dryRun);
        }

        if ($type === 'all' || $type === 'messages') {
            $totalDeleted += $this->cleanupOrphanedMessages($dryRun);
        }

        if ($type === 'all' || $type === 'contacts') {
            $totalDeleted += $this->cleanupOldContacts($cutoffDate, $dryRun);
        }

        if ($type === 'all' || $type === 'webhooks') {
            $totalDeleted += $this->cleanupWebhookLogs($cutoffDate, $dryRun);
        }

        if ($type === 'all' || $type === 'files') {
            $totalDeleted += $this->cleanupOldFiles($cutoffDate, $dryRun);
        }

        $this->info("Cleanup completed. Total records processed: {$totalDeleted}");
        
        if (!$dryRun) {
            $this->optimizeTables();
        }
    }

    /**
     * Clean up old visitor behaviors
     */
    protected function cleanupVisitorBehaviors(Carbon $cutoffDate, bool $dryRun): int
    {
        $this->info('Cleaning up visitor behaviors...');
        
        $query = VisitorBehavior::where('created_at', '<', $cutoffDate);
        $count = $query->count();
        
        $this->line("Found {$count} visitor behavior records to delete");
        
        if (!$dryRun && $count > 0) {
            // Delete in chunks to avoid memory issues
            $deleted = 0;
            $chunkSize = 1000;
            
            while ($deleted < $count) {
                $chunkDeleted = VisitorBehavior::where('created_at', '<', $cutoffDate)
                    ->limit($chunkSize)
                    ->delete();
                    
                $deleted += $chunkDeleted;
                $this->line("Deleted {$deleted}/{$count} visitor behavior records");
                
                if ($chunkDeleted === 0) {
                    break;
                }
            }
        }
        
        return $count;
    }

    /**
     * Clean up old chat sessions
     */
    protected function cleanupOldSessions(Carbon $cutoffDate, bool $dryRun): int
    {
        $this->info('Cleaning up old chat sessions...');
        
        $query = ChatSession::where('started_at', '<', $cutoffDate)
            ->where('status', 'ended');
        $count = $query->count();
        
        $this->line("Found {$count} ended chat sessions to delete");
        
        if (!$dryRun && $count > 0) {
            // First delete associated messages
            $sessionIds = $query->pluck('id');
            $messageCount = ChatMessage::whereIn('session_id', $sessionIds)->count();
            $this->line("Deleting {$messageCount} associated messages...");
            
            ChatMessage::whereIn('session_id', $sessionIds)->delete();
            
            // Then delete sessions
            $query->delete();
        }
        
        return $count;
    }

    /**
     * Clean up orphaned messages
     */
    protected function cleanupOrphanedMessages(bool $dryRun): int
    {
        $this->info('Cleaning up orphaned messages...');
        
        $count = ChatMessage::whereNotExists(function ($query) {
            $query->select(DB::raw(1))
                  ->from('chat_sessions')
                  ->whereRaw('chat_sessions.id = chat_messages.session_id');
        })->count();
        
        $this->line("Found {$count} orphaned messages to delete");
        
        if (!$dryRun && $count > 0) {
            ChatMessage::whereNotExists(function ($query) {
                $query->select(DB::raw(1))
                      ->from('chat_sessions')
                      ->whereRaw('chat_sessions.id = chat_messages.session_id');
            })->delete();
        }
        
        return $count;
    }

    /**
     * Clean up old contact messages
     */
    protected function cleanupOldContacts(Carbon $cutoffDate, bool $dryRun): int
    {
        $this->info('Cleaning up old contact messages...');
        
        $query = ContactMessage::where('submitted_at', '<', $cutoffDate)
            ->where('status', 'closed');
        $count = $query->count();
        
        $this->line("Found {$count} closed contact messages to delete");
        
        if (!$dryRun && $count > 0) {
            // Delete associated files first
            $messages = $query->whereNotNull('attachments')->get();
            foreach ($messages as $message) {
                $this->deleteContactAttachments($message);
            }
            
            $query->delete();
        }
        
        return $count;
    }

    /**
     * Clean up webhook logs
     */
    protected function cleanupWebhookLogs(Carbon $cutoffDate, bool $dryRun): int
    {
        $this->info('Cleaning up webhook logs...');
        
        $query = WebhookLog::where('processed_at', '<', $cutoffDate);
        $count = $query->count();
        
        $this->line("Found {$count} webhook logs to delete");
        
        if (!$dryRun && $count > 0) {
            $query->delete();
        }
        
        return $count;
    }

    /**
     * Clean up old files
     */
    protected function cleanupOldFiles(Carbon $cutoffDate, bool $dryRun): int
    {
        $this->info('Cleaning up old files...');
        
        $deletedCount = 0;
        $folders = ['chat_files', 'contact_attachments', 'temp'];
        
        foreach ($folders as $folder) {
            if (!Storage::disk('public')->exists($folder)) {
                continue;
            }
            
            $files = Storage::disk('public')->allFiles($folder);
            
            foreach ($files as $file) {
                $lastModified = Storage::disk('public')->lastModified($file);
                
                if ($lastModified < $cutoffDate->timestamp) {
                    $this->line("Would delete: {$file}");
                    
                    if (!$dryRun) {
                        Storage::disk('public')->delete($file);
                    }
                    
                    $deletedCount++;
                }
            }
        }
        
        $this->line("Found {$deletedCount} old files to delete");
        
        return $deletedCount;
    }

    /**
     * Delete contact message attachments
     */
    protected function deleteContactAttachments(ContactMessage $message): void
    {
        if (!$message->attachments) {
            return;
        }
        
        $attachments = json_decode($message->attachments, true);
        
        foreach ($attachments as $attachment) {
            if (isset($attachment['path']) && Storage::disk('private')->exists($attachment['path'])) {
                Storage::disk('private')->delete($attachment['path']);
                $this->line("Deleted attachment: {$attachment['path']}");
            }
        }
    }

    /**
     * Optimize database tables after cleanup
     */
    protected function optimizeTables(): void
    {
        $this->info('Optimizing database tables...');
        
        $tables = [
            'visitor_behaviors',
            'chat_sessions',
            'chat_messages',
            'contact_messages',
            'webhook_logs'
        ];
        
        foreach ($tables as $table) {
            try {
                DB::statement("OPTIMIZE TABLE {$table}");
                $this->line("Optimized table: {$table}");
            } catch (\Exception $e) {
                $this->warn("Failed to optimize table {$table}: " . $e->getMessage());
            }
        }
    }
}

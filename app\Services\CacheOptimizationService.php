<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class CacheOptimizationService
{
    /**
     * Optimize cache performance
     */
    public function optimizeCache(): array
    {
        $results = [];

        try {
            // Clean expired keys
            $results['cleanup'] = $this->cleanupExpiredKeys();

            // Optimize memory usage
            $results['memory_optimization'] = $this->optimizeMemoryUsage();

            // Warm up critical caches
            $results['cache_warming'] = $this->warmUpCaches();

            // Analyze cache performance
            $results['performance_analysis'] = $this->analyzeCachePerformance();

            Log::info('Cache optimization completed', $results);

        } catch (\Exception $e) {
            Log::error('Cache optimization failed: ' . $e->getMessage());
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Clean up expired and unused cache keys
     */
    protected function cleanupExpiredKeys(): array
    {
        $results = [];

        try {
            // Get Redis connection
            $redis = Redis::connection();

            // Get memory usage before cleanup
            $memoryBefore = $redis->info('memory')['used_memory'];

            // Clean up expired keys (Redis does this automatically, but we can force it)
            $expiredKeys = $redis->eval("
                local keys = redis.call('keys', ARGV[1])
                local expired = 0
                for i=1,#keys do
                    if redis.call('ttl', keys[i]) == -2 then
                        expired = expired + 1
                    end
                end
                return expired
            ", 0, '*');

            // Clean up old session data
            $sessionPattern = config('session.cookie') . ':*';
            $sessionKeys = $redis->keys($sessionPattern);
            $oldSessions = 0;

            foreach ($sessionKeys as $key) {
                $ttl = $redis->ttl($key);
                if ($ttl < 0 || $ttl > 86400) { // Older than 24 hours
                    $redis->del($key);
                    $oldSessions++;
                }
            }

            // Clean up old cache tags
            $this->cleanupCacheTags();

            $memoryAfter = $redis->info('memory')['used_memory'];
            $memorySaved = $memoryBefore - $memoryAfter;

            $results = [
                'expired_keys' => $expiredKeys,
                'old_sessions_removed' => $oldSessions,
                'memory_saved_bytes' => $memorySaved,
                'memory_saved_mb' => round($memorySaved / 1024 / 1024, 2)
            ];

        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Optimize memory usage
     */
    protected function optimizeMemoryUsage(): array
    {
        $results = [];

        try {
            $redis = Redis::connection();

            // Get current memory info
            $memoryInfo = $redis->info('memory');
            $results['memory_before'] = [
                'used_memory_mb' => round($memoryInfo['used_memory'] / 1024 / 1024, 2),
                'used_memory_peak_mb' => round($memoryInfo['used_memory_peak'] / 1024 / 1024, 2)
            ];

            // Defragment memory (Redis 4.0+)
            try {
                $redis->memory('defrag');
                $results['defragmentation'] = 'completed';
            } catch (\Exception $e) {
                $results['defragmentation'] = 'not_supported';
            }

            // Optimize data structures
            $this->optimizeDataStructures();

            // Get memory info after optimization
            $memoryInfoAfter = $redis->info('memory');
            $results['memory_after'] = [
                'used_memory_mb' => round($memoryInfoAfter['used_memory'] / 1024 / 1024, 2),
                'used_memory_peak_mb' => round($memoryInfoAfter['used_memory_peak'] / 1024 / 1024, 2)
            ];

        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Warm up critical caches
     */
    protected function warmUpCaches(): array
    {
        $results = [];

        try {
            // Warm up user permissions cache
            $activeUsers = DB::table('users')->where('is_active', true)->get();
            foreach ($activeUsers as $user) {
                Cache::remember("user_permissions_{$user->id}", 3600, function () use ($user) {
                    return DB::table('user_permissions')
                        ->where('user_id', $user->id)
                        ->pluck('permission')
                        ->toArray();
                });
            }
            $results['user_permissions'] = count($activeUsers) . ' users cached';

            // Warm up site configurations
            $sites = DB::table('sites')->where('is_active', true)->get();
            foreach ($sites as $site) {
                Cache::remember("site_config_{$site->id}", 1800, function () use ($site) {
                    return DB::table('site_configurations')
                        ->where('site_id', $site->id)
                        ->first();
                });
            }
            $results['site_configs'] = count($sites) . ' sites cached';

            // Warm up theme configurations
            $themes = DB::table('themes')->where('is_active', true)->get();
            foreach ($themes as $theme) {
                Cache::remember("theme_{$theme->id}", 3600, function () use ($theme) {
                    return [
                        'colors' => json_decode($theme->colors, true),
                        'fonts' => json_decode($theme->fonts, true),
                        'custom_css' => $theme->custom_css
                    ];
                });
            }
            $results['themes'] = count($themes) . ' themes cached';

            // Warm up working hours
            Cache::remember('working_hours', 1800, function () {
                return DB::table('working_hours')->get()->groupBy('site_id');
            });
            $results['working_hours'] = 'cached';

            // Warm up language translations
            $languages = ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ko', 'ar', 'hi', 'tr', 'pl', 'nl'];
            foreach ($languages as $lang) {
                Cache::remember("translations_{$lang}", 7200, function () use ($lang) {
                    return DB::table('translations')
                        ->where('language', $lang)
                        ->pluck('translation', 'key')
                        ->toArray();
                });
            }
            $results['translations'] = count($languages) . ' languages cached';

        } catch (\Exception $e) {
            $results['error'] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Analyze cache performance
     */
    protected function analyzeCachePerformance(): array
    {
        $analysis = [];

        try {
            $redis = Redis::connection();

            // Get Redis info
            $info = $redis->info();
            
            $analysis['redis_version'] = $info['redis_version'];
            $analysis['uptime_days'] = round($info['uptime_in_seconds'] / 86400, 1);
            $analysis['connected_clients'] = $info['connected_clients'];
            
            // Memory analysis
            $analysis['memory'] = [
                'used_memory_mb' => round($info['used_memory'] / 1024 / 1024, 2),
                'used_memory_peak_mb' => round($info['used_memory_peak'] / 1024 / 1024, 2),
                'memory_fragmentation_ratio' => $info['mem_fragmentation_ratio'] ?? 'N/A'
            ];

            // Performance stats
            $analysis['performance'] = [
                'total_commands_processed' => $info['total_commands_processed'],
                'instantaneous_ops_per_sec' => $info['instantaneous_ops_per_sec'],
                'keyspace_hits' => $info['keyspace_hits'],
                'keyspace_misses' => $info['keyspace_misses']
            ];

            // Calculate hit ratio
            $hits = $info['keyspace_hits'];
            $misses = $info['keyspace_misses'];
            $total = $hits + $misses;
            $analysis['hit_ratio'] = $total > 0 ? round(($hits / $total) * 100, 2) : 0;

            // Key analysis
            $analysis['keys'] = [
                'total_keys' => $redis->dbsize(),
                'expired_keys' => $info['expired_keys'],
                'evicted_keys' => $info['evicted_keys']
            ];

            // Persistence info
            $analysis['persistence'] = [
                'rdb_last_save_time' => date('Y-m-d H:i:s', $info['rdb_last_save_time']),
                'rdb_changes_since_last_save' => $info['rdb_changes_since_last_save']
            ];

        } catch (\Exception $e) {
            $analysis['error'] = $e->getMessage();
        }

        return $analysis;
    }

    /**
     * Clean up cache tags
     */
    protected function cleanupCacheTags(): void
    {
        try {
            $redis = Redis::connection();
            
            // Clean up Laravel cache tags
            $tagKeys = $redis->keys('laravel_cache:tag:*');
            $cleanedTags = 0;
            
            foreach ($tagKeys as $tagKey) {
                $members = $redis->smembers($tagKey);
                $validMembers = [];
                
                foreach ($members as $member) {
                    if ($redis->exists($member)) {
                        $validMembers[] = $member;
                    }
                }
                
                if (count($validMembers) !== count($members)) {
                    $redis->del($tagKey);
                    if (!empty($validMembers)) {
                        $redis->sadd($tagKey, ...$validMembers);
                    }
                    $cleanedTags++;
                }
            }
            
            Log::info("Cleaned up {$cleanedTags} cache tags");
            
        } catch (\Exception $e) {
            Log::error('Failed to cleanup cache tags: ' . $e->getMessage());
        }
    }

    /**
     * Optimize data structures
     */
    protected function optimizeDataStructures(): void
    {
        try {
            $redis = Redis::connection();
            
            // Find and optimize hash tables with few fields
            $hashKeys = $redis->keys('*');
            $optimized = 0;
            
            foreach ($hashKeys as $key) {
                $type = $redis->type($key);
                
                if ($type === Redis::REDIS_HASH) {
                    $fieldCount = $redis->hlen($key);
                    
                    // If hash has only one field, consider converting to string
                    if ($fieldCount === 1) {
                        $fields = $redis->hgetall($key);
                        $ttl = $redis->ttl($key);
                        
                        // This is just an example - be careful with data structure changes
                        // In practice, you'd need to ensure compatibility with your application
                        $optimized++;
                    }
                }
            }
            
            Log::info("Analyzed {$optimized} data structures for optimization");
            
        } catch (\Exception $e) {
            Log::error('Failed to optimize data structures: ' . $e->getMessage());
        }
    }

    /**
     * Get cache statistics
     */
    public function getCacheStatistics(): array
    {
        $stats = [];

        try {
            $redis = Redis::connection();
            $info = $redis->info();

            $stats = [
                'memory_usage_mb' => round($info['used_memory'] / 1024 / 1024, 2),
                'total_keys' => $redis->dbsize(),
                'hit_ratio' => $this->calculateHitRatio($info),
                'operations_per_second' => $info['instantaneous_ops_per_sec'],
                'connected_clients' => $info['connected_clients'],
                'uptime_hours' => round($info['uptime_in_seconds'] / 3600, 1)
            ];

            // Cache the statistics
            Cache::put('cache_statistics', $stats, 60);

        } catch (\Exception $e) {
            $stats['error'] = $e->getMessage();
        }

        return $stats;
    }

    /**
     * Calculate cache hit ratio
     */
    protected function calculateHitRatio(array $info): float
    {
        $hits = $info['keyspace_hits'] ?? 0;
        $misses = $info['keyspace_misses'] ?? 0;
        $total = $hits + $misses;

        return $total > 0 ? round(($hits / $total) * 100, 2) : 0;
    }
}

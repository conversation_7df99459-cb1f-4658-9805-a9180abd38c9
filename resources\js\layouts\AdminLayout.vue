<template>
  <div class="min-h-screen bg-gray-50 flex">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0"
         :class="{ '-translate-x-full': !sidebarOpen, 'translate-x-0': sidebarOpen }">
      
      <!-- Logo -->
      <div class="flex items-center justify-center h-16 px-4 bg-blue-600">
        <h1 class="text-xl font-bold text-white">Customer Service</h1>
      </div>

      <!-- Navigation -->
      <nav class="mt-8">
        <div class="px-4 space-y-2">
          <router-link
            v-for="item in navigationItems"
            :key="item.name"
            :to="item.to"
            class="flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200"
            :class="isActiveRoute(item.to) 
              ? 'bg-blue-100 text-blue-700 border-r-2 border-blue-700' 
              : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'"
            @click="closeSidebar"
          >
            <component :is="item.icon" class="mr-3 h-5 w-5" />
            {{ item.label }}
            <span v-if="item.badge" class="ml-auto inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
              {{ item.badge }}
            </span>
          </router-link>
        </div>
      </nav>

      <!-- User info at bottom -->
      <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
        <div class="flex items-center">
          <img :src="userAvatar" :alt="userName" class="h-8 w-8 rounded-full">
          <div class="ml-3 flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 truncate">{{ userName }}</p>
            <p class="text-xs text-gray-500 truncate">{{ userRole }}</p>
          </div>
          <button @click="showUserMenu = !showUserMenu" class="ml-2 p-1 rounded-full hover:bg-gray-100">
            <ChevronUpIcon class="h-4 w-4 text-gray-400" />
          </button>
        </div>
        
        <!-- User menu -->
        <div v-if="showUserMenu" class="mt-2 py-1 bg-white rounded-md shadow-lg border">
          <button @click="showStatusModal = true" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
            Change Status
          </button>
          <button @click="showProfileModal = true" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
            Profile Settings
          </button>
          <hr class="my-1">
          <button @click="handleLogout" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50">
            Logout
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile sidebar overlay -->
    <div v-if="sidebarOpen" class="fixed inset-0 z-40 lg:hidden">
      <div class="absolute inset-0 bg-gray-600 opacity-75" @click="closeSidebar"></div>
    </div>

    <!-- Main content -->
    <div class="flex-1 flex flex-col lg:pl-0">
      <!-- Top bar -->
      <div class="sticky top-0 z-10 flex-shrink-0 flex h-16 bg-white shadow-sm border-b border-gray-200">
        <button @click="toggleSidebar" class="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 lg:hidden">
          <Bars3Icon class="h-6 w-6" />
        </button>
        
        <div class="flex-1 px-4 flex justify-between items-center">
          <div class="flex-1 flex">
            <h1 class="text-2xl font-semibold text-gray-900">{{ pageTitle }}</h1>
          </div>
          
          <div class="ml-4 flex items-center md:ml-6 space-x-4">
            <!-- Connection status -->
            <div class="flex items-center space-x-2">
              <div class="h-2 w-2 rounded-full" :class="isConnected ? 'bg-green-400' : 'bg-red-400'"></div>
              <span class="text-sm text-gray-500">{{ isConnected ? 'Connected' : 'Disconnected' }}</span>
            </div>

            <!-- Notifications -->
            <button @click="showNotifications = !showNotifications" class="relative p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <BellIcon class="h-6 w-6" />
              <span v-if="unreadNotifications > 0" class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                {{ unreadNotifications > 9 ? '9+' : unreadNotifications }}
              </span>
            </button>
          </div>
        </div>
      </div>

      <!-- Page content -->
      <main class="flex-1 relative overflow-y-auto focus:outline-none">
        <div class="py-6">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
            <router-view />
          </div>
        </div>
      </main>
    </div>

    <!-- Status Modal -->
    <StatusModal v-if="showStatusModal" @close="showStatusModal = false" />
    
    <!-- Profile Modal -->
    <ProfileModal v-if="showProfileModal" @close="showProfileModal = false" />
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth.js'
import { useChatStore } from '../stores/chat.js'
import { useNotificationStore } from '../stores/notifications.js'

// Icons
import { 
  Bars3Icon, 
  BellIcon, 
  ChevronUpIcon,
  HomeIcon,
  ChatBubbleLeftRightIcon,
  ChartBarIcon,
  CogIcon,
  UsersIcon,
  ClockIcon,
  EyeIcon,
  PaintBrushIcon,
  LanguageIcon
} from '@heroicons/vue/24/outline'

// Components
import StatusModal from '../components/modals/StatusModal.vue'
import ProfileModal from '../components/modals/ProfileModal.vue'

export default {
  name: 'AdminLayout',
  components: {
    StatusModal,
    ProfileModal,
    Bars3Icon,
    BellIcon,
    ChevronUpIcon
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const authStore = useAuthStore()
    const chatStore = useChatStore()
    const notificationStore = useNotificationStore()

    // State
    const sidebarOpen = ref(false)
    const showUserMenu = ref(false)
    const showStatusModal = ref(false)
    const showProfileModal = ref(false)
    const showNotifications = ref(false)

    // Computed
    const userName = computed(() => authStore.userName)
    const userRole = computed(() => authStore.userRole)
    const userAvatar = computed(() => authStore.userAvatar)
    const isConnected = computed(() => authStore.isConnected)
    const pageTitle = computed(() => route.meta.title || 'Dashboard')
    const unreadNotifications = computed(() => notificationStore.notifications.length)

    // Navigation items based on user role
    const navigationItems = computed(() => {
      const items = [
        {
          name: 'dashboard',
          label: 'Dashboard',
          to: '/admin/dashboard',
          icon: HomeIcon,
          roles: ['admin', 'supervisor', 'agent']
        }
      ]

      if (['agent', 'supervisor', 'admin'].includes(userRole.value)) {
        items.push({
          name: 'workspace',
          label: 'Agent Workspace',
          to: '/admin/workspace',
          icon: ChatBubbleLeftRightIcon,
          badge: chatStore.totalWaitingSessions > 0 ? chatStore.totalWaitingSessions : null,
          roles: ['agent', 'supervisor', 'admin']
        })
      }

      if (['supervisor', 'admin'].includes(userRole.value)) {
        items.push({
          name: 'analytics',
          label: 'Analytics',
          to: '/admin/analytics',
          icon: ChartBarIcon,
          roles: ['supervisor', 'admin']
        })
      }

      if (['admin', 'supervisor'].includes(userRole.value)) {
        items.push({
          name: 'users',
          label: 'User Management',
          to: '/admin/users',
          icon: UsersIcon,
          roles: ['admin', 'supervisor']
        })
      }

      items.push({
        name: 'chat-history',
        label: 'Chat History',
        to: '/admin/chat-history',
        icon: ClockIcon,
        roles: ['admin', 'supervisor', 'agent']
      })

      if (['supervisor', 'admin'].includes(userRole.value)) {
        items.push({
          name: 'visitor-tracking',
          label: 'Visitor Tracking',
          to: '/admin/visitor-tracking',
          icon: EyeIcon,
          roles: ['supervisor', 'admin']
        })
      }

      if (userRole.value === 'admin') {
        items.push(
          {
            name: 'config',
            label: 'System Config',
            to: '/admin/config',
            icon: CogIcon,
            roles: ['admin']
          },
          {
            name: 'themes',
            label: 'Themes',
            to: '/admin/themes',
            icon: PaintBrushIcon,
            roles: ['admin']
          },
          {
            name: 'languages',
            label: 'Languages',
            to: '/admin/languages',
            icon: LanguageIcon,
            roles: ['admin']
          }
        )
      }

      return items.filter(item => item.roles.includes(userRole.value))
    })

    // Methods
    const toggleSidebar = () => {
      sidebarOpen.value = !sidebarOpen.value
    }

    const closeSidebar = () => {
      sidebarOpen.value = false
      showUserMenu.value = false
    }

    const isActiveRoute = (routePath) => {
      return route.path === routePath || route.path.startsWith(routePath + '/')
    }

    const handleLogout = async () => {
      try {
        await authStore.logout()
        router.push('/login')
      } catch (error) {
        console.error('Logout error:', error)
      }
    }

    // Close dropdowns when clicking outside
    const handleClickOutside = (event) => {
      if (!event.target.closest('.user-menu')) {
        showUserMenu.value = false
      }
      if (!event.target.closest('.notifications-menu')) {
        showNotifications.value = false
      }
    }

    // Lifecycle
    onMounted(() => {
      document.addEventListener('click', handleClickOutside)
      
      // Load initial data
      if (userRole.value === 'agent' || userRole.value === 'supervisor' || userRole.value === 'admin') {
        chatStore.fetchWorkspaceOverview()
      }
    })

    onUnmounted(() => {
      document.removeEventListener('click', handleClickOutside)
    })

    return {
      // State
      sidebarOpen,
      showUserMenu,
      showStatusModal,
      showProfileModal,
      showNotifications,
      
      // Computed
      userName,
      userRole,
      userAvatar,
      isConnected,
      pageTitle,
      unreadNotifications,
      navigationItems,
      
      // Methods
      toggleSidebar,
      closeSidebar,
      isActiveRoute,
      handleLogout
    }
  }
}
</script>

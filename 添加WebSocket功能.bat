@echo off
chcp 65001 >nul
title 添加WebSocket功能
color 0B

echo.
echo ========================================
echo    添加WebSocket功能脚本
echo ========================================
echo.

set PROJECT_DIR=D:\wwwroot\aichat.jagship.com
set PHP_PATH=D:\BtSoft\php\72\php.exe

if not exist "%PROJECT_DIR%" (
    echo ❌ 项目目录不存在，请先运行基础部署脚本
    pause
    exit /b 1
)

cd /d "%PROJECT_DIR%"

echo [1/4] 安装WebSocket相关依赖...
composer require ratchet/pawl textalk/websocket-client --no-interaction
if %errorlevel% neq 0 (
    echo ❌ WebSocket依赖安装失败
    pause
    exit /b 1
)
echo ✅ WebSocket依赖安装完成

echo.
echo [2/4] 创建WebSocket服务启动脚本...
echo @echo off > start_websocket.bat
echo cd /d "%PROJECT_DIR%" >> start_websocket.bat
echo "%PHP_PATH%" artisan websocket:serve >> start_websocket.bat
echo pause >> start_websocket.bat
echo ✅ WebSocket启动脚本创建完成

echo.
echo [3/4] 创建队列处理启动脚本...
echo @echo off > start_queue.bat
echo cd /d "%PROJECT_DIR%" >> start_queue.bat
echo "%PHP_PATH%" artisan queue:work --sleep=3 --tries=3 >> start_queue.bat
echo pause >> start_queue.bat
echo ✅ 队列处理启动脚本创建完成

echo.
echo [4/4] 更新环境配置...
powershell -Command "(Get-Content .env) -replace 'BROADCAST_DRIVER=log', 'BROADCAST_DRIVER=redis' | Set-Content .env"
powershell -Command "(Get-Content .env) -replace 'QUEUE_CONNECTION=sync', 'QUEUE_CONNECTION=redis' | Set-Content .env"

:: 添加WebSocket配置
echo. >> .env
echo WEBSOCKET_HOST=127.0.0.1 >> .env
echo WEBSOCKET_PORT=8080 >> .env

echo ✅ 环境配置更新完成

echo.
echo ========================================
echo    🎉 WebSocket功能添加完成！
echo ========================================
echo.
echo 已创建的文件：
echo - start_websocket.bat (启动WebSocket服务)
echo - start_queue.bat (启动队列处理)
echo.
echo 下一步：
echo 1. 双击运行 start_websocket.bat 启动WebSocket服务
echo 2. 双击运行 start_queue.bat 启动队列处理
echo 3. 在宝塔面板防火墙中开放8080端口
echo.
pause

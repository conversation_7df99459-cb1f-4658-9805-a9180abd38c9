<?php

namespace App\Http\Controllers;

use App\Services\ExportService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class ExportController extends Controller
{
    protected $exportService;

    public function __construct(ExportService $exportService)
    {
        $this->exportService = $exportService;
    }

    /**
     * Export chat sessions
     */
    public function exportChatSessions(Request $request, $siteId = null): JsonResponse
    {
        $request->validate([
            'format' => 'required|in:csv,json',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $format = $request->format;
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        try {
            $result = $this->exportService->exportChatSessions($siteId, $startDate, $endDate, $format);

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Chat sessions exported successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export chat sessions: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export chat messages
     */
    public function exportChatMessages(Request $request, $siteId = null): JsonResponse
    {
        $request->validate([
            'format' => 'required|in:csv,json',
            'session_id' => 'nullable|string',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $format = $request->format;
        $sessionId = $request->session_id;
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        try {
            $result = $this->exportService->exportChatMessages($sessionId, $siteId, $startDate, $endDate, $format);

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Chat messages exported successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export chat messages: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export visitor analytics
     */
    public function exportVisitorAnalytics(Request $request, $siteId = null): JsonResponse
    {
        $request->validate([
            'format' => 'required|in:csv,json',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $format = $request->format;
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        try {
            $result = $this->exportService->exportVisitorAnalytics($siteId, $startDate, $endDate, $format);

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Visitor analytics exported successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export visitor analytics: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export comprehensive report
     */
    public function exportComprehensiveReport(Request $request, $siteId = null): JsonResponse
    {
        $request->validate([
            'format' => 'nullable|in:json',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $format = $request->format ?? 'json';
        $startDate = $request->start_date ? Carbon::parse($request->start_date) : null;
        $endDate = $request->end_date ? Carbon::parse($request->end_date) : null;

        try {
            $result = $this->exportService->exportComprehensiveReport($siteId, $startDate, $endDate, $format);

            return response()->json([
                'success' => true,
                'data' => $result,
                'message' => 'Comprehensive report exported successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export comprehensive report: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Download exported file
     */
    public function downloadFile(Request $request): JsonResponse
    {
        $request->validate([
            'file_path' => 'required|string',
        ]);

        $filePath = $request->file_path;

        if (!Storage::exists($filePath)) {
            return response()->json([
                'success' => false,
                'message' => 'File not found',
            ], 404);
        }

        try {
            $fileName = basename($filePath);
            $fileContent = Storage::get($filePath);
            $mimeType = Storage::mimeType($filePath);

            return response()->json([
                'success' => true,
                'data' => [
                    'file_name' => $fileName,
                    'file_content' => base64_encode($fileContent),
                    'mime_type' => $mimeType,
                    'file_size' => Storage::size($filePath),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to download file: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get export history
     */
    public function getExportHistory(): JsonResponse
    {
        try {
            $files = Storage::files('exports');
            
            $exports = collect($files)->map(function ($file) {
                $fileName = basename($file);
                $fileSize = Storage::size($file);
                $lastModified = Carbon::createFromTimestamp(Storage::lastModified($file));
                
                // Parse filename to extract information
                $parts = explode('_', $fileName);
                $type = $parts[0] ?? 'unknown';
                $format = pathinfo($fileName, PATHINFO_EXTENSION);
                
                return [
                    'file_path' => $file,
                    'file_name' => $fileName,
                    'file_size' => $fileSize,
                    'file_size_human' => $this->formatBytes($fileSize),
                    'export_type' => $type,
                    'format' => $format,
                    'created_at' => $lastModified->toISOString(),
                    'download_url' => Storage::url($file),
                ];
            })->sortByDesc('created_at')->values();

            return response()->json([
                'success' => true,
                'data' => $exports,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get export history: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete exported file
     */
    public function deleteExportFile(Request $request): JsonResponse
    {
        $request->validate([
            'file_path' => 'required|string',
        ]);

        $filePath = $request->file_path;

        if (!Storage::exists($filePath)) {
            return response()->json([
                'success' => false,
                'message' => 'File not found',
            ], 404);
        }

        try {
            Storage::delete($filePath);

            return response()->json([
                'success' => true,
                'message' => 'File deleted successfully',
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete file: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Clean up old export files
     */
    public function cleanupOldExports(Request $request): JsonResponse
    {
        $request->validate([
            'days_old' => 'nullable|integer|min:1|max:365',
        ]);

        $daysOld = $request->days_old ?? 7;

        try {
            $deletedCount = $this->exportService->cleanupOldExports($daysOld);

            return response()->json([
                'success' => true,
                'data' => [
                    'deleted_count' => $deletedCount,
                    'days_old' => $daysOld,
                ],
                'message' => "Cleaned up {$deletedCount} old export files",
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cleanup old exports: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get export statistics
     */
    public function getExportStats(): JsonResponse
    {
        try {
            $files = Storage::files('exports');
            
            $totalFiles = count($files);
            $totalSize = collect($files)->sum(function ($file) {
                return Storage::size($file);
            });
            
            $typeStats = collect($files)->groupBy(function ($file) {
                $fileName = basename($file);
                $parts = explode('_', $fileName);
                return $parts[0] ?? 'unknown';
            })->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_size' => $group->sum(function ($file) {
                        return Storage::size($file);
                    }),
                ];
            });

            $formatStats = collect($files)->groupBy(function ($file) {
                return pathinfo($file, PATHINFO_EXTENSION);
            })->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'total_size' => $group->sum(function ($file) {
                        return Storage::size($file);
                    }),
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'total_files' => $totalFiles,
                    'total_size' => $totalSize,
                    'total_size_human' => $this->formatBytes($totalSize),
                    'type_stats' => $typeStats,
                    'format_stats' => $formatStats,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get export statistics: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Format bytes to human readable format
     */
    protected function formatBytes($bytes, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
